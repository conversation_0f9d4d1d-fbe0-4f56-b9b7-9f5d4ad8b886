#!/usr/bin/env python3
"""
Debug individual property cards to understand data structure
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re


def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Anti-detection measures
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def debug_property_cards():
    """Debug individual property cards"""
    driver = setup_driver()
    
    try:
        url = "https://www.99acres.com/property-for-sale-in-mumbai-ffid"
        print(f"🔍 Loading page: {url}")
        
        driver.get(url)
        time.sleep(5)
        
        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Find property links to identify cards
        property_links = soup.find_all('a', href=True)
        property_cards = []
        
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                # Find the container for this property
                current = link
                for _ in range(20):
                    if current is None:
                        break
                    
                    text = current.get_text().lower()
                    if ('₹' in text and 'sqft' in text and 'bhk' in text):
                        if current not in property_cards:
                            property_cards.append(current)
                        break
                    current = current.parent
        
        print(f"✅ Found {len(property_cards)} property cards")
        
        # Analyze first few cards in detail
        for i, card in enumerate(property_cards[:3]):
            print(f"\n{'='*60}")
            print(f"🏠 PROPERTY CARD {i+1}")
            print(f"{'='*60}")
            
            # Get card text
            card_text = card.get_text()
            print(f"📝 Card text length: {len(card_text)} characters")
            
            # Find headings
            headings = card.find_all(['h1', 'h2', 'h3', 'h4'])
            print(f"\n📋 Headings ({len(headings)}):")
            for j, heading in enumerate(headings):
                text = heading.get_text(strip=True)
                if text:
                    print(f"   {j+1}. {heading.name}: {text}")
            
            # Find links
            links = card.find_all('a', href=True)
            property_links_in_card = [link for link in links if 'spid-' in link.get('href', '') or 'npxid-' in link.get('href', '')]
            print(f"\n🔗 Property links ({len(property_links_in_card)}):")
            for j, link in enumerate(property_links_in_card):
                href = link.get('href')
                text = link.get_text(strip=True)
                print(f"   {j+1}. {href}")
                print(f"       Text: {text}")
            
            # Find price patterns
            print(f"\n💰 Price analysis:")
            price_patterns = [
                r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|crore)',
                r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Lakh|lakh)',
                r'₹\s*[\d,]+\s*/sqft'
            ]
            
            for pattern_name, pattern in [
                ("Main price (Cr)", price_patterns[0]),
                ("Main price (Lakh)", price_patterns[1]),
                ("Price per sqft", price_patterns[2])
            ]:
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    print(f"   {pattern_name}: {matches}")
                else:
                    print(f"   {pattern_name}: Not found")
            
            # Find area patterns
            print(f"\n📐 Area analysis:")
            area_patterns = [
                r'(\d+(?:,\d+)*)\s*sqft',
                r'(\d+(?:,\d+)*)\s*sq\.?\s*ft'
            ]
            
            for pattern_name, pattern in [
                ("Area (sqft)", area_patterns[0]),
                ("Area (sq.ft)", area_patterns[1])
            ]:
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    print(f"   {pattern_name}: {matches}")
                else:
                    print(f"   {pattern_name}: Not found")
            
            # Find BHK patterns
            print(f"\n🏠 BHK analysis:")
            bhk_patterns = [
                r'(\d+)\s*bhk',
                r'(\d+)\s*bedroom'
            ]
            
            for pattern_name, pattern in [
                ("BHK", bhk_patterns[0]),
                ("Bedroom", bhk_patterns[1])
            ]:
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    print(f"   {pattern_name}: {matches}")
                else:
                    print(f"   {pattern_name}: Not found")
            
            # Show raw text sample
            print(f"\n📄 Raw text sample (first 500 chars):")
            print(f"   {card_text[:500]}...")
            
            # Save individual card HTML for inspection
            with open(f'data/debug_card_{i+1}.html', 'w', encoding='utf-8') as f:
                f.write(str(card))
            print(f"💾 Card HTML saved to: data/debug_card_{i+1}.html")
        
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
    
    finally:
        driver.quit()


if __name__ == "__main__":
    debug_property_cards()
