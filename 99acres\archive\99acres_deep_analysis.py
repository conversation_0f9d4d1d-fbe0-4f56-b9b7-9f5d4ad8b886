#!/usr/bin/env python3
"""
99acres Deep Analysis Script
Comprehensive analysis of hundreds of property pages across multiple cities and property types
"""

import asyncio
import aiohttp
import json
import csv
import time
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
from collections import defaultdict, Counter
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NinetyNineAcresAnalyzer:
    def __init__(self):
        self.base_url = "https://www.99acres.com"
        self.session = None
        self.analyzed_properties = []
        self.data_fields = defaultdict(set)
        self.property_types = set()
        self.cities_analyzed = set()
        
        # Headers to mimic real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # Cities to analyze
        self.cities = [
            'mumbai', 'delhi', 'bangalore', 'pune', 'chennai', 
            'hyderabad', 'kolkata', 'ahmedabad', 'gurgaon', 'noida'
        ]
        
        # Property types to analyze
        self.property_types_search = [
            'apartment', 'villa', 'independent-house', 'plot', 
            'office', 'shop', 'warehouse', 'showroom'
        ]

    async def create_session(self):
        """Create aiohttp session with proper configuration"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            connector=connector,
            timeout=timeout
        )

    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()

    async def get_page(self, url, retries=3):
        """Get page content with retry logic"""
        for attempt in range(retries):
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        return content
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt < retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
        return None

    async def get_property_urls_from_city(self, city, property_type='', max_pages=10):
        """Get property URLs from a specific city"""
        property_urls = []
        
        for page in range(1, max_pages + 1):
            if property_type:
                search_url = f"{self.base_url}/search/property/buy/{city}?page={page}&property_type={property_type}"
            else:
                search_url = f"{self.base_url}/search/property/buy/{city}?page={page}"
                
            logger.info(f"Fetching listings from {city}, page {page}")
            
            content = await self.get_page(search_url)
            if not content:
                continue
                
            soup = BeautifulSoup(content, 'html.parser')
            
            # Extract property URLs from listing page
            property_links = soup.find_all('a', href=re.compile(r'/\d+-bhk-|/\d+-bedroom-|/plot-|/office-|/shop-'))
            
            for link in property_links:
                href = link.get('href')
                if href and href.startswith('/'):
                    full_url = urljoin(self.base_url, href)
                    property_urls.append(full_url)
            
            # Rate limiting
            await asyncio.sleep(1)
            
        return list(set(property_urls))  # Remove duplicates

    async def analyze_property_page(self, url):
        """Analyze individual property page and extract all data fields"""
        logger.info(f"Analyzing property: {url}")
        
        content = await self.get_page(url)
        if not content:
            return None
            
        soup = BeautifulSoup(content, 'html.parser')
        
        property_data = {
            'url': url,
            'extracted_fields': {},
            'property_type': 'unknown',
            'city': 'unknown',
            'price_range': 'unknown'
        }
        
        # Extract basic property information
        self.extract_basic_info(soup, property_data)
        
        # Extract detailed specifications
        self.extract_specifications(soup, property_data)
        
        # Extract amenities and features
        self.extract_amenities(soup, property_data)
        
        # Extract location and nearby places
        self.extract_location_data(soup, property_data)
        
        # Extract agent/dealer information
        self.extract_agent_info(soup, property_data)
        
        # Extract RERA and legal information
        self.extract_legal_info(soup, property_data)
        
        # Extract reviews and ratings
        self.extract_reviews_ratings(soup, property_data)
        
        # Extract market data
        self.extract_market_data(soup, property_data)
        
        return property_data

    def extract_basic_info(self, soup, property_data):
        """Extract basic property information"""
        # Property title
        title_elem = soup.find('h1') or soup.find('title')
        if title_elem:
            property_data['extracted_fields']['title'] = title_elem.get_text(strip=True)
        
        # Price information
        price_elements = soup.find_all(text=re.compile(r'₹|Cr|Lac|Lakh'))
        for elem in price_elements:
            if 'price' not in property_data['extracted_fields']:
                property_data['extracted_fields']['price'] = elem.strip()
                break
        
        # Area information
        area_elements = soup.find_all(text=re.compile(r'sq\.?ft|sqft|sq\.?m'))
        for elem in area_elements:
            if 'area' not in property_data['extracted_fields']:
                property_data['extracted_fields']['area'] = elem.strip()
                break
        
        # Configuration (BHK)
        bhk_elements = soup.find_all(text=re.compile(r'\d+\s*BHK|\d+\s*Bedroom'))
        for elem in bhk_elements:
            if 'configuration' not in property_data['extracted_fields']:
                property_data['extracted_fields']['configuration'] = elem.strip()
                break

    def extract_specifications(self, soup, property_data):
        """Extract detailed property specifications"""
        # Floor information
        floor_elements = soup.find_all(text=re.compile(r'Floor|floor'))
        for elem in floor_elements:
            if 'floor' not in property_data['extracted_fields']:
                property_data['extracted_fields']['floor'] = elem.strip()
                break
        
        # Facing direction
        facing_elements = soup.find_all(text=re.compile(r'North|South|East|West|Facing'))
        for elem in facing_elements:
            if 'facing' not in property_data['extracted_fields']:
                property_data['extracted_fields']['facing'] = elem.strip()
                break
        
        # Property age
        age_elements = soup.find_all(text=re.compile(r'Year|Age|Old|New'))
        for elem in age_elements:
            if 'age' not in property_data['extracted_fields']:
                property_data['extracted_fields']['age'] = elem.strip()
                break

    def extract_amenities(self, soup, property_data):
        """Extract amenities and features"""
        amenity_keywords = [
            'Swimming Pool', 'Gym', 'Lift', 'Parking', 'Security', 'Garden',
            'Club House', 'Power Backup', 'Water Supply', 'CCTV', 'Playground'
        ]
        
        amenities_found = []
        for keyword in amenity_keywords:
            if soup.find(text=re.compile(keyword, re.IGNORECASE)):
                amenities_found.append(keyword)
        
        if amenities_found:
            property_data['extracted_fields']['amenities'] = amenities_found

    def extract_location_data(self, soup, property_data):
        """Extract location and nearby places"""
        # Location information
        location_elements = soup.find_all(text=re.compile(r'Mumbai|Delhi|Bangalore|Pune|Chennai'))
        for elem in location_elements:
            if 'location' not in property_data['extracted_fields']:
                property_data['extracted_fields']['location'] = elem.strip()
                break
        
        # Nearby places
        nearby_keywords = ['School', 'Hospital', 'Mall', 'Metro', 'Railway', 'Airport']
        nearby_found = []
        for keyword in nearby_keywords:
            if soup.find(text=re.compile(keyword, re.IGNORECASE)):
                nearby_found.append(keyword)
        
        if nearby_found:
            property_data['extracted_fields']['nearby_places'] = nearby_found

    def extract_agent_info(self, soup, property_data):
        """Extract agent/dealer information"""
        # Agent name
        agent_elements = soup.find_all(text=re.compile(r'Agent|Dealer|Owner|Builder'))
        for elem in agent_elements:
            if 'agent_type' not in property_data['extracted_fields']:
                property_data['extracted_fields']['agent_type'] = elem.strip()
                break

    def extract_legal_info(self, soup, property_data):
        """Extract RERA and legal information"""
        # RERA information
        rera_elements = soup.find_all(text=re.compile(r'RERA|Registration'))
        for elem in rera_elements:
            if 'rera' not in property_data['extracted_fields']:
                property_data['extracted_fields']['rera'] = elem.strip()
                break

    def extract_reviews_ratings(self, soup, property_data):
        """Extract reviews and ratings"""
        # Rating information
        rating_elements = soup.find_all(text=re.compile(r'\d+\.\d+|★|Rating'))
        for elem in rating_elements:
            if 'rating' not in property_data['extracted_fields']:
                property_data['extracted_fields']['rating'] = elem.strip()
                break

    def extract_market_data(self, soup, property_data):
        """Extract market and price trend data"""
        # Price trends
        trend_elements = soup.find_all(text=re.compile(r'Price Trend|Market|Investment'))
        for elem in trend_elements:
            if 'market_data' not in property_data['extracted_fields']:
                property_data['extracted_fields']['market_data'] = elem.strip()
                break

    async def run_comprehensive_analysis(self, max_properties_per_city=50):
        """Run comprehensive analysis across multiple cities and property types"""
        await self.create_session()
        
        try:
            all_property_urls = []
            
            # Collect property URLs from multiple cities
            for city in self.cities:
                logger.info(f"Collecting property URLs from {city}")
                city_urls = await self.get_property_urls_from_city(city, max_pages=5)
                all_property_urls.extend(city_urls[:max_properties_per_city])
                
                # Rate limiting between cities
                await asyncio.sleep(2)
            
            logger.info(f"Total property URLs collected: {len(all_property_urls)}")
            
            # Analyze properties in batches
            batch_size = 10
            for i in range(0, len(all_property_urls), batch_size):
                batch = all_property_urls[i:i + batch_size]
                
                # Process batch concurrently
                tasks = [self.analyze_property_page(url) for url in batch]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, dict):
                        self.analyzed_properties.append(result)
                        
                        # Track data fields
                        for field_name in result['extracted_fields'].keys():
                            self.data_fields[field_name].add(type(result['extracted_fields'][field_name]).__name__)
                
                logger.info(f"Analyzed {len(self.analyzed_properties)} properties so far")
                
                # Rate limiting between batches
                await asyncio.sleep(3)
                
        finally:
            await self.close_session()

    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report"""
        report = {
            'summary': {
                'total_properties_analyzed': len(self.analyzed_properties),
                'unique_data_fields_found': len(self.data_fields),
                'cities_covered': len(self.cities_analyzed),
                'property_types_found': len(self.property_types)
            },
            'data_fields_analysis': dict(self.data_fields),
            'sample_properties': self.analyzed_properties[:10]  # First 10 as samples
        }
        
        # Save detailed report
        with open('99acres_comprehensive_analysis_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Save CSV for easy analysis
        if self.analyzed_properties:
            fieldnames = set()
            for prop in self.analyzed_properties:
                fieldnames.update(prop['extracted_fields'].keys())
            
            with open('99acres_properties_data.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['url'] + list(fieldnames))
                writer.writeheader()
                
                for prop in self.analyzed_properties:
                    row = {'url': prop['url']}
                    row.update(prop['extracted_fields'])
                    writer.writerow(row)
        
        logger.info(f"Analysis complete! Found {len(self.data_fields)} unique data fields")
        logger.info(f"Analyzed {len(self.analyzed_properties)} properties")
        
        return report

async def main():
    """Main function to run the comprehensive analysis"""
    analyzer = NinetyNineAcresAnalyzer()
    
    logger.info("Starting comprehensive 99acres analysis...")
    logger.info("This will analyze hundreds of properties across multiple cities and types")
    
    # Run the analysis
    await analyzer.run_comprehensive_analysis(max_properties_per_city=30)
    
    # Generate report
    report = analyzer.generate_comprehensive_report()
    
    print("\n" + "="*50)
    print("COMPREHENSIVE ANALYSIS COMPLETE")
    print("="*50)
    print(f"Total Properties Analyzed: {report['summary']['total_properties_analyzed']}")
    print(f"Unique Data Fields Found: {report['summary']['unique_data_fields_found']}")
    print(f"Cities Covered: {len(analyzer.cities)}")
    print("\nTop Data Fields Found:")
    for field, types in list(report['data_fields_analysis'].items())[:20]:
        print(f"  - {field}: {', '.join(types)}")
    
    print(f"\nDetailed report saved to: 99acres_comprehensive_analysis_report.json")
    print(f"Property data saved to: 99acres_properties_data.csv")

if __name__ == "__main__":
    asyncio.run(main())
