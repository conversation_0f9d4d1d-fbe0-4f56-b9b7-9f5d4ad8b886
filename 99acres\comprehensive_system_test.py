#!/usr/bin/env python3
"""
Comprehensive System Test
Complete end-to-end testing of the scraper system
"""

import requests
import time
import json
from datetime import datetime

def test_dashboard_api():
    """Test dashboard API endpoints"""
    print("🔍 Testing Dashboard API")
    print("=" * 40)
    
    base_url = "http://localhost:5002"
    
    try:
        # Test 1: Sources endpoint
        print("1. Testing /api/sources...")
        response = requests.get(f"{base_url}/api/sources")
        if response.status_code == 200:
            sources = response.json()
            print(f"   ✅ Sources loaded: {sources}")
            
            # Validate source data
            if 'individual_listings' in sources:
                source = sources['individual_listings']
                print(f"   📊 Total URLs: {source['total']}")
                print(f"   ✅ Extracted: {source['extracted']}")
                print(f"   🔄 Remaining: {source['remaining']}")
            else:
                print("   ❌ No individual_listings source found")
                return False
        else:
            print(f"   ❌ Sources endpoint failed: {response.status_code}")
            return False
        
        # Test 2: Stats endpoint
        print("\n2. Testing /api/stats...")
        response = requests.get(f"{base_url}/api/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Stats loaded: Running={stats['is_running']}")
            print(f"   📊 Progress: {stats['progress']}")
        else:
            print(f"   ❌ Stats endpoint failed: {response.status_code}")
            return False
        
        print("✅ Dashboard API tests PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Dashboard API tests FAILED: {str(e)}")
        return False

def test_scraping_workflow():
    """Test complete scraping workflow"""
    print("\n🚀 Testing Complete Scraping Workflow")
    print("=" * 40)
    
    base_url = "http://localhost:5002"
    
    try:
        # Test 1: Start scraping with small batch
        print("1. Starting scraping (3 properties)...")
        config = {
            'max_properties': 3,
            'skip_existing': True
        }
        
        response = requests.post(f"{base_url}/api/start", json=config)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"   ✅ Scraping started: {result['message']}")
            else:
                print(f"   ❌ Failed to start: {result['message']}")
                return False
        else:
            print(f"   ❌ Start endpoint failed: {response.status_code}")
            return False
        
        # Test 2: Monitor progress
        print("\n2. Monitoring progress...")
        start_time = datetime.now()
        max_wait_minutes = 5
        
        while True:
            response = requests.get(f"{base_url}/api/stats")
            if response.status_code == 200:
                stats = response.json()
                progress = stats['progress']
                
                print(f"   📊 Progress: {progress['processed']}/{progress['total_urls']} "
                      f"(Success: {progress['successful']}, Failed: {progress['failed']})")
                
                if progress['current_url']:
                    print(f"   🔄 Current: {progress['current_url'][:60]}...")
                
                # Check if completed
                if not stats['is_running']:
                    print(f"   ✅ Scraping completed!")
                    print(f"   📊 Final results: {progress['successful']} successful, {progress['failed']} failed")
                    break
                
                # Check timeout
                elapsed = (datetime.now() - start_time).total_seconds() / 60
                if elapsed > max_wait_minutes:
                    print(f"   ⏰ Timeout after {max_wait_minutes} minutes")
                    break
                
                time.sleep(10)  # Wait 10 seconds between checks
            else:
                print(f"   ❌ Stats check failed: {response.status_code}")
                break
        
        # Test 3: Verify results
        print("\n3. Verifying results...")
        final_response = requests.get(f"{base_url}/api/stats")
        if final_response.status_code == 200:
            final_stats = final_response.json()
            final_progress = final_stats['progress']
            
            if final_progress['successful'] > 0:
                print(f"   ✅ Successfully extracted {final_progress['successful']} properties")
                return True
            else:
                print(f"   ❌ No properties successfully extracted")
                return False
        else:
            print(f"   ❌ Final stats check failed")
            return False
        
    except Exception as e:
        print(f"❌ Scraping workflow test FAILED: {str(e)}")
        return False

def test_database_integrity():
    """Test database integrity after scraping"""
    print("\n💾 Testing Database Integrity")
    print("=" * 40)
    
    try:
        import sqlite3
        
        # Test individual properties database
        print("1. Checking individual properties database...")
        conn = sqlite3.connect('data/individual_properties_comprehensive.db')
        cursor = conn.cursor()
        
        # Count records
        cursor.execute("SELECT COUNT(*) FROM individual_properties")
        total_count = cursor.fetchone()[0]
        print(f"   📊 Total records: {total_count}")
        
        # Check recent records
        cursor.execute("""
            SELECT property_url, title, price_display 
            FROM individual_properties 
            ORDER BY id DESC LIMIT 3
        """)
        recent_records = cursor.fetchall()
        
        print(f"   📋 Recent extractions:")
        for i, (url, title, price) in enumerate(recent_records, 1):
            print(f"      {i}. {title[:40]}... | {price} | {url[:50]}...")
        
        conn.close()
        
        # Test data quality
        print("\n2. Testing data quality...")
        if total_count > 0:
            print(f"   ✅ Database has {total_count} records")
            if len(recent_records) > 0:
                print(f"   ✅ Recent records have proper structure")
                return True
            else:
                print(f"   ❌ No recent records found")
                return False
        else:
            print(f"   ❌ Database is empty")
            return False
        
    except Exception as e:
        print(f"❌ Database integrity test FAILED: {str(e)}")
        return False

def generate_test_report(api_passed, workflow_passed, db_passed):
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("🎯 COMPREHENSIVE SYSTEM TEST REPORT")
    print("=" * 60)
    
    print(f"📊 Dashboard API Test: {'✅ PASSED' if api_passed else '❌ FAILED'}")
    print(f"🚀 Scraping Workflow Test: {'✅ PASSED' if workflow_passed else '❌ FAILED'}")
    print(f"💾 Database Integrity Test: {'✅ PASSED' if db_passed else '❌ FAILED'}")
    
    overall_passed = api_passed and workflow_passed and db_passed
    
    print(f"\n🏆 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_passed else '❌ SOME TESTS FAILED'}")
    
    if overall_passed:
        print(f"\n🎉 SYSTEM IS PRODUCTION READY!")
        print(f"✅ Dashboard working correctly")
        print(f"✅ Scraping functionality validated")
        print(f"✅ Database operations confirmed")
        print(f"✅ End-to-end workflow successful")
        print(f"\n🔧 Ready for large-scale operations!")
    else:
        print(f"\n⚠️ SYSTEM NEEDS FIXES BEFORE PRODUCTION")
        if not api_passed:
            print(f"❌ Fix dashboard API issues")
        if not workflow_passed:
            print(f"❌ Fix scraping workflow issues")
        if not db_passed:
            print(f"❌ Fix database integrity issues")
    
    # Save report
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_results': {
            'dashboard_api': api_passed,
            'scraping_workflow': workflow_passed,
            'database_integrity': db_passed,
            'overall_passed': overall_passed
        },
        'status': 'PRODUCTION_READY' if overall_passed else 'NEEDS_FIXES'
    }
    
    with open('comprehensive_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Test report saved: comprehensive_test_report.json")
    return overall_passed

def main():
    """Main test function"""
    print("🧪 99acres Scraper Comprehensive System Test")
    print("=" * 60)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Testing dashboard at: http://localhost:5002")
    
    # Run all tests
    api_passed = test_dashboard_api()
    workflow_passed = test_scraping_workflow()
    db_passed = test_database_integrity()
    
    # Generate report
    overall_passed = generate_test_report(api_passed, workflow_passed, db_passed)
    
    return overall_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
