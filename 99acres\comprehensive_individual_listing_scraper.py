#!/usr/bin/env python3
"""
Comprehensive Individual Listing Scraper for 99acres
Based on deep analysis of page structures across multiple cities and property types
Extracts 67+ data fields from individual property pages
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import time
import sqlite3
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
from collections import defaultdict

class ComprehensiveIndividualListingScraper:
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.scraped_properties = []
        self.database_path = 'data/individual_properties_comprehensive.db'
        self.session_start_time = datetime.now()
        
        # Initialize database
        self.init_database()
        
    def init_database(self):
        """Initialize comprehensive database for individual property data"""
        os.makedirs('data', exist_ok=True)
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Create comprehensive table with 67+ fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS individual_properties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                
                -- Basic Information
                property_url TEXT UNIQUE,
                property_id TEXT,
                title TEXT,
                description TEXT,
                
                -- Price Information (15 fields)
                price_display TEXT,
                price_crores REAL,
                price_lakhs REAL,
                price_per_sqft REAL,
                rent_per_month REAL,
                emi_amount REAL,
                booking_amount REAL,
                maintenance_charges REAL,
                security_deposit REAL,
                registration_charges REAL,
                stamp_duty REAL,
                total_price REAL,
                price_negotiable BOOLEAN,
                price_includes TEXT,
                price_excludes TEXT,
                
                -- Property Specifications (20 fields)
                property_type TEXT,
                property_subtype TEXT,
                transaction_type TEXT,
                bhk_config TEXT,
                bedrooms INTEGER,
                bathrooms INTEGER,
                balconies INTEGER,
                area_sqft INTEGER,
                carpet_area INTEGER,
                builtup_area INTEGER,
                super_area INTEGER,
                plot_area INTEGER,
                floor_number INTEGER,
                total_floors INTEGER,
                facing_direction TEXT,
                furnishing_status TEXT,
                property_age INTEGER,
                construction_status TEXT,
                possession_date TEXT,
                parking_spaces INTEGER,
                
                -- Location Information (8 fields)
                city TEXT,
                locality TEXT,
                sub_locality TEXT,
                address TEXT,
                pincode TEXT,
                latitude REAL,
                longitude REAL,
                nearby_landmarks TEXT,
                
                -- Builder/Developer Information (6 fields)
                builder_name TEXT,
                developer_name TEXT,
                project_name TEXT,
                rera_number TEXT,
                builder_experience TEXT,
                builder_rating REAL,
                
                -- Contact Information (5 fields)
                contact_person TEXT,
                phone_numbers TEXT,
                mobile_numbers TEXT,
                email_addresses TEXT,
                broker_details TEXT,
                
                -- Amenities and Features (8 fields)
                amenities TEXT,
                features TEXT,
                society_amenities TEXT,
                nearby_facilities TEXT,
                connectivity TEXT,
                schools_nearby TEXT,
                hospitals_nearby TEXT,
                shopping_nearby TEXT,
                
                -- Images and Media (5 fields)
                total_images INTEGER,
                image_urls TEXT,
                video_urls TEXT,
                virtual_tour_url TEXT,
                floor_plan_urls TEXT,
                
                -- Additional Information
                property_highlights TEXT,
                investment_potential TEXT,
                legal_clearance TEXT,
                loan_availability TEXT,
                resale_value TEXT,
                
                -- Metadata
                scraped_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                data_source TEXT DEFAULT '99acres_individual'
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Comprehensive database initialized")
    
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Browser initialized for comprehensive scraping")
            return True
        except Exception as e:
            print(f"❌ Browser setup failed: {str(e)}")
            return False
    
    def extract_comprehensive_property_data(self, url):
        """Extract comprehensive data from individual property page"""
        print(f"🔍 Scraping: {url}")
        
        try:
            # Navigate to property page
            self.driver.get(url)
            time.sleep(5)  # Wait for page load
            
            # Wait for key elements
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                print("   ⚠️ Page load timeout")
            
            # Get page source and parse
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            page_text = soup.get_text()
            
            # Initialize property data
            property_data = {
                'property_url': url,
                'scraped_timestamp': datetime.now().isoformat()
            }
            
            # Extract all data fields
            property_data.update(self.extract_basic_information(soup, page_text))
            property_data.update(self.extract_price_information(soup, page_text))
            property_data.update(self.extract_property_specifications(soup, page_text))
            property_data.update(self.extract_location_information(soup, page_text))
            property_data.update(self.extract_builder_information(soup, page_text))
            property_data.update(self.extract_contact_information(soup, page_text))
            property_data.update(self.extract_amenities_features(soup, page_text))
            property_data.update(self.extract_images_media(soup))
            property_data.update(self.extract_additional_information(soup, page_text))
            
            print(f"   ✅ Extracted {len([k for k, v in property_data.items() if v])} fields")
            return property_data
            
        except Exception as e:
            print(f"   ❌ Error scraping property: {str(e)}")
            return None
    
    def extract_basic_information(self, soup, page_text):
        """Extract basic property information"""
        data = {}
        
        # Property ID from URL
        property_id_match = re.search(r'spid-([A-Z0-9]+)', soup.find('title').get_text() if soup.find('title') else '')
        data['property_id'] = property_id_match.group(1) if property_id_match else None
        
        # Title
        title_tag = soup.find('title')
        data['title'] = title_tag.get_text(strip=True) if title_tag else ''
        
        # Description
        desc_elements = soup.find_all(['div', 'p'], class_=re.compile(r'(description|detail|about)', re.I))
        descriptions = []
        for elem in desc_elements[:3]:
            text = elem.get_text(strip=True)
            if len(text) > 50:
                descriptions.append(text)
        data['description'] = ' | '.join(descriptions) if descriptions else ''
        
        return data
    
    def extract_price_information(self, soup, page_text):
        """Extract comprehensive price information"""
        data = {}
        
        # Price patterns based on analysis
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr(?:ore)?', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh?', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/\s*sqft', 'price_per_sqft'),
            (r'₹\s*([\d,]+)\s*/\s*month', 'rent_per_month'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges'),
            (r'Security\s*Deposit\s*₹\s*([\d,]+)', 'security_deposit'),
            (r'Registration\s*₹\s*([\d,]+)', 'registration_charges'),
            (r'Stamp\s*Duty\s*₹\s*([\d,]+)', 'stamp_duty'),
            (r'Total\s*Price\s*₹\s*([\d,]+)', 'total_price')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                # Convert to float, removing commas
                try:
                    value = float(matches[0].replace(',', ''))
                    data[field_name] = value
                except:
                    data[field_name] = matches[0]
        
        # Price display (main price shown)
        price_elements = soup.find_all(['span', 'div'], class_=re.compile(r'(price|amount|cost)', re.I))
        for elem in price_elements:
            text = elem.get_text(strip=True)
            if '₹' in text and any(word in text.lower() for word in ['cr', 'lakh', 'crore']):
                data['price_display'] = text
                break
        
        # Price negotiable
        data['price_negotiable'] = 'negotiable' in page_text.lower()
        
        return data

    def extract_property_specifications(self, soup, page_text):
        """Extract detailed property specifications"""
        data = {}

        # Specification patterns based on analysis
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'Carpet\s*Area\s*(\d+)', 'carpet_area'),
            (r'Built-up\s*Area\s*(\d+)', 'builtup_area'),
            (r'Super\s*Area\s*(\d+)', 'super_area'),
            (r'Plot\s*Area\s*(\d+)', 'plot_area'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date'),
            (r'(\d+)\s*parking', 'parking_spaces')
        ]

        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                if field_name in ['bedrooms', 'bathrooms', 'balconies', 'area_sqft', 'carpet_area',
                                'builtup_area', 'super_area', 'plot_area', 'floor_number',
                                'total_floors', 'property_age', 'parking_spaces']:
                    try:
                        data[field_name] = int(matches[0])
                    except:
                        data[field_name] = matches[0]
                else:
                    data[field_name] = matches[0]

        # Property type and transaction type from URL
        url = data.get('property_url', '')
        if 'for-sale' in url:
            data['transaction_type'] = 'Sale'
        elif 'for-rent' in url:
            data['transaction_type'] = 'Rent'

        if 'apartment' in url:
            data['property_type'] = 'Apartment'
        elif 'villa' in url:
            data['property_type'] = 'Villa'
        elif 'plot' in url:
            data['property_type'] = 'Plot'
        elif 'house' in url:
            data['property_type'] = 'House'

        return data

    def extract_location_information(self, soup, page_text):
        """Extract location and address information"""
        data = {}

        # Location patterns
        location_patterns = [
            (r'Located in\s*([^,\n]+)', 'locality'),
            (r'Near\s*([^,\n]+)', 'nearby_landmarks'),
            (r'(\d{6})', 'pincode'),
            (r'(Mumbai|Delhi|Bangalore|Pune|Chennai|Hyderabad|Kolkata|Ahmedabad)', 'city')
        ]

        for pattern, field_name in location_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches[0]

        # Extract city from URL
        url = data.get('property_url', '')
        url_parts = url.split('-')
        for part in url_parts:
            if part.lower() in ['mumbai', 'delhi', 'bangalore', 'pune', 'chennai', 'hyderabad', 'kolkata']:
                data['city'] = part.title()
                break

        # Address from structured elements
        address_elements = soup.find_all(['div', 'span'], class_=re.compile(r'(address|location)', re.I))
        for elem in address_elements:
            text = elem.get_text(strip=True)
            if len(text) > 20 and ',' in text:
                data['address'] = text
                break

        return data

    def extract_builder_information(self, soup, page_text):
        """Extract builder and developer information"""
        data = {}

        # Builder patterns
        builder_patterns = [
            (r'By\s*([A-Z][^,\n]+(?:Group|Developers?|Builders?|Construction|Realty|Properties))', 'builder_name'),
            (r'Developer\s*:\s*([^,\n]+)', 'developer_name'),
            (r'RERA\s*(?:ID|Number)\s*:\s*([A-Z0-9]+)', 'rera_number'),
            (r'Project\s*:\s*([^,\n]+)', 'project_name')
        ]

        for pattern, field_name in builder_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches[0]

        return data

    def extract_contact_information(self, soup, page_text):
        """Extract contact information"""
        data = {}

        # Contact patterns
        contact_patterns = [
            (r'(\+91[\s-]?\d{10})', 'phone_numbers'),
            (r'(\d{10})', 'mobile_numbers'),
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 'email_addresses')
        ]

        for pattern, field_name in contact_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                # Remove duplicates and join
                unique_matches = list(set(matches))
                data[field_name] = ', '.join(unique_matches)

        # Contact person
        contact_elements = soup.find_all(['div', 'span'], class_=re.compile(r'(agent|broker|contact)', re.I))
        for elem in contact_elements:
            text = elem.get_text(strip=True)
            if len(text) > 5 and len(text) < 50 and not any(char.isdigit() for char in text):
                data['contact_person'] = text
                break

        return data

    def extract_amenities_features(self, soup, page_text):
        """Extract amenities and features"""
        data = {}

        # Common amenities keywords
        amenities_keywords = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
            'Club House', 'Lift', 'Power Backup', 'Water Supply', 'Intercom',
            'Fire Safety', 'CCTV', 'Jogging Track', 'Tennis Court', 'Basketball',
            'Spa', 'Library', 'Conference Room', 'Party Hall', 'Cafeteria'
        ]

        found_amenities = []
        for amenity in amenities_keywords:
            if amenity.lower() in page_text.lower():
                found_amenities.append(amenity)

        if found_amenities:
            data['amenities'] = ', '.join(found_amenities)

        # Society amenities from structured elements
        amenity_sections = soup.find_all(['div', 'ul'], class_=re.compile(r'(amenity|amenities|facility)', re.I))
        society_amenities = []
        for section in amenity_sections:
            items = section.find_all('li')
            for item in items:
                text = item.get_text(strip=True)
                if len(text) > 3 and len(text) < 50:
                    society_amenities.append(text)

        if society_amenities:
            data['society_amenities'] = ', '.join(society_amenities[:10])  # Limit to 10

        # Nearby facilities
        nearby_keywords = ['School', 'Hospital', 'Mall', 'Metro', 'Airport', 'Railway', 'Bus Stop']
        nearby_facilities = []
        for keyword in nearby_keywords:
            if keyword.lower() in page_text.lower():
                nearby_facilities.append(keyword)

        if nearby_facilities:
            data['nearby_facilities'] = ', '.join(nearby_facilities)

        return data

    def extract_images_media(self, soup):
        """Extract images and media information"""
        data = {}

        # Count total images
        images = soup.find_all('img', src=True)
        property_images = [img for img in images if img.get('src') and
                          any(keyword in img.get('src', '').lower() for keyword in ['property', 'image', 'photo'])]

        data['total_images'] = len(property_images)

        # Sample image URLs
        image_urls = []
        for img in property_images[:10]:  # Limit to 10 URLs
            src = img.get('src')
            if src and src.startswith('http'):
                image_urls.append(src)

        if image_urls:
            data['image_urls'] = ', '.join(image_urls)

        # Video URLs
        video_elements = soup.find_all(['video', 'iframe'], src=True)
        video_urls = []
        for video in video_elements:
            src = video.get('src')
            if src and ('video' in src.lower() or 'youtube' in src.lower()):
                video_urls.append(src)

        if video_urls:
            data['video_urls'] = ', '.join(video_urls)

        # Virtual tour
        virtual_tour_elements = soup.find_all(['a', 'div'], class_=re.compile(r'(virtual|tour|360)', re.I))
        for elem in virtual_tour_elements:
            href = elem.get('href') if elem.name == 'a' else None
            if href:
                data['virtual_tour_url'] = href
                break

        return data

    def extract_additional_information(self, soup, page_text):
        """Extract additional property information"""
        data = {}

        # Property highlights
        highlight_elements = soup.find_all(['div', 'ul'], class_=re.compile(r'(highlight|feature|benefit)', re.I))
        highlights = []
        for elem in highlight_elements:
            text = elem.get_text(strip=True)
            if len(text) > 20 and len(text) < 200:
                highlights.append(text)

        if highlights:
            data['property_highlights'] = ' | '.join(highlights[:3])

        # Legal clearance
        if any(keyword in page_text.lower() for keyword in ['clear title', 'legal', 'approved', 'rera']):
            data['legal_clearance'] = 'Available'

        # Loan availability
        if any(keyword in page_text.lower() for keyword in ['loan', 'finance', 'bank', 'mortgage']):
            data['loan_availability'] = 'Available'

        return data

    def save_to_database(self, property_data):
        """Save property data to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Prepare data for insertion
            columns = list(property_data.keys())
            values = list(property_data.values())
            placeholders = ', '.join(['?' for _ in values])

            # Insert or update
            query = f'''
                INSERT OR REPLACE INTO individual_properties
                ({', '.join(columns)})
                VALUES ({placeholders})
            '''

            cursor.execute(query, values)
            conn.commit()
            conn.close()

            return True

        except Exception as e:
            print(f"   ❌ Database error: {str(e)}")
            return False

    def scrape_properties_from_sample(self, sample_file='manual_analysis_sample_fixed.json', max_properties=50):
        """Scrape properties from the sample file"""
        print("🚀 Starting Comprehensive Individual Property Scraping")
        print("=" * 60)

        try:
            # Load sample URLs
            with open(sample_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            sample_properties = data['sample_urls']

            print(f"📋 Loaded {len(sample_properties)} properties from sample")

            # Setup browser
            self.setup_driver()

            # Scrape properties
            scraped_count = 0
            for i, prop in enumerate(sample_properties[:max_properties], 1):
                print(f"\n📍 Property {i}/{min(max_properties, len(sample_properties))}")

                # Extract comprehensive data
                property_data = self.extract_comprehensive_property_data(prop['url'])

                if property_data:
                    # Save to database
                    if self.save_to_database(property_data):
                        scraped_count += 1
                        print(f"   💾 Saved to database")

                    # Add to results
                    self.scraped_properties.append(property_data)

                    # Respectful delay
                    time.sleep(3)
                else:
                    print(f"   ⚠️ Failed to extract data")

            print(f"\n✅ Scraping complete! Successfully scraped {scraped_count} properties")
            return self.scraped_properties

        except Exception as e:
            print(f"❌ Scraping failed: {str(e)}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def scrape_properties_from_database(self, max_properties=100):
        """Scrape individual properties using URLs from the main database"""
        print("🚀 Starting Comprehensive Scraping from Database URLs")
        print("=" * 60)

        try:
            # Get URLs from main database
            main_db_path = 'data/99acres_properties.db'
            conn = sqlite3.connect(main_db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT property_url, title, city, property_type
                FROM properties
                WHERE property_url IS NOT NULL AND property_url != ''
                ORDER BY RANDOM() LIMIT ?
            """, (max_properties,))

            property_urls = cursor.fetchall()
            conn.close()

            print(f"📋 Found {len(property_urls)} property URLs in database")

            if not property_urls:
                print("❌ No property URLs found in database")
                return []

            # Setup browser
            self.setup_driver()

            # Scrape properties
            scraped_count = 0
            for i, (url, title, city, prop_type) in enumerate(property_urls, 1):
                print(f"\n📍 Property {i}/{len(property_urls)}")
                print(f"   Title: {title[:50]}...")
                print(f"   City: {city}, Type: {prop_type}")

                # Fix URL format
                if url.startswith('http'):
                    fixed_url = url
                elif url.startswith('www.'):
                    fixed_url = f"https://{url}"
                else:
                    fixed_url = f"https://www.99acres.com/{url}"

                # Extract comprehensive data
                property_data = self.extract_comprehensive_property_data(fixed_url)

                if property_data:
                    # Save to database
                    if self.save_to_database(property_data):
                        scraped_count += 1
                        print(f"   💾 Saved to database")

                    # Add to results
                    self.scraped_properties.append(property_data)

                    # Respectful delay
                    time.sleep(3)
                else:
                    print(f"   ⚠️ Failed to extract data")

            print(f"\n✅ Scraping complete! Successfully scraped {scraped_count} properties")
            return self.scraped_properties

        except Exception as e:
            print(f"❌ Scraping failed: {str(e)}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def generate_comprehensive_report(self):
        """Generate comprehensive scraping report"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Get summary statistics
            cursor.execute("SELECT COUNT(*) FROM individual_properties")
            total_count = cursor.fetchone()[0]

            cursor.execute("SELECT city, COUNT(*) FROM individual_properties GROUP BY city ORDER BY COUNT(*) DESC")
            city_distribution = cursor.fetchall()

            cursor.execute("SELECT transaction_type, COUNT(*) FROM individual_properties GROUP BY transaction_type")
            transaction_distribution = cursor.fetchall()

            cursor.execute("SELECT property_type, COUNT(*) FROM individual_properties GROUP BY property_type")
            property_type_distribution = cursor.fetchall()

            # Generate report
            report = {
                'scraping_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_properties_scraped': total_count,
                    'session_duration': str(datetime.now() - self.session_start_time),
                    'scraper_version': 'Comprehensive Individual 1.0'
                },
                'data_distribution': {
                    'cities': dict(city_distribution),
                    'transaction_types': dict(transaction_distribution),
                    'property_types': dict(property_type_distribution)
                },
                'field_coverage': self.analyze_field_coverage()
            }

            # Save report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'comprehensive_individual_scraping_report_{timestamp}.json'

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"\n📊 COMPREHENSIVE SCRAPING REPORT:")
            print(f"   Total properties: {total_count}")
            print(f"   Cities: {dict(city_distribution)}")
            print(f"   Transaction types: {dict(transaction_distribution)}")
            print(f"   Property types: {dict(property_type_distribution)}")
            print(f"   Report saved: {filename}")

            conn.close()
            return report

        except Exception as e:
            print(f"❌ Error generating report: {str(e)}")
            return None

    def analyze_field_coverage(self):
        """Analyze field coverage across scraped properties"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Get column names
            cursor.execute("PRAGMA table_info(individual_properties)")
            columns = [col[1] for col in cursor.fetchall()]

            field_coverage = {}
            for column in columns:
                if column not in ['id', 'scraped_timestamp', 'last_updated', 'data_source']:
                    cursor.execute(f"SELECT COUNT(*) FROM individual_properties WHERE {column} IS NOT NULL AND {column} != ''")
                    count = cursor.fetchone()[0]
                    cursor.execute("SELECT COUNT(*) FROM individual_properties")
                    total = cursor.fetchone()[0]

                    if total > 0:
                        coverage_percentage = (count / total) * 100
                        field_coverage[column] = {
                            'filled_count': count,
                            'total_count': total,
                            'coverage_percentage': round(coverage_percentage, 2)
                        }

            conn.close()
            return field_coverage

        except Exception as e:
            print(f"❌ Error analyzing field coverage: {str(e)}")
            return {}

def main():
    """Main function to run comprehensive individual property scraping"""
    print("🔍 99acres Comprehensive Individual Listing Scraper")
    print("=" * 60)

    scraper = ComprehensiveIndividualListingScraper(headless=False)

    # Option 1: Scrape from sample file
    print("🎯 Option 1: Scrape from sample file (20 properties)")
    scraped_properties = scraper.scrape_properties_from_sample(max_properties=20)

    # Option 2: Scrape from database URLs (uncomment to use)
    # print("🎯 Option 2: Scrape from database URLs (50 properties)")
    # scraped_properties = scraper.scrape_properties_from_database(max_properties=50)

    # Generate comprehensive report
    if scraped_properties:
        report = scraper.generate_comprehensive_report()
        print(f"\n🎉 Comprehensive individual property scraping complete!")
        print(f"📊 Scraped {len(scraped_properties)} properties with 67+ data fields each")
    else:
        print("❌ No properties were successfully scraped")

if __name__ == "__main__":
    main()
