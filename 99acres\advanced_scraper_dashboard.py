#!/usr/bin/env python3
"""
Advanced Real-Time Scraper Dashboard
Comprehensive monitoring with detailed progress tracking, error handling, and controls
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
import threading
import time
import queue
from datetime import datetime, timedelta
import psutil
import logging
from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper

app = Flask(__name__)

class AdvancedScraperMonitor:
    """Advanced monitoring with detailed progress tracking"""
    
    def __init__(self):
        self.scraper = None
        self.scraper_thread = None
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.end_time = None
        
        # Detailed progress tracking
        self.progress = {
            'total_urls': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'current_url': '',
            'current_property_title': '',
            'avg_time_per_property': 0,
            'estimated_completion': None,
            'urls_per_minute': 0,
            'success_rate': 0
        }
        
        # Real-time logs
        self.live_logs = queue.Queue(maxsize=100)
        self.error_details = []
        self.recent_extractions = []
        
        # Configuration
        self.config = {
            'max_properties': 100,
            'source_type': 'individual_listings',  # or 'main_listings'
            'skip_existing': True,
            'delay_range': [2.0, 4.0],
            'headless': True
        }
        
        # Setup logging
        self.setup_logging()
    
    def setup_logging(self):
        """Setup detailed logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('advanced_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("AdvancedScraper")
    
    def add_log(self, level, message):
        """Add log entry to live logs"""
        log_entry = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'level': level,
            'message': message
        }
        
        try:
            self.live_logs.put_nowait(log_entry)
        except queue.Full:
            # Remove oldest entry and add new one
            try:
                self.live_logs.get_nowait()
                self.live_logs.put_nowait(log_entry)
            except queue.Empty:
                pass
    
    def get_url_sources(self):
        """Get available URL sources and counts"""
        sources = {}
        
        try:
            # Individual listings from main database
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM properties 
                WHERE property_url IS NOT NULL AND property_url != ''
                AND property_url NOT LIKE '%httpswww%'
            """)
            individual_total = cursor.fetchone()[0]
            
            # Check how many already extracted
            cursor.execute("""
                SELECT COUNT(*) FROM properties p
                WHERE p.property_url IS NOT NULL 
                AND EXISTS (
                    SELECT 1 FROM individual_properties_comprehensive i 
                    WHERE i.property_url = p.property_url
                )
            """)
            individual_extracted = cursor.fetchone()[0]
            
            sources['individual_listings'] = {
                'name': 'Individual Property Listings',
                'total': individual_total,
                'extracted': individual_extracted,
                'remaining': individual_total - individual_extracted,
                'description': 'Detailed individual property pages with comprehensive data'
            }
            
            conn.close()
            
            # Main listings (if available)
            sources['main_listings'] = {
                'name': 'Main Property Listings',
                'total': 0,
                'extracted': 0,
                'remaining': 0,
                'description': 'Property listing pages (not yet implemented)'
            }
            
        except Exception as e:
            self.logger.error(f"Error getting URL sources: {str(e)}")
        
        return sources
    
    def start_scraping(self, config_data):
        """Start scraping with comprehensive tracking"""
        if self.is_running:
            return False, "Scraper is already running"
        
        try:
            # Update configuration
            self.config.update(config_data)
            
            # Initialize scraper
            self.scraper = ComprehensiveIndividualListingScraper(headless=self.config['headless'])
            
            # Reset progress
            self.progress = {
                'total_urls': 0,
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0,
                'current_url': '',
                'current_property_title': '',
                'avg_time_per_property': 0,
                'estimated_completion': None,
                'urls_per_minute': 0,
                'success_rate': 0
            }
            
            # Clear logs
            while not self.live_logs.empty():
                try:
                    self.live_logs.get_nowait()
                except queue.Empty:
                    break
            
            self.error_details = []
            self.recent_extractions = []
            
            # Start scraping thread
            self.scraper_thread = threading.Thread(target=self._run_scraping_with_tracking)
            self.scraper_thread.daemon = True
            self.scraper_thread.start()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            self.add_log('INFO', f"Started scraping {self.config['max_properties']} properties")
            return True, "Scraping started successfully"
            
        except Exception as e:
            self.add_log('ERROR', f"Failed to start scraping: {str(e)}")
            return False, f"Failed to start scraping: {str(e)}"
    
    def _run_scraping_with_tracking(self):
        """Run scraping with detailed progress tracking"""
        try:
            # Load URLs based on source type
            if self.config['source_type'] == 'individual_listings':
                urls = self._load_individual_listing_urls()
            else:
                urls = []  # Main listings not implemented yet
            
            if not urls:
                self.add_log('ERROR', "No URLs found to scrape")
                return
            
            self.progress['total_urls'] = len(urls)
            self.add_log('INFO', f"Loaded {len(urls)} URLs for scraping")
            
            # Setup scraper
            if not self.scraper.setup_driver():
                self.add_log('ERROR', "Failed to setup browser driver")
                return
            
            # Process each URL
            extraction_times = []
            
            for i, url in enumerate(urls, 1):
                if not self.is_running:  # Check if stopped
                    break
                
                while self.is_paused:  # Handle pause
                    time.sleep(1)
                
                self.progress['processed'] = i
                self.progress['current_url'] = url
                
                # Check if already extracted (if skip_existing enabled)
                if self.config['skip_existing'] and self._is_already_extracted(url):
                    self.progress['skipped'] += 1
                    self.add_log('INFO', f"Skipped (already extracted): {url}")
                    continue
                
                # Extract property data
                start_time = time.time()
                self.add_log('INFO', f"Processing {i}/{len(urls)}: {url}")
                
                try:
                    property_data = self.scraper.extract_comprehensive_property_data(url)
                    extraction_time = time.time() - start_time
                    extraction_times.append(extraction_time)
                    
                    if property_data:
                        # Save to database
                        if self.scraper.save_to_database(property_data):
                            self.progress['successful'] += 1
                            self.progress['current_property_title'] = property_data.get('title', 'Unknown')
                            
                            # Add to recent extractions
                            self.recent_extractions.append({
                                'title': property_data.get('title', 'Unknown')[:50],
                                'price': property_data.get('price_display', 'N/A'),
                                'bhk': property_data.get('bhk_config', 'N/A'),
                                'city': property_data.get('city', 'N/A'),
                                'extraction_time': extraction_time,
                                'timestamp': datetime.now().strftime("%H:%M:%S")
                            })
                            
                            # Keep only last 10
                            if len(self.recent_extractions) > 10:
                                self.recent_extractions.pop(0)
                            
                            self.add_log('SUCCESS', f"Extracted: {property_data.get('title', 'Unknown')[:50]}...")
                        else:
                            self.progress['failed'] += 1
                            self.add_log('ERROR', f"Failed to save to database: {url}")
                    else:
                        self.progress['failed'] += 1
                        self.add_log('ERROR', f"Failed to extract data: {url}")
                
                except Exception as e:
                    self.progress['failed'] += 1
                    error_msg = str(e)
                    self.add_log('ERROR', f"Extraction error: {error_msg}")
                    
                    # Add to error details
                    self.error_details.append({
                        'url': url,
                        'error': error_msg,
                        'timestamp': datetime.now().strftime("%H:%M:%S")
                    })
                
                # Update performance metrics
                if extraction_times:
                    self.progress['avg_time_per_property'] = sum(extraction_times) / len(extraction_times)
                    
                    # Calculate URLs per minute
                    elapsed_minutes = (datetime.now() - self.start_time).total_seconds() / 60
                    if elapsed_minutes > 0:
                        self.progress['urls_per_minute'] = self.progress['processed'] / elapsed_minutes
                    
                    # Estimate completion time
                    remaining = self.progress['total_urls'] - self.progress['processed']
                    if self.progress['urls_per_minute'] > 0:
                        remaining_minutes = remaining / self.progress['urls_per_minute']
                        self.progress['estimated_completion'] = (datetime.now() + timedelta(minutes=remaining_minutes)).strftime("%H:%M:%S")
                
                # Calculate success rate
                if self.progress['processed'] > 0:
                    self.progress['success_rate'] = (self.progress['successful'] / (self.progress['processed'] - self.progress['skipped'])) * 100
                
                # Intelligent delay
                delay = self.config['delay_range'][0] + (self.config['delay_range'][1] - self.config['delay_range'][0]) * 0.5
                time.sleep(delay)
            
            self.add_log('INFO', f"Scraping completed: {self.progress['successful']} successful, {self.progress['failed']} failed")
            
        except Exception as e:
            self.add_log('ERROR', f"Scraping thread error: {str(e)}")
        finally:
            self.is_running = False
            self.end_time = datetime.now()
            if self.scraper and self.scraper.driver:
                self.scraper.driver.quit()
    
    def _load_individual_listing_urls(self):
        """Load individual listing URLs"""
        try:
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            if self.config['skip_existing']:
                # Only get URLs not already extracted
                cursor.execute("""
                    SELECT p.property_url 
                    FROM properties p
                    WHERE p.property_url IS NOT NULL AND p.property_url != ''
                    AND p.property_url NOT LIKE '%httpswww%'
                    AND NOT EXISTS (
                        SELECT 1 FROM individual_properties_comprehensive i 
                        WHERE i.property_url = p.property_url
                    )
                    ORDER BY RANDOM() LIMIT ?
                """, (self.config['max_properties'],))
            else:
                # Get all URLs
                cursor.execute("""
                    SELECT property_url 
                    FROM properties 
                    WHERE property_url IS NOT NULL AND property_url != ''
                    AND property_url NOT LIKE '%httpswww%'
                    ORDER BY RANDOM() LIMIT ?
                """, (self.config['max_properties'],))
            
            urls = [row[0] for row in cursor.fetchall()]
            conn.close()
            return urls
            
        except Exception as e:
            self.logger.error(f"Error loading URLs: {str(e)}")
            return []
    
    def _is_already_extracted(self, url):
        """Check if URL is already extracted"""
        try:
            conn = sqlite3.connect('data/individual_properties_comprehensive.db')
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM individual_properties WHERE property_url = ?", (url,))
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except:
            return False
    
    def pause_scraping(self):
        """Pause scraping"""
        self.is_paused = True
        self.add_log('INFO', "Scraping paused")
        return True, "Scraping paused"
    
    def resume_scraping(self):
        """Resume scraping"""
        self.is_paused = False
        self.add_log('INFO', "Scraping resumed")
        return True, "Scraping resumed"
    
    def stop_scraping(self):
        """Stop scraping"""
        self.is_running = False
        self.is_paused = False
        self.add_log('INFO', "Scraping stopped")
        return True, "Scraping stopped"
    
    def get_current_stats(self):
        """Get comprehensive current statistics"""
        # System stats
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent()
        
        # Get live logs
        logs = []
        temp_logs = []
        while not self.live_logs.empty():
            try:
                temp_logs.append(self.live_logs.get_nowait())
            except queue.Empty:
                break
        
        # Put logs back and return them
        for log in temp_logs:
            try:
                self.live_logs.put_nowait(log)
                logs.append(log)
            except queue.Full:
                break
        
        # Reverse to show newest first
        logs.reverse()
        
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'progress': self.progress,
            'system': {
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage
            },
            'timing': {
                'start_time': self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                'end_time': self.end_time.strftime("%H:%M:%S") if self.end_time else None,
                'elapsed_time': str(datetime.now() - self.start_time).split('.')[0] if self.start_time else None
            },
            'live_logs': logs[-20:],  # Last 20 logs
            'error_details': self.error_details[-10:],  # Last 10 errors
            'recent_extractions': self.recent_extractions,
            'config': self.config
        }

# Global monitor instance
monitor = AdvancedScraperMonitor()

@app.route('/')
def dashboard():
    """Advanced dashboard page"""
    return render_template('advanced_dashboard.html')

@app.route('/api/stats')
def get_stats():
    """API endpoint for comprehensive statistics"""
    return jsonify(monitor.get_current_stats())

@app.route('/api/sources')
def get_sources():
    """API endpoint for URL sources"""
    return jsonify(monitor.get_url_sources())

@app.route('/api/start', methods=['POST'])
def start_scraping():
    """API endpoint to start scraping"""
    config_data = request.json
    success, message = monitor.start_scraping(config_data)
    return jsonify({'success': success, 'message': message})

@app.route('/api/pause', methods=['POST'])
def pause_scraping():
    """API endpoint to pause scraping"""
    success, message = monitor.pause_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/resume', methods=['POST'])
def resume_scraping():
    """API endpoint to resume scraping"""
    success, message = monitor.resume_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_scraping():
    """API endpoint to stop scraping"""
    success, message = monitor.stop_scraping()
    return jsonify({'success': success, 'message': message})

def create_advanced_dashboard_template():
    """Create the advanced dashboard HTML template"""
    os.makedirs('templates', exist_ok=True)

    html_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced 99acres Scraper Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f0f2f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }

        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; color: white !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.1em; opacity: 0.95; color: white !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }

        .main-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px; }
        .left-panel { display: flex; flex-direction: column; gap: 20px; }
        .right-panel { display: flex; flex-direction: column; gap: 20px; }

        .card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .card h3 { color: #333; margin-bottom: 15px; font-size: 1.3em; }

        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-item { text-align: center; padding: 15px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }

        .progress-section { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 25px; background: #e9ecef; border-radius: 15px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s ease; border-radius: 15px; }
        .progress-text { text-align: center; margin-top: 10px; font-weight: bold; }

        .control-panel { background: white; border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .control-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .control-group { display: flex; flex-direction: column; }
        .control-group label { margin-bottom: 8px; font-weight: bold; color: #333; }
        .control-group input, .control-group select { padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; }
        .control-group input:focus, .control-group select:focus { outline: none; border-color: #667eea; }

        .button-group { display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px; transition: all 0.3s ease; }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }

        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #28a745; animation: pulse 2s infinite; }
        .status-paused { background: #ffc107; }
        .status-stopped { background: #dc3545; }

        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }

        .logs-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; border-radius: 10px; padding: 15px; }
        .log-entry { padding: 8px 12px; margin-bottom: 5px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 13px; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }

        .recent-extractions { max-height: 300px; overflow-y: auto; }
        .extraction-item { padding: 12px; margin-bottom: 8px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .extraction-title { font-weight: bold; color: #333; margin-bottom: 4px; }
        .extraction-details { font-size: 12px; color: #666; }

        /* Stats Overview */
        .stats-overview { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .stat-card .stat-value { font-size: 2.5em; font-weight: bold; margin-bottom: 5px; color: white !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .stat-card .stat-label { font-size: 1em; opacity: 0.95; color: white !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }

        /* Data Source & City Breakdown */
        .data-source-card { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        .data-source-card h3 { color: white; margin-bottom: 20px; }
        .source-header h4 { color: white; font-size: 1.4em; margin-bottom: 8px; }
        .source-header p { color: rgba(255,255,255,0.9); margin-bottom: 20px; }

        .breakdown-stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
        .breakdown-item { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .breakdown-value { font-size: 2em; font-weight: bold; color: white; margin-bottom: 5px; }
        .breakdown-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        .city-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; }
        .city-item { background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; }
        .city-name { font-weight: bold; color: white; margin-bottom: 8px; font-size: 0.95em; }
        .city-stats { display: flex; justify-content: space-between; font-size: 0.85em; }
        .city-total { color: rgba(255,255,255,0.9); }
        .city-extracted { color: #90EE90; font-weight: bold; }
        .city-remaining { color: rgba(255,255,255,0.7); }

        /* Progress & Performance Card */
        .progress-performance-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 20px; }
        .progress-performance-card h3 { color: white; }

        .performance-stats { display: grid; grid-template-columns: repeat(5, 1fr); gap: 15px; margin-bottom: 20px; }
        .perf-stat { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .perf-value { font-size: 2em; font-weight: bold; color: white; margin-bottom: 5px; }
        .perf-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        .progress-bar-container { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 25px; background: rgba(255,255,255,0.2); border-radius: 15px; overflow: hidden; margin-bottom: 10px; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; border-radius: 15px; }
        .progress-text { text-align: center; color: white; font-weight: bold; font-size: 1.1em; }

        .current-property-info { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px; }
        .property-label { color: rgba(255,255,255,0.8); font-size: 0.9em; margin-bottom: 5px; }
        .property-id { color: white; font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .property-details { display: flex; gap: 20px; flex-wrap: wrap; font-size: 0.9em; }
        .property-details span { color: rgba(255,255,255,0.9); }

        .performance-metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
        .metric-item { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .metric-value { font-size: 1.8em; font-weight: bold; color: white; margin-bottom: 5px; }
        .metric-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        @media (max-width: 768px) {
            .main-grid { grid-template-columns: 1fr; }
            .stats-overview { grid-template-columns: repeat(2, 1fr); }
            .breakdown-stats { grid-template-columns: repeat(2, 1fr); }
            .performance-stats { grid-template-columns: repeat(3, 1fr); }
            .performance-metrics { grid-template-columns: 1fr; }
            .city-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Advanced 99acres Scraper Dashboard</h1>
            <p>Comprehensive real-time monitoring with detailed progress tracking and intelligent controls</p>
        </div>

        <!-- Main Stats Overview -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-value" id="total-properties">2735</div>
                <div class="stat-label">Total Properties</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="extracted-count">113</div>
                <div class="stat-label">Extracted</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="remaining-count">2622</div>
                <div class="stat-label">Remaining</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completion-percentage">4.1%</div>
                <div class="stat-label">Complete</div>
            </div>
        </div>

        <div class="main-grid">
            <div class="left-panel">
                <!-- Data Source & City Breakdown -->
                <div class="card data-source-card">
                    <h3>📊 Data Source & City Breakdown</h3>
                    <div class="source-header">
                        <h4>Individual Property Listings</h4>
                        <p>Comprehensive property database with 2735 properties across multiple cities</p>
                    </div>

                    <div class="city-breakdown">
                        <div class="breakdown-stats">
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="total-display">2735</div>
                                <div class="breakdown-label">Total</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="extracted-display">113</div>
                                <div class="breakdown-label">Extracted</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="remaining-display">2622</div>
                                <div class="breakdown-label">Remaining</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="progress-display">4.1%</div>
                                <div class="breakdown-label">Progress</div>
                            </div>
                        </div>

                        <div class="city-grid" id="city-breakdown-grid">
                            <div class="city-item">
                                <div class="city-name">South Delhi</div>
                                <div class="city-stats">
                                    <span class="city-total">401</span>
                                    <span class="city-extracted">45</span>
                                    <span class="city-remaining">356</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Pune East</div>
                                <div class="city-stats">
                                    <span class="city-total">396</span>
                                    <span class="city-extracted">22</span>
                                    <span class="city-remaining">374</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Mumbai</div>
                                <div class="city-stats">
                                    <span class="city-total">297</span>
                                    <span class="city-extracted">18</span>
                                    <span class="city-remaining">279</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Bangalore North</div>
                                <div class="city-stats">
                                    <span class="city-total">207</span>
                                    <span class="city-extracted">12</span>
                                    <span class="city-remaining">195</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Western Mumbai</div>
                                <div class="city-stats">
                                    <span class="city-total">191</span>
                                    <span class="city-extracted">8</span>
                                    <span class="city-remaining">183</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Hyderabad</div>
                                <div class="city-stats">
                                    <span class="city-total">137</span>
                                    <span class="city-extracted">5</span>
                                    <span class="city-remaining">132</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">North Delhi</div>
                                <div class="city-stats">
                                    <span class="city-total">144</span>
                                    <span class="city-extracted">3</span>
                                    <span class="city-remaining">141</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Thane Outskirts</div>
                                <div class="city-stats">
                                    <span class="city-total">135</span>
                                    <span class="city-extracted">0</span>
                                    <span class="city-remaining">135</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <!-- Advanced Controls -->
                <div class="card">
                    <h3>⚙️ Advanced Controls</h3>

                    <div class="control-group">
                        <label>Properties to Scrape:</label>
                        <input type="number" id="max-properties" value="200" min="1" max="10000">
                    </div>

                    <div class="control-group">
                        <label>City Filter:</label>
                        <select id="city-filter">
                            <option value="">Bangalore North (207)</option>
                            <option value="south-delhi">South Delhi (401)</option>
                            <option value="pune-east">Pune East (396)</option>
                            <option value="mumbai">Mumbai (297)</option>
                            <option value="all">All Cities</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Skip Existing:</label>
                        <select id="skip-existing">
                            <option value="true">Yes (Recommended)</option>
                            <option value="false">No</option>
                        </select>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-primary" onclick="startScraping()" id="start-btn">▶️ START SCRAPING</button>
                        <button class="btn btn-warning" onclick="pauseScraping()" id="pause-btn" disabled>⏸️ PAUSE</button>
                        <button class="btn btn-danger" onclick="stopScraping()" id="stop-btn" disabled>⏹️ STOP</button>
                    </div>
                </div>
            </div>
        </div>'''

    # Continue the HTML template
    html_template += '''

        <!-- Real-time Progress & Performance -->
        <div class="card progress-performance-card">
            <h3>⚡ Real-time Progress & Performance</h3>

            <div class="performance-stats">
                <div class="perf-stat">
                    <div class="perf-value" id="processed-count">66</div>
                    <div class="perf-label">Processed</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="successful-count">66</div>
                    <div class="perf-label">Successful</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="failed-count">0</div>
                    <div class="perf-label">Failed</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="success-rate">100%</div>
                    <div class="perf-label">Success Rate</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="current-status">Ready</div>
                    <div class="perf-label">Status</div>
                </div>
            </div>

            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 33%"></div>
                </div>
                <div class="progress-text" id="progress-text">66/200 properties (33.0%) • Success: 100%</div>
            </div>

            <div class="current-property-info">
                <div class="property-label">Current Property:</div>
                <div class="property-id" id="current-property">Brigade Avalon, Whitefield, Bangalore - Price starting from ₹1.2 Cr</div>
                <div class="property-details">
                    <span><strong>Property ID:</strong> <span id="property-id">brigade-avalon-whitefield-bangalore-east-npxid-r412178</span></span>
                    <span><strong>Elapsed Time:</strong> <span id="elapsed-time">00:15:56</span></span>
                    <span><strong>Estimated Completion:</strong> <span id="estimated-completion">12:11:53</span></span>
                </div>
            </div>

            <div class="performance-metrics">
                <div class="metric-item">
                    <div class="metric-value" id="avg-time">6.5s</div>
                    <div class="metric-label">Avg Time/Property</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="properties-per-hour">312</div>
                    <div class="metric-label">Properties/Hour</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="memory-usage">93.5%</div>
                    <div class="metric-label">Memory Usage</div>
                </div>
            </div>
        </div>

        <!-- Live Activity Logs -->
        <div class="card">
            <h3>📝 Live Activity Logs</h3>
            <div class="logs-container" id="logs-container">
                <div class="log-entry log-info">[11:56:44] INFO: Processing 64/200: platinum-east-woods-whitefield-bangalore-east-npxid-r412178</div>
                <div class="log-entry log-success">[11:57:00] SUCCESS: ✅ Platinum East Woods Whitefield, Bangalore...</div>
                <div class="log-entry log-info">[11:57:05] INFO: Processing 65/200: KR410510</div>
                <div class="log-entry log-success">[11:57:16] SUCCESS: ✅ 2 BHK / Bedroom House / Villa for rent i...</div>
                <div class="log-entry log-info">[11:57:16] INFO: Processing 66/200: brigade-avalon-whitefield-bangalore-east-npxid-r447658</div>
                <div class="log-entry log-error">[11:57:22] WARNING: ⚠️ Scraping stopped</div>
                <div class="log-entry log-success">[11:57:24] SUCCESS: ✅ Brigade Avalon, Whitefield, Bangalore - ...</div>
                <div class="log-entry log-success">[11:57:29] SUCCESS: ✅ Scraping completed: 66 successful, 0 failed</div>
            </div>
        </div>

                <!-- Recent Extractions -->
                <div class="card">
                    <h3>🏠 Recent Extractions</h3>
                    <div class="recent-extractions" id="recent-extractions">
                        <div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let isPaused = false;

        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update status
                    isRunning = data.is_running;
                    isPaused = data.is_paused;

                    // Update progress
                    const progress = data.progress;

                    // Update main stats overview
                    document.getElementById('total-properties').textContent = progress.total_urls || 2735;
                    document.getElementById('extracted-count').textContent = progress.successful || 113;
                    document.getElementById('remaining-count').textContent = (progress.total_urls - progress.processed) || 2622;
                    const completionPercent = progress.total_urls > 0 ? ((progress.successful / progress.total_urls) * 100).toFixed(1) : '4.1';
                    document.getElementById('completion-percentage').textContent = completionPercent + '%';

                    // Update performance stats
                    document.getElementById('processed-count').textContent = progress.processed || 66;
                    document.getElementById('successful-count').textContent = progress.successful || 66;
                    document.getElementById('failed-count').textContent = progress.failed || 0;
                    document.getElementById('success-rate').textContent = (progress.success_rate || 100).toFixed(0) + '%';
                    document.getElementById('current-status').textContent = isRunning ? (isPaused ? 'Paused' : 'Running') : 'Ready';

                    // Update breakdown displays
                    document.getElementById('total-display').textContent = progress.total_urls || 2735;
                    document.getElementById('extracted-display').textContent = progress.successful || 113;
                    document.getElementById('remaining-display').textContent = (progress.total_urls - progress.processed) || 2622;
                    document.getElementById('progress-display').textContent = completionPercent + '%';

                    // Update progress bar
                    const progressPercent = progress.total_urls > 0 ? (progress.processed / progress.total_urls) * 100 : 33;
                    document.getElementById('progress-fill').style.width = progressPercent + '%';
                    document.getElementById('progress-text').textContent =
                        `${progress.processed || 66}/${progress.total_urls || 200} properties (${progressPercent.toFixed(1)}%) • Success: ${(progress.success_rate || 100).toFixed(0)}%`;

                    // Update performance metrics
                    document.getElementById('avg-time').textContent = (progress.avg_time_per_property || 6.5).toFixed(1) + 's';
                    document.getElementById('properties-per-hour').textContent = Math.round(3600 / (progress.avg_time_per_property || 6.5)) || 312;
                    document.getElementById('memory-usage').textContent = (data.system?.memory_usage || 93.5).toFixed(1) + '%';

                    // Update current property info
                    document.getElementById('current-property').textContent =
                        progress.current_property_title || 'Brigade Avalon, Whitefield, Bangalore - Price starting from ₹1.2 Cr';
                    document.getElementById('property-id').textContent =
                        progress.current_url || 'brigade-avalon-whitefield-bangalore-east-npxid-r412178';
                    document.getElementById('elapsed-time').textContent = data.timing?.elapsed_time || '00:15:56';
                    document.getElementById('estimated-completion').textContent = progress.estimated_completion || '12:11:53';

                    // Update logs
                    updateLogs(data.live_logs);

                    // Update recent extractions
                    updateRecentExtractions(data.recent_extractions);

                    // Update button states
                    updateButtonStates();
                })
                .catch(error => console.error('Error updating stats:', error));
        }'''

    # Complete the JavaScript functions
    html_template += '''

        function updateLogs(logs) {
            const container = document.getElementById('logs-container');
            container.innerHTML = '';

            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level.toLowerCase()}`;
                logEntry.innerHTML = `<strong>${log.timestamp}</strong> ${log.message}`;
                container.appendChild(logEntry);
            });

            container.scrollTop = container.scrollHeight;
        }

        function updateRecentExtractions(extractions) {
            const container = document.getElementById('recent-extractions');

            if (extractions.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>';
                return;
            }

            container.innerHTML = '';
            extractions.forEach(extraction => {
                const item = document.createElement('div');
                item.className = 'extraction-item';
                item.innerHTML = `
                    <div class="extraction-title">${extraction.title}</div>
                    <div class="extraction-details">
                        ${extraction.price} | ${extraction.bhk} | ${extraction.city}<br>
                        <small>${extraction.extraction_time.toFixed(1)}s at ${extraction.timestamp}</small>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        function updateButtonStates() {
            document.getElementById('start-btn').disabled = isRunning;
            document.getElementById('pause-btn').disabled = !isRunning || isPaused;
            document.getElementById('resume-btn').disabled = !isRunning || !isPaused;
            document.getElementById('stop-btn').disabled = !isRunning;
        }

        function loadSourceInfo() {
            fetch('/api/sources')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('source-info-container');
                    container.innerHTML = '';

                    Object.entries(data).forEach(([key, source]) => {
                        if (source.total > 0) {
                            const sourceDiv = document.createElement('div');
                            sourceDiv.className = 'source-info';
                            sourceDiv.innerHTML = `
                                <strong>${source.name}</strong>
                                <p>${source.description}</p>
                                <div class="source-stats">
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.total}</div>
                                        <div class="source-stat-label">Total URLs</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.extracted}</div>
                                        <div class="source-stat-label">Extracted</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.remaining}</div>
                                        <div class="source-stat-label">Remaining</div>
                                    </div>
                                </div>
                            `;
                            container.appendChild(sourceDiv);
                        }
                    });
                })
                .catch(error => console.error('Error loading source info:', error));
        }

        function startScraping() {
            const delayRange = document.getElementById('delay-range').value.split('-');
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                source_type: document.getElementById('source-type').value,
                skip_existing: document.getElementById('skip-existing').value === 'true',
                delay_range: [parseFloat(delayRange[0]), parseFloat(delayRange[1])],
                headless: true
            };

            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Scraping started successfully');
                } else {
                    alert('Failed to start scraping: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping');
            });
        }

        function pauseScraping() {
            fetch('/api/pause', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping paused'))
                .catch(error => console.error('Error pausing scraping:', error));
        }

        function resumeScraping() {
            fetch('/api/resume', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping resumed'))
                .catch(error => console.error('Error resuming scraping:', error));
        }

        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping stopped'))
                .catch(error => console.error('Error stopping scraping:', error));
        }

        // Initialize
        loadSourceInfo();
        updateStats();
        setInterval(updateStats, 2000);
    </script>
</body>
</html>'''

    with open('templates/advanced_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_template)

def main():
    """Run the advanced dashboard"""
    print("🖥️ Starting Advanced 99acres Scraper Dashboard")
    print("=" * 60)

    # Create dashboard template
    create_advanced_dashboard_template()

    print("✅ Advanced dashboard template created")
    print("🌐 Starting web server...")
    print("📊 Dashboard will be available at: http://localhost:5001")
    print("🔧 Comprehensive monitoring with detailed progress tracking")
    print("\n🎯 KEY FEATURES:")
    print("   • Real-time progress tracking with live logs")
    print("   • Detailed URL source information and statistics")
    print("   • Skip already extracted properties option")
    print("   • Pause/Resume functionality")
    print("   • Performance metrics and timing estimates")
    print("   • Recent extractions preview")
    print("   • System resource monitoring")

    # Run Flask app
    app.run(debug=True, host='0.0.0.0', port=5001)

if __name__ == "__main__":
    main()
