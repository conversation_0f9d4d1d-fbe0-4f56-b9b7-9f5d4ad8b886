#!/usr/bin/env python3
"""
Advanced Real-Time Scraper Dashboard
Comprehensive monitoring with detailed progress tracking, error handling, and controls
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
import threading
import time
import queue
from datetime import datetime, timedelta
import psutil
import logging
from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Tuple, List, Dict, Any
import random

app = Flask(__name__)

@dataclass
class OptimizedScrapingConfig:
    """Configuration for optimized concurrent scraping"""
    # Concurrent Processing
    max_concurrent_instances: int = 4
    enable_concurrent: bool = True

    # Time Controls
    base_delay_range: Tuple[float, float] = (1.5, 3.0)  # Optimized from 3s to 1.5-3s
    page_load_timeout: int = 15
    element_wait_timeout: int = 8

    # Browser Optimizations
    headless: bool = True
    enable_images: bool = False
    enable_css: bool = False
    disable_extensions: bool = True

    # Performance Settings
    batch_size: int = 20
    memory_cleanup_interval: int = 25
    retry_attempts: int = 2
    smart_delays: bool = True

class AdvancedScraperMonitor:
    """Advanced monitoring with detailed progress tracking"""
    
    def __init__(self):
        self.scraper = None
        self.scraper_thread = None
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.end_time = None

        # Optimization configuration
        self.config = OptimizedScrapingConfig()

        # Concurrent processing
        self.scrapers = {}  # Instance ID -> scraper
        self.executor = None
        self.futures = []
        
        # Detailed progress tracking with concurrent support
        self.progress = {
            'total_urls': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'current_url': '',
            'current_property_title': '',
            'avg_time_per_property': 0,
            'estimated_completion': None,
            'urls_per_minute': 0,
            'success_rate': 0,
            'concurrent_instances': 0,
            'fastest_instance': 0,
            'slowest_instance': 0,
            'instance_performance': {}
        }
        
        # Real-time logs
        self.live_logs = queue.Queue(maxsize=100)
        self.error_details = []
        self.recent_extractions = []
        
        # Configuration
        self.config = {
            'max_properties': 100,
            'source_type': 'individual_listings',  # or 'main_listings'
            'skip_existing': True,
            'delay_range': [2.0, 4.0],
            'headless': True
        }
        
        # Setup logging
        self.setup_logging()
    
    def setup_logging(self):
        """Setup detailed logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('advanced_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("AdvancedScraper")

    def create_optimized_scraper(self, instance_id=0):
        """Create optimized scraper with performance enhancements"""
        scraper = ComprehensiveIndividualListingScraper(headless=self.config.headless)

        # Apply browser optimizations
        if hasattr(scraper, 'setup_optimized_driver'):
            scraper.setup_optimized_driver(
                enable_images=self.config.enable_images,
                enable_css=self.config.enable_css,
                page_load_timeout=self.config.page_load_timeout
            )
        else:
            scraper.setup_driver()

        return scraper

    def add_log(self, level, message):
        """Add log entry to live logs"""
        log_entry = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'level': level,
            'message': message
        }
        
        try:
            self.live_logs.put_nowait(log_entry)
        except queue.Full:
            # Remove oldest entry and add new one
            try:
                self.live_logs.get_nowait()
                self.live_logs.put_nowait(log_entry)
            except queue.Empty:
                pass
    
    def get_url_sources(self):
        """Get available URL sources and counts"""
        sources = {}

        try:
            # Individual listings from main database
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()

            # Get total available URLs
            cursor.execute("""
                SELECT COUNT(*) FROM properties
                WHERE property_url IS NOT NULL AND property_url != ''
                AND property_url NOT LIKE '%httpswww%'
            """)
            individual_total = cursor.fetchone()[0]
            conn.close()

            # Check how many already extracted from individual properties database
            individual_extracted = 0
            try:
                conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT COUNT(*) FROM individual_properties")
                individual_extracted = cursor2.fetchone()[0]
                conn2.close()
            except:
                individual_extracted = 0

            sources['individual_listings'] = {
                'name': 'Individual Property Listings',
                'total': individual_total,
                'extracted': individual_extracted,
                'remaining': individual_total - individual_extracted,
                'description': 'Detailed individual property pages with comprehensive data (67+ fields per property)'
            }

            # Main listings (if available)
            sources['main_listings'] = {
                'name': 'Main Property Listings',
                'total': 0,
                'extracted': 0,
                'remaining': 0,
                'description': 'Property listing pages (not yet implemented)'
            }

        except Exception as e:
            self.logger.error(f"Error getting URL sources: {str(e)}")

        return sources
    
    def start_scraping(self, config_data):
        """Start scraping with comprehensive tracking and concurrent processing"""
        if self.is_running:
            return False, "Scraper is already running"

        try:
            # Update configuration with optimizations
            self.config.update(config_data)

            # Apply optimization settings
            if 'enable_concurrent' in config_data:
                self.config.enable_concurrent = config_data['enable_concurrent']
            if 'max_concurrent_instances' in config_data:
                self.config.max_concurrent_instances = config_data['max_concurrent_instances']
            if 'base_delay_range' in config_data:
                self.config.base_delay_range = tuple(config_data['base_delay_range'])
            if 'enable_images' in config_data:
                self.config.enable_images = config_data['enable_images']
            if 'enable_css' in config_data:
                self.config.enable_css = config_data['enable_css']
            if 'headless' in config_data:
                self.config.headless = config_data['headless']
            if 'page_load_timeout' in config_data:
                self.config.page_load_timeout = config_data['page_load_timeout']
            if 'batch_size' in config_data:
                self.config.batch_size = config_data['batch_size']
            if 'memory_cleanup_interval' in config_data:
                self.config.memory_cleanup_interval = config_data['memory_cleanup_interval']
            if 'retry_attempts' in config_data:
                self.config.retry_attempts = config_data['retry_attempts']
            if 'smart_delays' in config_data:
                self.config.smart_delays = config_data['smart_delays']
            if 'anti_detection' in config_data:
                self.config.user_agent_rotation = config_data['anti_detection']
                self.config.viewport_randomization = config_data['anti_detection']
            if 'enable_javascript' in config_data:
                self.config.enable_javascript = config_data['enable_javascript']

            # Initialize scraper (will be replaced by concurrent scrapers if enabled)
            self.scraper = self.create_optimized_scraper()
            
            # Reset progress
            self.progress = {
                'total_urls': 0,
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0,
                'current_url': '',
                'current_property_title': '',
                'avg_time_per_property': 0,
                'estimated_completion': None,
                'urls_per_minute': 0,
                'success_rate': 0
            }
            
            # Clear logs
            while not self.live_logs.empty():
                try:
                    self.live_logs.get_nowait()
                except queue.Empty:
                    break
            
            self.error_details = []
            self.recent_extractions = []
            
            # Start scraping thread
            self.scraper_thread = threading.Thread(target=self._run_scraping_with_tracking)
            self.scraper_thread.daemon = True
            self.scraper_thread.start()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            self.add_log('INFO', f"Started scraping {self.config['max_properties']} properties")
            return True, "Scraping started successfully"
            
        except Exception as e:
            self.add_log('ERROR', f"Failed to start scraping: {str(e)}")
            return False, f"Failed to start scraping: {str(e)}"
    
    def _run_scraping_with_tracking(self):
        """Run scraping with detailed progress tracking and concurrent processing"""
        try:
            # Load URLs based on source type
            if self.config['source_type'] == 'individual_listings':
                urls = self._load_individual_listing_urls()
            else:
                urls = []  # Main listings not implemented yet

            if not urls:
                self.add_log('ERROR', "No URLs found to scrape")
                return

            self.progress['total_urls'] = len(urls)
            self.add_log('INFO', f"Loaded {len(urls)} URLs for scraping")

            # Choose processing method based on configuration
            if self.config.enable_concurrent and len(urls) > 10:
                self.add_log('INFO', f"🚀 Starting CONCURRENT processing with {self.config.max_concurrent_instances} instances")
                self._run_concurrent_scraping(urls)
            else:
                self.add_log('INFO', f"📝 Starting SEQUENTIAL processing")
                self._run_sequential_scraping(urls)

        except Exception as e:
            self.add_log('ERROR', f"Scraping error: {str(e)}")
        finally:
            self.is_running = False
            self.end_time = datetime.now()
            self.add_log('INFO', "Scraping completed")

    def _run_concurrent_scraping(self, urls):
        """Run concurrent scraping with multiple instances"""
        try:
            # Split URLs among instances
            urls_per_instance = len(urls) // self.config.max_concurrent_instances
            url_chunks = []

            for i in range(self.config.max_concurrent_instances):
                start_idx = i * urls_per_instance
                end_idx = start_idx + urls_per_instance if i < self.config.max_concurrent_instances - 1 else len(urls)
                if start_idx < len(urls):
                    url_chunks.append(urls[start_idx:end_idx])

            self.progress['concurrent_instances'] = len(url_chunks)

            # Start concurrent processing
            with ThreadPoolExecutor(max_workers=self.config.max_concurrent_instances) as executor:
                futures = []

                for i, url_chunk in enumerate(url_chunks):
                    if url_chunk:
                        future = executor.submit(self._scrape_instance_worker, i, url_chunk)
                        futures.append(future)

                # Monitor progress
                completed = 0
                for future in as_completed(futures):
                    try:
                        instance_results = future.result()
                        completed += 1
                        self.add_log('SUCCESS', f"✅ Instance {completed}/{len(futures)} completed")
                    except Exception as e:
                        self.add_log('ERROR', f"❌ Instance failed: {str(e)}")

        except Exception as e:
            self.add_log('ERROR', f"Concurrent scraping error: {str(e)}")

    def _scrape_instance_worker(self, instance_id, urls):
        """Worker function for concurrent scraping instance"""
        try:
            self.add_log('INFO', f"🔧 Instance {instance_id} starting with {len(urls)} URLs")

            # Create optimized scraper for this instance
            scraper = self.create_optimized_scraper(instance_id)

            instance_successful = 0
            instance_failed = 0

            for i, url in enumerate(urls):
                if not self.is_running:
                    break

                try:
                    # Extract data with optimized timing
                    start_time = time.time()
                    property_data = scraper.extract_comprehensive_property_data(url)
                    extraction_time = time.time() - start_time

                    if property_data:
                        if scraper.save_to_database(property_data):
                            instance_successful += 1
                            self.progress['successful'] += 1
                            self.add_log('SUCCESS', f"Instance {instance_id}: ✅ {property_data.get('title', 'Unknown')[:40]}... ({extraction_time:.1f}s)")
                        else:
                            instance_failed += 1
                            self.progress['failed'] += 1
                    else:
                        instance_failed += 1
                        self.progress['failed'] += 1

                    self.progress['processed'] += 1

                    # Optimized delay
                    delay = random.uniform(*self.config.base_delay_range)
                    time.sleep(delay)

                except Exception as e:
                    instance_failed += 1
                    self.progress['failed'] += 1
                    self.add_log('ERROR', f"Instance {instance_id}: ❌ {str(e)}")

            # Cleanup
            if scraper.driver:
                scraper.driver.quit()

            self.add_log('SUCCESS', f"🎯 Instance {instance_id} completed: {instance_successful} successful, {instance_failed} failed")
            return {'successful': instance_successful, 'failed': instance_failed}

        except Exception as e:
            self.add_log('ERROR', f"Instance {instance_id} worker error: {str(e)}")
            return {'successful': 0, 'failed': len(urls)}

    def _run_sequential_scraping(self, urls):
        """Run sequential scraping with optimized timing"""
        try:
            # Setup scraper
            if not self.scraper.setup_driver():
                self.add_log('ERROR', "Failed to setup browser driver")
                return

            self.add_log('SUCCESS', "Browser setup successful")

            successful = 0
            failed = 0

            for i, url in enumerate(urls, 1):
                if not self.is_running:
                    break

                while self.is_paused and self.is_running:
                    time.sleep(1)

                if not self.is_running:
                    break

                try:
                    start_time = time.time()
                    self.progress['current_url'] = url

                    # Extract data
                    property_data = self.scraper.extract_comprehensive_property_data(url)
                    extraction_time = time.time() - start_time

                    if property_data:
                        if self.scraper.save_to_database(property_data):
                            successful += 1
                            self.progress['successful'] = successful
                            self.progress['current_property_title'] = property_data.get('title', 'Unknown')[:50]
                            self.add_log('SUCCESS', f"✅ {i}/{len(urls)}: {property_data.get('title', 'Unknown')[:40]}... ({extraction_time:.1f}s)")
                        else:
                            failed += 1
                            self.progress['failed'] = failed
                            self.add_log('ERROR', f"❌ {i}/{len(urls)}: Database save failed")
                    else:
                        failed += 1
                        self.progress['failed'] = failed
                        self.add_log('ERROR', f"❌ {i}/{len(urls)}: No data extracted")

                    self.progress['processed'] = i

                    # Calculate performance metrics
                    if i > 0:
                        elapsed = (datetime.now() - self.start_time).total_seconds()
                        self.progress['avg_time_per_property'] = elapsed / i
                        self.progress['urls_per_minute'] = (i / elapsed) * 60 if elapsed > 0 else 0
                        self.progress['success_rate'] = (successful / i) * 100

                        # Estimate completion
                        if self.progress['avg_time_per_property'] > 0:
                            remaining_time = (len(urls) - i) * self.progress['avg_time_per_property']
                            completion_time = datetime.now() + timedelta(seconds=remaining_time)
                            self.progress['estimated_completion'] = completion_time.strftime("%H:%M:%S")

                    # Optimized delay
                    delay = random.uniform(*self.config.base_delay_range)
                    time.sleep(delay)

                except Exception as e:
                    failed += 1
                    self.progress['failed'] = failed
                    self.add_log('ERROR', f"❌ {i}/{len(urls)}: {str(e)}")

            self.add_log('SUCCESS', f"🎯 Sequential scraping completed: {successful} successful, {failed} failed")

        except Exception as e:
            self.add_log('ERROR', f"Sequential scraping error: {str(e)}")
        finally:
            if self.scraper and self.scraper.driver:
                try:
                    self.scraper.driver.quit()
                except:
                    pass
            
            # Setup scraper
            if not self.scraper.setup_driver():
                self.add_log('ERROR', "Failed to setup browser driver")
                return
            
            # Process each URL
            extraction_times = []
            
            for i, url in enumerate(urls, 1):
                if not self.is_running:  # Check if stopped
                    break
                
                while self.is_paused:  # Handle pause
                    time.sleep(1)
                
                self.progress['processed'] = i
                self.progress['current_url'] = url
                
                # Check if already extracted (if skip_existing enabled)
                if self.config['skip_existing'] and self._is_already_extracted(url):
                    self.progress['skipped'] += 1
                    self.add_log('INFO', f"Skipped (already extracted): {url}")
                    continue
                
                # Extract property data
                start_time = time.time()
                self.add_log('INFO', f"Processing {i}/{len(urls)}: {url}")
                
                try:
                    property_data = self.scraper.extract_comprehensive_property_data(url)
                    extraction_time = time.time() - start_time
                    extraction_times.append(extraction_time)
                    
                    if property_data:
                        # Save to database
                        if self.scraper.save_to_database(property_data):
                            self.progress['successful'] += 1
                            self.progress['current_property_title'] = property_data.get('title', 'Unknown')
                            
                            # Add to recent extractions
                            self.recent_extractions.append({
                                'title': property_data.get('title', 'Unknown')[:50],
                                'price': property_data.get('price_display', 'N/A'),
                                'bhk': property_data.get('bhk_config', 'N/A'),
                                'city': property_data.get('city', 'N/A'),
                                'extraction_time': extraction_time,
                                'timestamp': datetime.now().strftime("%H:%M:%S")
                            })
                            
                            # Keep only last 10
                            if len(self.recent_extractions) > 10:
                                self.recent_extractions.pop(0)
                            
                            self.add_log('SUCCESS', f"Extracted: {property_data.get('title', 'Unknown')[:50]}...")
                        else:
                            self.progress['failed'] += 1
                            self.add_log('ERROR', f"Failed to save to database: {url}")
                    else:
                        self.progress['failed'] += 1
                        self.add_log('ERROR', f"Failed to extract data: {url}")
                
                except Exception as e:
                    self.progress['failed'] += 1
                    error_msg = str(e)
                    self.add_log('ERROR', f"Extraction error: {error_msg}")
                    
                    # Add to error details
                    self.error_details.append({
                        'url': url,
                        'error': error_msg,
                        'timestamp': datetime.now().strftime("%H:%M:%S")
                    })
                
                # Update performance metrics
                if extraction_times:
                    self.progress['avg_time_per_property'] = sum(extraction_times) / len(extraction_times)
                    
                    # Calculate URLs per minute
                    elapsed_minutes = (datetime.now() - self.start_time).total_seconds() / 60
                    if elapsed_minutes > 0:
                        self.progress['urls_per_minute'] = self.progress['processed'] / elapsed_minutes
                    
                    # Estimate completion time
                    remaining = self.progress['total_urls'] - self.progress['processed']
                    if self.progress['urls_per_minute'] > 0:
                        remaining_minutes = remaining / self.progress['urls_per_minute']
                        self.progress['estimated_completion'] = (datetime.now() + timedelta(minutes=remaining_minutes)).strftime("%H:%M:%S")
                
                # Calculate success rate
                if self.progress['processed'] > 0:
                    self.progress['success_rate'] = (self.progress['successful'] / (self.progress['processed'] - self.progress['skipped'])) * 100
                
                # Intelligent delay
                delay = self.config['delay_range'][0] + (self.config['delay_range'][1] - self.config['delay_range'][0]) * 0.5
                time.sleep(delay)
            
            self.add_log('INFO', f"Scraping completed: {self.progress['successful']} successful, {self.progress['failed']} failed")
            
        except Exception as e:
            self.add_log('ERROR', f"Scraping thread error: {str(e)}")
        finally:
            self.is_running = False
            self.end_time = datetime.now()
            if self.scraper and self.scraper.driver:
                self.scraper.driver.quit()
    
    def _load_individual_listing_urls(self):
        """Load individual listing URLs from main database"""
        try:
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()

            if self.config['skip_existing']:
                # Get already extracted URLs first
                try:
                    conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                    cursor2 = conn2.cursor()
                    cursor2.execute("SELECT property_url FROM individual_properties WHERE property_url IS NOT NULL")
                    extracted_urls = set(row[0] for row in cursor2.fetchall())
                    conn2.close()

                    # Get all URLs and filter out extracted ones
                    cursor.execute("""
                        SELECT property_url
                        FROM properties
                        WHERE property_url IS NOT NULL AND property_url != ''
                        AND property_url NOT LIKE '%httpswww%'
                        ORDER BY RANDOM()
                    """)
                    all_urls = [row[0] for row in cursor.fetchall()]

                    # Filter out already extracted URLs
                    urls = [url for url in all_urls if url not in extracted_urls][:self.config['max_properties']]

                except Exception as e:
                    self.add_log('WARNING', f"Could not check extracted URLs: {str(e)}")
                    # Fallback to all URLs
                    cursor.execute("""
                        SELECT property_url
                        FROM properties
                        WHERE property_url IS NOT NULL AND property_url != ''
                        AND property_url NOT LIKE '%httpswww%'
                        ORDER BY RANDOM() LIMIT ?
                    """, (self.config['max_properties'],))
                    urls = [row[0] for row in cursor.fetchall()]
            else:
                # Get all URLs
                cursor.execute("""
                    SELECT property_url
                    FROM properties
                    WHERE property_url IS NOT NULL AND property_url != ''
                    AND property_url NOT LIKE '%httpswww%'
                    ORDER BY RANDOM() LIMIT ?
                """, (self.config['max_properties'],))
                urls = [row[0] for row in cursor.fetchall()]
            conn.close()

            self.add_log('INFO', f"Loaded {len(urls)} URLs from main database")
            return urls

        except Exception as e:
            self.add_log('ERROR', f"Error loading URLs: {str(e)}")
            self.logger.error(f"Error loading URLs: {str(e)}")
            return []
    
    def _is_already_extracted(self, url):
        """Check if URL is already extracted"""
        try:
            conn = sqlite3.connect('data/individual_properties_comprehensive.db')
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM individual_properties WHERE property_url = ?", (url,))
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except:
            return False
    
    def pause_scraping(self):
        """Pause scraping"""
        self.is_paused = True
        self.add_log('INFO', "Scraping paused")
        return True, "Scraping paused"
    
    def resume_scraping(self):
        """Resume scraping"""
        self.is_paused = False
        self.add_log('INFO', "Scraping resumed")
        return True, "Scraping resumed"
    
    def stop_scraping(self):
        """Stop scraping"""
        self.is_running = False
        self.is_paused = False
        self.add_log('INFO', "Scraping stopped")
        return True, "Scraping stopped"
    
    def get_current_stats(self):
        """Get comprehensive current statistics"""
        # System stats
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent()
        
        # Get live logs
        logs = []
        temp_logs = []
        while not self.live_logs.empty():
            try:
                temp_logs.append(self.live_logs.get_nowait())
            except queue.Empty:
                break
        
        # Put logs back and return them
        for log in temp_logs:
            try:
                self.live_logs.put_nowait(log)
                logs.append(log)
            except queue.Full:
                break
        
        # Reverse to show newest first
        logs.reverse()
        
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'progress': self.progress,
            'system': {
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage
            },
            'timing': {
                'start_time': self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                'end_time': self.end_time.strftime("%H:%M:%S") if self.end_time else None,
                'elapsed_time': str(datetime.now() - self.start_time).split('.')[0] if self.start_time else None
            },
            'live_logs': logs[-20:],  # Last 20 logs
            'error_details': self.error_details[-10:],  # Last 10 errors
            'recent_extractions': self.recent_extractions,
            'config': self.config
        }

# Global monitor instance
monitor = AdvancedScraperMonitor()

@app.route('/')
def dashboard():
    """Advanced dashboard page"""
    return render_template('advanced_dashboard.html')

@app.route('/api/stats')
def get_stats():
    """API endpoint for comprehensive statistics"""
    return jsonify(monitor.get_current_stats())

@app.route('/api/sources')
def get_sources():
    """API endpoint for URL sources"""
    return jsonify(monitor.get_url_sources())

@app.route('/api/start', methods=['POST'])
def start_scraping():
    """API endpoint to start scraping"""
    config_data = request.json
    success, message = monitor.start_scraping(config_data)
    return jsonify({'success': success, 'message': message})

@app.route('/api/pause', methods=['POST'])
def pause_scraping():
    """API endpoint to pause scraping"""
    success, message = monitor.pause_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/resume', methods=['POST'])
def resume_scraping():
    """API endpoint to resume scraping"""
    success, message = monitor.resume_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_scraping():
    """API endpoint to stop scraping"""
    success, message = monitor.stop_scraping()
    return jsonify({'success': success, 'message': message})

def create_advanced_dashboard_template():
    """Create the advanced dashboard HTML template"""
    os.makedirs('templates', exist_ok=True)

    html_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced 99acres Scraper Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f0f2f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }

        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }

        .main-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px; }
        .left-panel { display: flex; flex-direction: column; gap: 20px; }
        .right-panel { display: flex; flex-direction: column; gap: 20px; }

        .card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .card h3 { color: #333; margin-bottom: 15px; font-size: 1.3em; }

        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-item { text-align: center; padding: 15px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }

        .progress-section { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 25px; background: #e9ecef; border-radius: 15px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s ease; border-radius: 15px; }
        .progress-text { text-align: center; margin-top: 10px; font-weight: bold; }

        .control-panel { background: white; border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .control-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .control-group { display: flex; flex-direction: column; }
        .control-group label { margin-bottom: 8px; font-weight: bold; color: #333; }
        .control-group input, .control-group select { padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; }
        .control-group input:focus, .control-group select:focus { outline: none; border-color: #667eea; }
        .control-group small { color: #6c757d; font-size: 12px; margin-top: 4px; font-style: italic; }

        .optimization-controls { background: linear-gradient(135deg, #f8f9ff, #e8f2ff); border-radius: 12px; padding: 20px; margin-top: 20px; border: 1px solid #e3f2fd; }
        .optimization-controls h4 { color: #1976d2; margin-bottom: 15px; font-size: 16px; }
        .optimization-controls .control-group { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); }
        .optimization-controls input[type="checkbox"] { width: auto; margin-right: 8px; }
        .optimization-controls label { display: flex; align-items: center; margin-bottom: 5px; }

        .preset-buttons { display: flex; gap: 10px; margin-bottom: 25px; flex-wrap: wrap; }
        .preset-buttons .btn { font-size: 13px; padding: 10px 16px; }
        .btn-secondary { background: #6c757d; color: white; }

        .button-group { display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px; transition: all 0.3s ease; }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }

        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #28a745; animation: pulse 2s infinite; }
        .status-paused { background: #ffc107; }
        .status-stopped { background: #dc3545; }

        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }

        .logs-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; border-radius: 10px; padding: 15px; }
        .log-entry { padding: 8px 12px; margin-bottom: 5px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 13px; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }

        .recent-extractions { max-height: 300px; overflow-y: auto; }
        .extraction-item { padding: 12px; margin-bottom: 8px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .extraction-title { font-weight: bold; color: #333; margin-bottom: 4px; }
        .extraction-details { font-size: 12px; color: #666; }

        .source-info { background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 15px; }
        .source-stats { display: flex; justify-content: space-between; margin-top: 10px; }
        .source-stat { text-align: center; }
        .source-stat-value { font-size: 1.5em; font-weight: bold; color: #1976d2; }
        .source-stat-label { font-size: 0.9em; color: #666; }

        @media (max-width: 768px) {
            .main-grid { grid-template-columns: 1fr; }
            .control-grid { grid-template-columns: 1fr; }
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Advanced 99acres Scraper Dashboard</h1>
            <p>Comprehensive real-time monitoring with detailed progress tracking and intelligent controls</p>
        </div>

        <div class="control-panel">
            <h3>🔧 Scraper Configuration & Control</h3>

            <div id="source-selection">
                <h4>📊 Data Source Information</h4>
                <div id="source-info-container">Loading source information...</div>
            </div>

            <div class="control-grid">
                <div class="control-group">
                    <label>Max Properties to Scrape:</label>
                    <input type="number" id="max-properties" value="100" min="1" max="10000">
                </div>
                <div class="control-group">
                    <label>Source Type:</label>
                    <select id="source-type">
                        <option value="individual_listings">Individual Property Listings</option>
                        <option value="main_listings" disabled>Main Listings (Coming Soon)</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>Skip Already Extracted:</label>
                    <select id="skip-existing">
                        <option value="true">Yes (Recommended)</option>
                        <option value="false">No</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>Delay Range (seconds):</label>
                    <input type="text" id="delay-range" value="1.5-3.0" placeholder="min-max">
                </div>
            </div>

            <div class="optimization-controls">
                <h4>🚀 Quick Performance Presets</h4>
                <div class="preset-buttons">
                    <button type="button" class="btn btn-success" onclick="applyPreset('maximum')">🏃‍♂️ Maximum Speed</button>
                    <button type="button" class="btn btn-primary" onclick="applyPreset('balanced')">⚖️ Balanced (Recommended)</button>
                    <button type="button" class="btn btn-warning" onclick="applyPreset('safe')">🛡️ Safe & Stable</button>
                    <button type="button" class="btn btn-secondary" onclick="applyPreset('conservative')">🐌 Conservative</button>
                </div>

                <h4>⚡ Performance Optimizations</h4>
                <div class="control-grid">
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-concurrent" checked>
                            Enable Concurrent Processing
                        </label>
                        <small>Use multiple browser instances for faster scraping</small>
                    </div>
                    <div class="control-group">
                        <label>Concurrent Instances:</label>
                        <select id="concurrent-instances">
                            <option value="2">2 Instances</option>
                            <option value="3">3 Instances</option>
                            <option value="4" selected>4 Instances (Recommended)</option>
                            <option value="6">6 Instances (High-end)</option>
                            <option value="8">8 Instances (Maximum)</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="disable-images" checked>
                            Disable Images
                        </label>
                        <small>Faster loading, less bandwidth</small>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="disable-css">
                            Disable CSS
                        </label>
                        <small>Maximum speed (may affect extraction)</small>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="headless-mode" checked>
                            Headless Mode
                        </label>
                        <small>Run browsers in background (faster)</small>
                    </div>
                    <div class="control-group">
                        <label>Page Load Timeout (seconds):</label>
                        <select id="page-timeout">
                            <option value="10">10 seconds (Fast)</option>
                            <option value="15" selected>15 seconds (Recommended)</option>
                            <option value="20">20 seconds (Safe)</option>
                            <option value="30">30 seconds (Slow connections)</option>
                        </select>
                    </div>
                </div>

                <h4>🎯 Advanced Controls</h4>
                <div class="control-grid">
                    <div class="control-group">
                        <label>Batch Size:</label>
                        <select id="batch-size">
                            <option value="10">10 properties</option>
                            <option value="20" selected>20 properties (Recommended)</option>
                            <option value="50">50 properties</option>
                            <option value="100">100 properties (Large batches)</option>
                        </select>
                        <small>Properties processed per batch</small>
                    </div>
                    <div class="control-group">
                        <label>Memory Cleanup Interval:</label>
                        <select id="memory-cleanup">
                            <option value="15">Every 15 properties</option>
                            <option value="25" selected>Every 25 properties (Recommended)</option>
                            <option value="50">Every 50 properties</option>
                            <option value="100">Every 100 properties</option>
                        </select>
                        <small>Browser memory cleanup frequency</small>
                    </div>
                    <div class="control-group">
                        <label>Retry Attempts:</label>
                        <select id="retry-attempts">
                            <option value="1">1 attempt (Fast)</option>
                            <option value="2" selected>2 attempts (Recommended)</option>
                            <option value="3">3 attempts (Safe)</option>
                            <option value="5">5 attempts (Maximum)</option>
                        </select>
                        <small>Retries for failed properties</small>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="smart-delays" checked>
                            Smart Adaptive Delays
                        </label>
                        <small>Automatically adjust delays based on performance</small>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="anti-detection" checked>
                            Anti-Detection Features
                        </label>
                        <small>User agent rotation, viewport randomization</small>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-javascript" checked>
                            Enable JavaScript
                        </label>
                        <small>Required for dynamic content (recommended)</small>
                    </div>
                </div>
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="startScraping()" id="start-btn">▶️ Start Scraping</button>
                <button class="btn btn-warning" onclick="pauseScraping()" id="pause-btn" disabled>⏸️ Pause</button>
                <button class="btn btn-success" onclick="resumeScraping()" id="resume-btn" disabled>▶️ Resume</button>
                <button class="btn btn-danger" onclick="stopScraping()" id="stop-btn" disabled>⏹️ Stop</button>
            </div>
        </div>'''

    # Continue the HTML template
    html_template += '''

        <div class="main-grid">
            <div class="left-panel">
                <div class="card">
                    <h3>📊 Progress Overview</h3>
                    <div class="progress-section">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="processed-count">0</div>
                                <div class="stat-label">Processed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="successful-count">0</div>
                                <div class="stat-label">Successful</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="failed-count">0</div>
                                <div class="stat-label">Failed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="skipped-count">0</div>
                                <div class="stat-label">Skipped</div>
                            </div>
                        </div>

                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text" id="progress-text">Ready to start</div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="success-rate">0%</div>
                            <div class="stat-label">Success Rate</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avg-time">0s</div>
                            <div class="stat-label">Avg Time/Property</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="urls-per-minute">0</div>
                            <div class="stat-label">URLs/Minute</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="estimated-completion">--:--</div>
                            <div class="stat-label">Est. Completion</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>📝 Live Activity Logs</h3>
                    <div class="logs-container" id="logs-container">
                        <div class="log-entry log-info">Ready to start scraping...</div>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="card">
                    <h3>
                        <span class="status-indicator" id="status-indicator" class="status-stopped"></span>
                        Status & Timing
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="current-status">Stopped</div>
                            <div class="stat-label">Current Status</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="elapsed-time">00:00:00</div>
                            <div class="stat-label">Elapsed Time</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="memory-usage">0%</div>
                            <div class="stat-label">Memory Usage</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="cpu-usage">0%</div>
                            <div class="stat-label">CPU Usage</div>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <strong>Current Property:</strong>
                        <div id="current-property" style="font-size: 14px; color: #666; margin-top: 5px;">None</div>
                    </div>
                </div>

                <div class="card">
                    <h3>🏠 Recent Extractions</h3>
                    <div class="recent-extractions" id="recent-extractions">
                        <div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let isPaused = false;

        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update status
                    isRunning = data.is_running;
                    isPaused = data.is_paused;

                    // Update progress
                    const progress = data.progress;
                    document.getElementById('processed-count').textContent = progress.processed;
                    document.getElementById('successful-count').textContent = progress.successful;
                    document.getElementById('failed-count').textContent = progress.failed;
                    document.getElementById('skipped-count').textContent = progress.skipped;

                    // Update concurrent processing info
                    if (progress.concurrent_instances > 0) {
                        document.getElementById('status-text').textContent =
                            `Running (${progress.concurrent_instances} concurrent instances)`;
                    }

                    // Update progress bar
                    const progressPercent = progress.total_urls > 0 ? (progress.processed / progress.total_urls) * 100 : 0;
                    document.getElementById('progress-fill').style.width = progressPercent + '%';
                    document.getElementById('progress-text').textContent =
                        `${progress.processed}/${progress.total_urls} properties (${progressPercent.toFixed(1)}%)`;

                    // Update performance metrics
                    document.getElementById('success-rate').textContent = progress.success_rate.toFixed(1) + '%';
                    document.getElementById('avg-time').textContent = progress.avg_time_per_property.toFixed(1) + 's';
                    document.getElementById('urls-per-minute').textContent = progress.urls_per_minute.toFixed(1);
                    document.getElementById('estimated-completion').textContent = progress.estimated_completion || '--:--';

                    // Update status indicator
                    const statusIndicator = document.getElementById('status-indicator');
                    const currentStatus = document.getElementById('current-status');

                    if (isRunning && !isPaused) {
                        statusIndicator.className = 'status-indicator status-running';
                        currentStatus.textContent = 'Running';
                    } else if (isRunning && isPaused) {
                        statusIndicator.className = 'status-indicator status-paused';
                        currentStatus.textContent = 'Paused';
                    } else {
                        statusIndicator.className = 'status-indicator status-stopped';
                        currentStatus.textContent = 'Stopped';
                    }

                    // Update timing
                    document.getElementById('elapsed-time').textContent = data.timing.elapsed_time || '00:00:00';

                    // Update system stats
                    document.getElementById('memory-usage').textContent = data.system.memory_usage.toFixed(1) + '%';
                    document.getElementById('cpu-usage').textContent = data.system.cpu_usage.toFixed(1) + '%';

                    // Update current property
                    document.getElementById('current-property').textContent =
                        progress.current_property_title || progress.current_url || 'None';

                    // Update logs
                    updateLogs(data.live_logs);

                    // Update recent extractions
                    updateRecentExtractions(data.recent_extractions);

                    // Update button states
                    updateButtonStates();
                })
                .catch(error => console.error('Error updating stats:', error));
        }'''

    # Complete the JavaScript functions
    html_template += '''

        function updateLogs(logs) {
            const container = document.getElementById('logs-container');
            container.innerHTML = '';

            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level.toLowerCase()}`;
                logEntry.innerHTML = `<strong>${log.timestamp}</strong> ${log.message}`;
                container.appendChild(logEntry);
            });

            container.scrollTop = container.scrollHeight;
        }

        function updateRecentExtractions(extractions) {
            const container = document.getElementById('recent-extractions');

            if (extractions.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>';
                return;
            }

            container.innerHTML = '';
            extractions.forEach(extraction => {
                const item = document.createElement('div');
                item.className = 'extraction-item';
                item.innerHTML = `
                    <div class="extraction-title">${extraction.title}</div>
                    <div class="extraction-details">
                        ${extraction.price} | ${extraction.bhk} | ${extraction.city}<br>
                        <small>${extraction.extraction_time.toFixed(1)}s at ${extraction.timestamp}</small>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        function updateButtonStates() {
            document.getElementById('start-btn').disabled = isRunning;
            document.getElementById('pause-btn').disabled = !isRunning || isPaused;
            document.getElementById('resume-btn').disabled = !isRunning || !isPaused;
            document.getElementById('stop-btn').disabled = !isRunning;
        }

        function loadSourceInfo() {
            fetch('/api/sources')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('source-info-container');
                    container.innerHTML = '';

                    Object.entries(data).forEach(([key, source]) => {
                        if (source.total > 0) {
                            const sourceDiv = document.createElement('div');
                            sourceDiv.className = 'source-info';
                            sourceDiv.innerHTML = `
                                <strong>${source.name}</strong>
                                <p>${source.description}</p>
                                <div class="source-stats">
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.total}</div>
                                        <div class="source-stat-label">Total URLs</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.extracted}</div>
                                        <div class="source-stat-label">Extracted</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.remaining}</div>
                                        <div class="source-stat-label">Remaining</div>
                                    </div>
                                </div>
                            `;
                            container.appendChild(sourceDiv);
                        }
                    });
                })
                .catch(error => console.error('Error loading source info:', error));
        }

        function startScraping() {
            const delayRange = document.getElementById('delay-range').value.split('-');
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                source_type: document.getElementById('source-type').value,
                skip_existing: document.getElementById('skip-existing').value === 'true',
                delay_range: [parseFloat(delayRange[0]), parseFloat(delayRange[1])],

                // Performance Optimizations
                enable_concurrent: document.getElementById('enable-concurrent').checked,
                max_concurrent_instances: parseInt(document.getElementById('concurrent-instances').value),
                enable_images: !document.getElementById('disable-images').checked,
                enable_css: !document.getElementById('disable-css').checked,
                headless: document.getElementById('headless-mode').checked,
                page_load_timeout: parseInt(document.getElementById('page-timeout').value),

                // Advanced Controls
                batch_size: parseInt(document.getElementById('batch-size').value),
                memory_cleanup_interval: parseInt(document.getElementById('memory-cleanup').value),
                retry_attempts: parseInt(document.getElementById('retry-attempts').value),
                smart_delays: document.getElementById('smart-delays').checked,
                anti_detection: document.getElementById('anti-detection').checked,
                enable_javascript: document.getElementById('enable-javascript').checked,

                // Base delay range for optimization
                base_delay_range: [parseFloat(delayRange[0]), parseFloat(delayRange[1])]
            };

            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Scraping started successfully');
                } else {
                    alert('Failed to start scraping: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping');
            });
        }

        function pauseScraping() {
            fetch('/api/pause', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping paused'))
                .catch(error => console.error('Error pausing scraping:', error));
        }

        function resumeScraping() {
            fetch('/api/resume', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping resumed'))
                .catch(error => console.error('Error resuming scraping:', error));
        }

        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping stopped'))
                .catch(error => console.error('Error stopping scraping:', error));
        }

        function applyPreset(presetName) {
            const presets = {
                'maximum': {
                    'enable-concurrent': true,
                    'concurrent-instances': '6',
                    'disable-images': true,
                    'disable-css': true,
                    'headless-mode': true,
                    'page-timeout': '10',
                    'batch-size': '50',
                    'memory-cleanup': '15',
                    'retry-attempts': '1',
                    'smart-delays': true,
                    'anti-detection': false,
                    'delay-range': '1.0-2.0'
                },
                'balanced': {
                    'enable-concurrent': true,
                    'concurrent-instances': '4',
                    'disable-images': true,
                    'disable-css': false,
                    'headless-mode': true,
                    'page-timeout': '15',
                    'batch-size': '20',
                    'memory-cleanup': '25',
                    'retry-attempts': '2',
                    'smart-delays': true,
                    'anti-detection': true,
                    'delay-range': '1.5-3.0'
                },
                'safe': {
                    'enable-concurrent': true,
                    'concurrent-instances': '2',
                    'disable-images': false,
                    'disable-css': false,
                    'headless-mode': true,
                    'page-timeout': '20',
                    'batch-size': '10',
                    'memory-cleanup': '25',
                    'retry-attempts': '3',
                    'smart-delays': true,
                    'anti-detection': true,
                    'delay-range': '2.0-4.0'
                },
                'conservative': {
                    'enable-concurrent': false,
                    'concurrent-instances': '1',
                    'disable-images': false,
                    'disable-css': false,
                    'headless-mode': false,
                    'page-timeout': '30',
                    'batch-size': '10',
                    'memory-cleanup': '50',
                    'retry-attempts': '3',
                    'smart-delays': false,
                    'anti-detection': true,
                    'delay-range': '3.0-5.0'
                }
            };

            const preset = presets[presetName];
            if (preset) {
                // Apply checkbox settings
                Object.keys(preset).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = preset[key];
                        } else if (element.tagName === 'SELECT') {
                            element.value = preset[key];
                        } else if (element.type === 'text') {
                            element.value = preset[key];
                        }
                    }
                });

                // Show notification
                alert(`Applied ${presetName.charAt(0).toUpperCase() + presetName.slice(1)} preset successfully!`);
            }
        }

        // Initialize
        loadSourceInfo();
        updateStats();
        setInterval(updateStats, 2000);
    </script>
</body>
</html>'''

    with open('templates/advanced_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_template)

def main():
    """Run the advanced dashboard"""
    print("🖥️ Starting Advanced 99acres Scraper Dashboard")
    print("=" * 60)

    # Create dashboard template
    create_advanced_dashboard_template()

    print("✅ Advanced dashboard template created")
    print("🌐 Starting web server...")
    print("📊 Dashboard will be available at: http://localhost:5001")
    print("🔧 Comprehensive monitoring with detailed progress tracking")
    print("\n🎯 KEY FEATURES:")
    print("   • Real-time progress tracking with live logs")
    print("   • Detailed URL source information and statistics")
    print("   • Skip already extracted properties option")
    print("   • Pause/Resume functionality")
    print("   • Performance metrics and timing estimates")
    print("   • Recent extractions preview")
    print("   • System resource monitoring")

    # Run Flask app
    app.run(debug=True, host='0.0.0.0', port=5001)

if __name__ == "__main__":
    main()
