#!/usr/bin/env python3
"""
Direct Property Field Analyzer
Uses existing scraper to get property URLs, then analyzes individual property pages
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integrated_99acres_scraper import Integrated99acresScraper
from bs4 import BeautifulSoup
import json
import time
import re
from collections import defaultdict
from datetime import datetime

class DirectPropertyAnalyzer:
    def __init__(self):
        self.scraper = Integrated99acresScraper(headless=False)
        self.all_fields = defaultdict(set)
        self.analyzed_properties = []
        
    def extract_comprehensive_fields(self, soup, url):
        """Extract all possible fields from a property page"""
        fields = {}
        page_text = soup.get_text()
        
        # 1. Price Information
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/sqft', 'price_per_sqft'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches[0] if len(matches) == 1 else matches
        
        # 2. Property Specifications
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'(\d+)\s*sq\.?\s*ft', 'area_sqft_alt'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches[0] if len(matches) == 1 else matches
        
        # 3. Location Information
        location_patterns = [
            (r'Located in\s*([^,\n]+)', 'location_area'),
            (r'Near\s*([^,\n]+)', 'nearby_landmarks'),
            (r'(\d{6})', 'pincode'),
            (r'(Mumbai|Delhi|Bangalore|Pune|Chennai|Hyderabad|Kolkata|Ahmedabad)', 'city')
        ]
        
        for pattern, field_name in location_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches[0] if len(matches) == 1 else matches
        
        # 4. Builder/Developer Information
        builder_patterns = [
            (r'By\s*([A-Z][^,\n]+(?:Group|Developers?|Builders?|Construction|Realty|Properties))', 'builder_name'),
            (r'Developer\s*:\s*([^,\n]+)', 'developer_name'),
            (r'RERA\s*(?:ID|Number)\s*:\s*([A-Z0-9]+)', 'rera_number')
        ]
        
        for pattern, field_name in builder_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches[0] if len(matches) == 1 else matches
        
        # 5. Amenities (look for common amenities)
        amenities_keywords = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
            'Club House', 'Lift', 'Power Backup', 'Water Supply', 'Intercom',
            'Fire Safety', 'CCTV', 'Jogging Track', 'Tennis Court', 'Basketball',
            'Badminton', 'Spa', 'Sauna', 'Library', 'Business Center'
        ]
        
        found_amenities = []
        for amenity in amenities_keywords:
            if amenity.lower() in page_text.lower():
                found_amenities.append(amenity)
        
        if found_amenities:
            fields['amenities'] = found_amenities
        
        # 6. Contact Information
        contact_patterns = [
            (r'(\+91[\s-]?\d{10})', 'phone_numbers'),
            (r'(\d{10})', 'mobile_numbers'),
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 'email_addresses')
        ]
        
        for pattern, field_name in contact_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                fields[field_name] = list(set(matches))  # Remove duplicates
        
        # 7. Meta Information
        title_tag = soup.find('title')
        if title_tag:
            fields['page_title'] = title_tag.get_text(strip=True)
        
        meta_description = soup.find('meta', attrs={'name': 'description'})
        if meta_description:
            fields['meta_description'] = meta_description.get('content', '')
        
        # 8. JSON-LD Structured Data
        json_scripts = soup.find_all('script', type='application/ld+json')
        for i, script in enumerate(json_scripts):
            try:
                json_data = json.loads(script.string)
                fields[f'structured_data_{i}'] = json_data
            except:
                pass
        
        # 9. Images
        images = soup.find_all('img', src=True)
        image_urls = [img.get('src') for img in images if img.get('src') and 'property' in img.get('src', '').lower()]
        if image_urls:
            fields['property_images'] = len(image_urls)
            fields['sample_image_urls'] = image_urls[:5]  # First 5 images
        
        # 10. Additional Data Attributes
        data_elements = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        data_attrs = {}
        for elem in data_elements:
            for attr, value in elem.attrs.items():
                if attr.startswith('data-') and value:
                    data_attrs[attr] = value
        
        if data_attrs:
            fields['data_attributes'] = data_attrs
        
        return fields
    
    def analyze_property_urls(self, max_properties=20):
        """Get property URLs and analyze them"""
        print(f"🚀 Starting comprehensive field analysis")
        print(f"🎯 Target: {max_properties} individual property pages")
        print("="*60)
        
        try:
            # Setup scraper
            self.scraper.setup_driver()
            
            # Get Mumbai properties first
            mumbai_url = self.scraper.generate_city_url('mumbai', 'sale')
            print(f"📋 Getting property URLs from: {mumbai_url}")
            
            properties = self.scraper.scrape_page(mumbai_url)
            print(f"✅ Found {len(properties)} properties")
            
            # Analyze individual property pages
            analyzed_count = 0
            for i, prop in enumerate(properties[:max_properties]):
                if 'url' not in prop or not prop['url']:
                    continue
                
                print(f"\n🏠 Analyzing property {analyzed_count + 1}/{max_properties}")
                print(f"   URL: {prop['url']}")
                
                try:
                    # Navigate to property page
                    self.scraper.driver.get(prop['url'])
                    time.sleep(3)
                    
                    # Get page source and analyze
                    soup = BeautifulSoup(self.scraper.driver.page_source, 'html.parser')
                    fields = self.extract_comprehensive_fields(soup, prop['url'])
                    
                    # Store results
                    analysis_result = {
                        'url': prop['url'],
                        'listing_data': prop,
                        'extracted_fields': fields,
                        'total_fields': len(fields)
                    }
                    
                    self.analyzed_properties.append(analysis_result)
                    
                    # Update global field collection
                    for field_name, field_value in fields.items():
                        self.all_fields[field_name].add(str(field_value)[:100])
                    
                    print(f"   ✅ Extracted {len(fields)} field types")
                    analyzed_count += 1
                    
                    # Respectful delay
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"   ❌ Error analyzing property: {str(e)}")
                    continue
            
            print(f"\n✅ Analysis complete! Analyzed {analyzed_count} properties")
            return self.generate_comprehensive_report()
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
            return None
        finally:
            if self.scraper.driver:
                self.scraper.driver.quit()
    
    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report"""
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_properties_analyzed': len(self.analyzed_properties),
            'total_unique_fields': len(self.all_fields),
            'field_frequency': {},
            'sample_properties': self.analyzed_properties[:5],  # First 5 as samples
            'all_properties': self.analyzed_properties
        }
        
        # Field frequency analysis
        for field_name, values in self.all_fields.items():
            report['field_frequency'][field_name] = {
                'frequency': len(values),
                'sample_values': list(values)[:5]
            }
        
        return report

def main():
    """Main analysis function"""
    analyzer = DirectPropertyAnalyzer()
    
    # Analyze 20 properties
    report = analyzer.analyze_property_urls(max_properties=20)
    
    if report:
        # Save comprehensive report
        with open('comprehensive_property_field_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🎉 COMPREHENSIVE FIELD ANALYSIS COMPLETE!")
        print("="*60)
        print(f"📊 Properties Analyzed: {report['total_properties_analyzed']}")
        print(f"🔍 Unique Fields Found: {report['total_unique_fields']}")
        print(f"📄 Report saved: comprehensive_property_field_analysis.json")
        
        # Print top fields
        print(f"\n🏆 TOP 20 MOST COMMON FIELDS:")
        sorted_fields = sorted(report['field_frequency'].items(), 
                             key=lambda x: x[1]['frequency'], reverse=True)
        for field_name, field_info in sorted_fields[:20]:
            print(f"   {field_name}: {field_info['frequency']} occurrences")
            if field_info['sample_values']:
                print(f"      Sample: {field_info['sample_values'][0]}")

if __name__ == "__main__":
    main()
