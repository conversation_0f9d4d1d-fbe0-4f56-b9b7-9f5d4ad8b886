#!/usr/bin/env python3
"""
Deep Individual Property Pages Research & Schema Analysis
CRITICAL PREREQUISITE for implementing individual property page extraction

This tool systematically analyzes hundreds of individual property detail pages
across multiple dimensions to understand complete data structure and patterns.
"""

import time
import json
import random
import csv
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
import os
from urllib.parse import urljoin, urlparse
from integrated_99acres_scraper import Integrated99acresScraper

class DeepIndividualPropertyResearcher:
    """Comprehensive individual property pages research tool"""
    
    def __init__(self):
        self.driver = None
        self.base_url = "https://www.99acres.com"
        self.research_results = {
            'timestamp': datetime.now().isoformat(),
            'research_summary': {},
            'property_analysis': {},
            'schema_patterns': {},
            'field_variations': {},
            'ui_patterns': {},
            'data_extraction_opportunities': {},
            'edge_cases': {},
            'recommendations': {}
        }
        
        # Research matrix - comprehensive coverage
        self.research_matrix = {
            'cities': ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'chennai'],
            'property_types': [
                'residential-sale',
                'residential-rent', 
                'commercial-sale',
                'commercial-rent'
            ],
            'sub_types': [
                'apartment',
                'villa',
                'independent-house',
                'builder-floor',
                'penthouse',
                'studio-apartment',
                'office',
                'shop',
                'warehouse'
            ],
            'price_ranges': [
                'budget',      # < 50L
                'mid-range',   # 50L - 2Cr
                'premium',     # 2Cr - 5Cr
                'luxury'       # > 5Cr
            ],
            'project_types': [
                'new-launch',
                'under-construction',
                'ready-to-move',
                'resale'
            ]
        }
        
        # Target: Analyze 200+ individual property pages
        self.target_analysis_count = 200
        self.analyzed_properties = []
        self.property_urls_collected = []
        
    def setup_driver(self):
        """Setup Chrome WebDriver for research"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # Research-friendly user agent
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Additional options to handle popups and modals
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")

        self.driver = webdriver.Chrome(options=chrome_options)
        print("✅ Browser initialized for deep individual property research")

    def handle_popups_and_modals(self):
        """Handle any popups or modals that might appear"""
        try:
            # Wait a moment for any popups to appear
            time.sleep(2)

            # Common popup/modal close button selectors
            close_selectors = [
                "button[class*='close']",
                "button[class*='dismiss']",
                "div[class*='close']",
                "span[class*='close']",
                ".modal-close",
                ".popup-close",
                "[data-dismiss='modal']",
                "button[aria-label='Close']",
                "button[title='Close']",
                ".fa-times",
                ".fa-close",
                "button:contains('×')",
                "button:contains('Close')",
                "div[role='dialog'] button",
                ".overlay button",
                ".modal button"
            ]

            for selector in close_selectors:
                try:
                    close_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in close_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button.click()
                            print("   ✅ Closed popup/modal")
                            time.sleep(1)
                            return True
                except:
                    continue

            # Try pressing Escape key to close modals
            try:
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                time.sleep(1)
                print("   ✅ Pressed Escape to close modal")
                return True
            except:
                pass

            return False

        except Exception as e:
            print(f"   ⚠️ Error handling popups: {str(e)}")
            return False
    
    def run_comprehensive_research(self):
        """Run complete individual property pages research"""
        print("🔍 STARTING DEEP INDIVIDUAL PROPERTY PAGES RESEARCH")
        print("="*80)
        print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Objective: Analyze 200+ individual property pages across all dimensions")
        print(f"📊 Research Matrix: {len(self.research_matrix['cities'])} cities × {len(self.research_matrix['property_types'])} types")
        
        try:
            self.setup_driver()
            
            # Phase 1: Collect Property URLs from Listing Pages
            print(f"\n📋 PHASE 1: Collecting Individual Property URLs")
            self.collect_property_urls_comprehensive()
            
            # Phase 2: Deep Analysis of Individual Property Pages
            print(f"\n🏠 PHASE 2: Deep Individual Property Analysis")
            self.analyze_individual_properties_comprehensive()
            
            # Phase 3: Schema and Pattern Analysis
            print(f"\n🔍 PHASE 3: Schema and Pattern Analysis")
            self.analyze_schema_patterns()
            
            # Phase 4: Field Variations and Edge Cases
            print(f"\n📊 PHASE 4: Field Variations and Edge Cases Analysis")
            self.analyze_field_variations()
            
            # Phase 5: UI Patterns and Extraction Opportunities
            print(f"\n🎨 PHASE 5: UI Patterns and Extraction Opportunities")
            self.analyze_ui_patterns()
            
            # Generate comprehensive research report
            self.generate_research_report()
            
            print(f"\n✅ DEEP INDIVIDUAL PROPERTY RESEARCH COMPLETED!")
            print(f"📊 Properties Analyzed: {len(self.analyzed_properties)}")
            print(f"📄 Research Report: individual_property_research_report.md")
            
        except Exception as e:
            print(f"❌ Research failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def collect_property_urls_comprehensive(self):
        """Collect individual property URLs across all research dimensions"""
        print("   📋 Collecting property URLs across research matrix...")
        
        # Use existing scraper to get listing pages and extract property URLs
        scraper = Integrated99acresScraper(headless=True)
        
        for city in self.research_matrix['cities']:
            print(f"     🌍 Collecting from {city.title()}...")
            
            # Collect from different property types
            for prop_type in ['property-for-sale-in-{city}-ffid', 'property-for-rent-in-{city}-ffid']:
                try:
                    url = f"https://www.99acres.com/{prop_type.format(city=city)}"
                    print(f"       📄 Analyzing listing page: {url}")
                    
                    # Get listing page
                    self.driver.get(url)
                    time.sleep(random.uniform(5, 8))

                    # Handle any popups or modals that might appear
                    self.handle_popups_and_modals()
                    time.sleep(2)

                    # Extract individual property URLs
                    property_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/propertydetail/']")

                    # If no property links found, try alternative selectors
                    if not property_links:
                        alternative_selectors = [
                            "a[href*='property']",
                            "a[href*='detail']",
                            "a[href*='npxid']",
                            ".property-card a",
                            ".listing-card a",
                            "[data-testid*='property'] a"
                        ]

                        for alt_selector in alternative_selectors:
                            property_links = self.driver.find_elements(By.CSS_SELECTOR, alt_selector)
                            if property_links:
                                print(f"       ✅ Found {len(property_links)} links using selector: {alt_selector}")
                                break
                    
                    for link in property_links[:20]:  # Limit per page for comprehensive coverage
                        try:
                            property_url = link.get_attribute('href')
                            if property_url and property_url not in self.property_urls_collected:
                                self.property_urls_collected.append({
                                    'url': property_url,
                                    'city': city,
                                    'source_type': prop_type,
                                    'collected_at': datetime.now().isoformat()
                                })
                        except Exception as e:
                            continue
                    
                    print(f"         ✅ Collected {len(property_links)} URLs from {city}")
                    time.sleep(random.uniform(3, 5))  # Respectful delay
                    
                except Exception as e:
                    print(f"         ❌ Failed to collect from {city}: {str(e)}")
                    continue
        
        scraper.close()
        print(f"   📊 Total URLs collected: {len(self.property_urls_collected)}")
        
        # Save collected URLs for reference
        with open('individual_property_urls_collected.json', 'w') as f:
            json.dump(self.property_urls_collected, f, indent=2)
    
    def analyze_individual_properties_comprehensive(self):
        """Analyze individual property pages comprehensively"""
        print("   🏠 Analyzing individual property pages...")
        
        # Select diverse sample for analysis
        analysis_sample = self.select_diverse_sample()
        
        for i, property_info in enumerate(analysis_sample):
            if len(self.analyzed_properties) >= self.target_analysis_count:
                break
                
            try:
                print(f"     🏠 Analyzing property {i+1}/{len(analysis_sample)}: {property_info['city']}")
                
                # Navigate to individual property page
                self.driver.get(property_info['url'])
                time.sleep(random.uniform(6, 10))

                # Handle any popups or modals that might appear
                self.handle_popups_and_modals()
                time.sleep(2)

                # Comprehensive analysis of this property
                property_analysis = self.analyze_single_property_comprehensive(property_info)
                
                if property_analysis:
                    self.analyzed_properties.append(property_analysis)
                    print(f"       ✅ Analysis complete - {len(property_analysis['data_fields'])} fields found")
                else:
                    print(f"       ⚠️ Analysis failed for this property")
                
                # Respectful delay between properties
                time.sleep(random.uniform(8, 12))
                
            except Exception as e:
                print(f"       ❌ Failed to analyze property {i+1}: {str(e)}")
                continue
        
        print(f"   📊 Individual properties analyzed: {len(self.analyzed_properties)}")
    
    def analyze_single_property_comprehensive(self, property_info):
        """Comprehensive analysis of a single property page"""
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'url': property_info['url'],
                'city': property_info['city'],
                'source_type': property_info['source_type'],
                'analysis_timestamp': datetime.now().isoformat(),
                'page_structure': {},
                'data_fields': {},
                'json_ld_data': {},
                'ui_elements': {},
                'media_content': {},
                'interactive_features': {},
                'contact_information': {},
                'similar_properties': {},
                'breadcrumbs': {},
                'seo_elements': {}
            }
            
            # 1. Page Structure Analysis
            analysis['page_structure'] = self._analyze_page_structure(soup)
            
            # 2. Data Fields Extraction
            analysis['data_fields'] = self._extract_all_data_fields(soup)
            
            # 3. JSON-LD Structured Data
            analysis['json_ld_data'] = self._extract_json_ld_data(soup)
            
            # 4. UI Elements Analysis
            analysis['ui_elements'] = self._analyze_ui_elements(soup)
            
            # 5. Media Content Analysis
            analysis['media_content'] = self._analyze_media_content(soup)
            
            # 6. Interactive Features
            analysis['interactive_features'] = self._analyze_interactive_features(soup)
            
            # 7. Contact Information
            analysis['contact_information'] = self._extract_contact_information(soup)
            
            # 8. Similar Properties
            analysis['similar_properties'] = self._analyze_similar_properties(soup)
            
            # 9. Breadcrumbs and Navigation
            analysis['breadcrumbs'] = self._extract_breadcrumbs(soup)
            
            # 10. SEO Elements
            analysis['seo_elements'] = self._analyze_seo_elements(soup)
            
            return analysis
            
        except Exception as e:
            print(f"         ❌ Single property analysis failed: {str(e)}")
            return None
    
    def select_diverse_sample(self):
        """Select diverse sample of properties for analysis"""
        # Ensure diverse coverage across cities, types, and price ranges
        sample = []
        
        # Group by city
        by_city = {}
        for prop in self.property_urls_collected:
            city = prop['city']
            if city not in by_city:
                by_city[city] = []
            by_city[city].append(prop)
        
        # Select balanced sample from each city
        target_per_city = max(30, self.target_analysis_count // len(by_city))
        
        for city, properties in by_city.items():
            # Shuffle and take sample
            random.shuffle(properties)
            sample.extend(properties[:target_per_city])
        
        # Shuffle final sample
        random.shuffle(sample)
        return sample[:self.target_analysis_count]
    
    # Helper methods for detailed analysis (to be implemented)
    def _analyze_page_structure(self, soup): return {}
    def _extract_all_data_fields(self, soup): return {}
    def _extract_json_ld_data(self, soup): return {}
    def _analyze_ui_elements(self, soup): return {}
    def _analyze_media_content(self, soup): return {}
    def _analyze_interactive_features(self, soup): return {}
    def _extract_contact_information(self, soup): return {}
    def _analyze_similar_properties(self, soup): return {}
    def _extract_breadcrumbs(self, soup): return {}
    def _analyze_seo_elements(self, soup): return {}
    
    def analyze_schema_patterns(self):
        """Analyze schema patterns across all analyzed properties"""
        print("   🔍 Analyzing schema patterns...")
        # Implementation for schema pattern analysis
        pass
    
    def analyze_field_variations(self):
        """Analyze field variations and edge cases"""
        print("   📊 Analyzing field variations...")
        # Implementation for field variation analysis
        pass
    
    def analyze_ui_patterns(self):
        """Analyze UI patterns and extraction opportunities"""
        print("   🎨 Analyzing UI patterns...")
        # Implementation for UI pattern analysis
        pass
    
    def generate_research_report(self):
        """Generate comprehensive research report"""
        print("   📄 Generating comprehensive research report...")
        
        # Create detailed markdown report
        report = []
        report.append("# Deep Individual Property Pages Research Report")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Executive Summary
        report.append("## Executive Summary")
        report.append(f"Comprehensive analysis of {len(self.analyzed_properties)} individual property pages")
        report.append(f"across {len(self.research_matrix['cities'])} cities and multiple property types.")
        report.append("")
        
        # Research Findings
        report.append("## Key Research Findings")
        report.append("### Data Structure Patterns")
        report.append("### Field Variations")
        report.append("### Schema Opportunities")
        report.append("### UI Patterns")
        report.append("### Extraction Recommendations")
        report.append("")
        
        # Save report
        with open('individual_property_research_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        # Save raw data
        with open('individual_property_research_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.research_results, f, indent=2, ensure_ascii=False)

def main():
    """Main research function"""
    researcher = DeepIndividualPropertyResearcher()
    researcher.run_comprehensive_research()

if __name__ == "__main__":
    main()
