<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>99acres Scraper Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        .control-panel { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-primary { background: #667eea; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; }
        .recent-properties { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .property-item { padding: 10px; border-bottom: 1px solid #eee; }
        .status-running { color: #27ae60; }
        .status-stopped { color: #e74c3c; }
        .config-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .config-item { display: flex; flex-direction: column; }
        .config-item label { margin-bottom: 5px; font-weight: bold; }
        .config-item input, .config-item select { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 99acres Optimized Scraper Dashboard</h1>
            <p>Real-time monitoring and control for large-scale property scraping</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-scraped">0</div>
                <div class="stat-label">Total Properties Scraped</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="success-rate">0%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avg-time">0s</div>
                <div class="stat-label">Avg Time per Property</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="status">Stopped</div>
                <div class="stat-label">Status</div>
            </div>
        </div>
        
        <div class="control-panel">
            <h3>Scraper Control</h3>
            <div class="config-grid">
                <div class="config-item">
                    <label>Max Properties:</label>
                    <input type="number" id="max-properties" value="100" min="1" max="10000">
                </div>
                <div class="config-item">
                    <label>Concurrent Instances:</label>
                    <input type="number" id="concurrent-instances" value="4" min="1" max="8">
                </div>
                <div class="config-item">
                    <label>Min Delay (s):</label>
                    <input type="number" id="min-delay" value="1.5" step="0.1" min="0.5">
                </div>
                <div class="config-item">
                    <label>Max Delay (s):</label>
                    <input type="number" id="max-delay" value="3.0" step="0.1" min="1.0">
                </div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="startScraping()">Start Scraping</button>
                <button class="btn btn-danger" onclick="stopScraping()">Stop Scraping</button>
            </div>
        </div>
        
        <div class="recent-properties">
            <h3>Recent Properties</h3>
            <div id="recent-list">Loading...</div>
        </div>
    </div>

    <script>
        let isRunning = false;
        
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-scraped').textContent = data.stats.total_scraped;
                    document.getElementById('success-rate').textContent = data.stats.success_rate.toFixed(1) + '%';
                    document.getElementById('avg-time').textContent = data.stats.avg_time_per_property.toFixed(1) + 's';
                    
                    const statusElement = document.getElementById('status');
                    if (data.is_running) {
                        statusElement.textContent = 'Running';
                        statusElement.className = 'stat-value status-running';
                    } else {
                        statusElement.textContent = 'Stopped';
                        statusElement.className = 'stat-value status-stopped';
                    }
                    
                    // Update recent properties
                    const recentList = document.getElementById('recent-list');
                    if (data.recent_properties.length > 0) {
                        recentList.innerHTML = data.recent_properties.map(prop => 
                            `<div class="property-item">
                                <strong>${prop.title || 'N/A'}</strong><br>
                                <small>${prop.city} | ${prop.bhk_config} | ₹${prop.price_crores}Cr | ${prop.extraction_time}s</small>
                            </div>`
                        ).join('');
                    } else {
                        recentList.innerHTML = '<div class="property-item">No properties scraped yet</div>';
                    }
                    
                    isRunning = data.is_running;
                })
                .catch(error => console.error('Error updating stats:', error));
        }
        
        function startScraping() {
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                concurrent_instances: parseInt(document.getElementById('concurrent-instances').value),
                min_delay: parseFloat(document.getElementById('min-delay').value),
                max_delay: parseFloat(document.getElementById('max-delay').value),
                headless: true,
                enable_images: false
            };
            
            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Scraping started successfully!');
                } else {
                    alert('Failed to start scraping: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping');
            });
        }
        
        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Scraping stopped successfully!');
                    } else {
                        alert('Failed to stop scraping: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error stopping scraping:', error);
                    alert('Error stopping scraping');
                });
        }
        
        // Update stats every 2 seconds
        setInterval(updateStats, 2000);
        updateStats(); // Initial load
    </script>
</body>
</html>