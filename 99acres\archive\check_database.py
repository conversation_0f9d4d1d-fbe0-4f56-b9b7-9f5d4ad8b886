#!/usr/bin/env python3
"""
Check the database to see what data was extracted
"""

import sqlite3
import pandas as pd

def check_database():
    """Check what data is in the database"""
    conn = sqlite3.connect('data/99acres_properties.db')
    
    # Get total count
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_count = cursor.fetchone()[0]
    print(f"📊 Total properties in database: {total_count}")
    
    # Get sample data
    df = pd.read_sql_query("""
        SELECT title, price, price_per_sqft, area, bedrooms, bathrooms, 
               locality, property_url, scraped_at
        FROM properties 
        ORDER BY id DESC 
        LIMIT 10
    """, conn)
    
    print("\n📋 Sample properties:")
    for i, row in df.iterrows():
        print(f"\n{i+1}. {row['title']}")
        print(f"   Price: {row['price'] or 'N/A'}")
        print(f"   Area: {row['area'] or 'N/A'} sqft")
        print(f"   BHK: {row['bedrooms'] or 'N/A'}")
        print(f"   Location: {row['locality'] or 'N/A'}")
        print(f"   URL: {row['property_url'][:80]}..." if row['property_url'] else "   URL: N/A")
    
    # Check data completeness
    print("\n📈 Data Completeness:")
    for col in ['title', 'price', 'area', 'bedrooms', 'locality']:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {col} IS NOT NULL AND {col} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {col}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    conn.close()

if __name__ == "__main__":
    check_database()
