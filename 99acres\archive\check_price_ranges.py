#!/usr/bin/env python3
"""
Check price range extraction results
"""

import sqlite3
import pandas as pd

def check_price_ranges():
    """Check price range extraction results"""
    conn = sqlite3.connect('data/99acres_properties.db')
    
    # Get total count
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_count = cursor.fetchone()[0]
    print(f"📊 Total properties in database: {total_count}")
    
    # Check price types
    print(f"\n💰 Price Analysis:")
    
    # Single prices
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price IS NOT NULL AND price != '' AND price NOT LIKE '%-%'")
    single_price_count = cursor.fetchone()[0]
    print(f"   Single prices: {single_price_count} ({single_price_count/total_count*100:.1f}%)")
    
    # Price ranges
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price IS NOT NULL AND price LIKE '%-%'")
    range_price_count = cursor.fetchone()[0]
    print(f"   Price ranges: {range_price_count} ({range_price_count/total_count*100:.1f}%)")
    
    # Starting from prices
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price IS NOT NULL AND price LIKE '%Starting from%'")
    starting_price_count = cursor.fetchone()[0]
    print(f"   Starting from: {starting_price_count} ({starting_price_count/total_count*100:.1f}%)")
    
    # Onwards prices
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price IS NOT NULL AND price LIKE '%onwards%'")
    onwards_price_count = cursor.fetchone()[0]
    print(f"   Onwards: {onwards_price_count} ({onwards_price_count/total_count*100:.1f}%)")
    
    # Show sample price ranges
    print(f"\n📋 Sample Price Ranges:")
    df = pd.read_sql_query("""
        SELECT title, price
        FROM properties
        WHERE price LIKE '%-%' OR price LIKE '%Starting%' OR price LIKE '%onwards%'
        ORDER BY id DESC
        LIMIT 10
    """, conn)

    for i, row in df.iterrows():
        print(f"\n{i+1}. {row['title']}")
        print(f"   Price: {row['price']}")
    
    # Price unit distribution
    print(f"\n📊 Price Unit Distribution:")
    cursor.execute("""
        SELECT 
            CASE 
                WHEN price LIKE '%Cr%' THEN 'Crore'
                WHEN price LIKE '%Lakh%' THEN 'Lakh'
                ELSE 'Other'
            END as unit,
            COUNT(*) as count
        FROM properties 
        WHERE price IS NOT NULL AND price != ''
        GROUP BY unit
        ORDER BY count DESC
    """)
    
    unit_dist = cursor.fetchall()
    for unit, count in unit_dist:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {unit}: {count} properties ({percentage:.1f}%)")
    
    # Property type distribution
    print(f"\n🏢 Property Type Distribution:")
    cursor.execute("""
        SELECT property_type, COUNT(*) as count
        FROM properties
        WHERE property_type IS NOT NULL AND property_type != ''
        GROUP BY property_type
        ORDER BY count DESC
    """)

    type_dist = cursor.fetchall()
    for property_type, count in type_dist:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {property_type}: {count} properties ({percentage:.1f}%)")
    
    conn.close()

if __name__ == "__main__":
    check_price_ranges()
