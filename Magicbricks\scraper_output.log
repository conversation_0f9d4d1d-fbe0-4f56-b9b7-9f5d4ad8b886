Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\integrated_magicbricks_scraper.py", line 2927, in main
    scraper = IntegratedMagicBricksScraper(headless=True, incremental_enabled=False)
  File "D:\real estate\Scrapers\Magicbricks\integrated_magicbricks_scraper.py", line 101, in __init__
    self.date_parser = DateParsingSystem()
                       ~~~~~~~~~~~~~~~~~^^
  File "D:\real estate\Scrapers\Magicbricks\date_parsing_system.py", line 66, in __init__
    print("\U0001f4c5 Date Parsing System Initialized")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c5' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\integrated_magicbricks_scraper.py", line 2958, in <module>
    main()
    ~~~~^^
  File "D:\real estate\Scrapers\Magicbricks\integrated_magicbricks_scraper.py", line 2950, in main
    print(f"\u274c Test failed: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
