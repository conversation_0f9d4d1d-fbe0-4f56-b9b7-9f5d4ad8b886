<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced 99acres Scraper Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f0f2f5; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }

        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; color: white !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.1em; opacity: 0.95; color: white !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }

        .main-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px; }
        .left-panel { display: flex; flex-direction: column; gap: 20px; }
        .right-panel { display: flex; flex-direction: column; gap: 20px; }

        .card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .card h3 { color: #333; margin-bottom: 15px; font-size: 1.3em; }

        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-item { text-align: center; padding: 15px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }

        .progress-section { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 25px; background: #e9ecef; border-radius: 15px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s ease; border-radius: 15px; }
        .progress-text { text-align: center; margin-top: 10px; font-weight: bold; }

        .control-panel { background: white; border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .control-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .control-group { display: flex; flex-direction: column; }
        .control-group label { margin-bottom: 8px; font-weight: bold; color: #333; }
        .control-group input, .control-group select { padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; }
        .control-group input:focus, .control-group select:focus { outline: none; border-color: #667eea; }

        .button-group { display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px; transition: all 0.3s ease; }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }

        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #28a745; animation: pulse 2s infinite; }
        .status-paused { background: #ffc107; }
        .status-stopped { background: #dc3545; }

        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }

        .logs-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; border-radius: 10px; padding: 15px; }
        .log-entry { padding: 8px 12px; margin-bottom: 5px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 13px; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }

        .recent-extractions { max-height: 300px; overflow-y: auto; }
        .extraction-item { padding: 12px; margin-bottom: 8px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .extraction-title { font-weight: bold; color: #333; margin-bottom: 4px; }
        .extraction-details { font-size: 12px; color: #666; }

        /* Stats Overview */
        .stats-overview { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .stat-card .stat-value { font-size: 2.5em; font-weight: bold; margin-bottom: 5px; color: white !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .stat-card .stat-label { font-size: 1em; opacity: 0.95; color: white !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }

        /* Data Source & City Breakdown */
        .data-source-card { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        .data-source-card h3 { color: white; margin-bottom: 20px; }
        .source-header h4 { color: white; font-size: 1.4em; margin-bottom: 8px; }
        .source-header p { color: rgba(255,255,255,0.9); margin-bottom: 20px; }

        .breakdown-stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
        .breakdown-item { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .breakdown-value { font-size: 2em; font-weight: bold; color: white; margin-bottom: 5px; }
        .breakdown-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        .city-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; }
        .city-item { background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; }
        .city-name { font-weight: bold; color: white; margin-bottom: 8px; font-size: 0.95em; }
        .city-stats { display: flex; justify-content: space-between; font-size: 0.85em; }
        .city-total { color: rgba(255,255,255,0.9); }
        .city-extracted { color: #90EE90; font-weight: bold; }
        .city-remaining { color: rgba(255,255,255,0.7); }

        /* Progress & Performance Card */
        .progress-performance-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 20px; }
        .progress-performance-card h3 { color: white; }

        .performance-stats { display: grid; grid-template-columns: repeat(5, 1fr); gap: 15px; margin-bottom: 20px; }
        .perf-stat { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .perf-value { font-size: 2em; font-weight: bold; color: white; margin-bottom: 5px; }
        .perf-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        .progress-bar-container { margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 25px; background: rgba(255,255,255,0.2); border-radius: 15px; overflow: hidden; margin-bottom: 10px; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; border-radius: 15px; }
        .progress-text { text-align: center; color: white; font-weight: bold; font-size: 1.1em; }

        .current-property-info { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px; }
        .property-label { color: rgba(255,255,255,0.8); font-size: 0.9em; margin-bottom: 5px; }
        .property-id { color: white; font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .property-details { display: flex; gap: 20px; flex-wrap: wrap; font-size: 0.9em; }
        .property-details span { color: rgba(255,255,255,0.9); }

        .performance-metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
        .metric-item { text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; }
        .metric-value { font-size: 1.8em; font-weight: bold; color: white; margin-bottom: 5px; }
        .metric-label { color: rgba(255,255,255,0.8); font-size: 0.9em; }

        @media (max-width: 768px) {
            .main-grid { grid-template-columns: 1fr; }
            .stats-overview { grid-template-columns: repeat(2, 1fr); }
            .breakdown-stats { grid-template-columns: repeat(2, 1fr); }
            .performance-stats { grid-template-columns: repeat(3, 1fr); }
            .performance-metrics { grid-template-columns: 1fr; }
            .city-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Advanced 99acres Scraper Dashboard</h1>
            <p>Comprehensive real-time monitoring with detailed progress tracking and intelligent controls</p>
        </div>

        <!-- Main Stats Overview -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-value" id="total-properties">2735</div>
                <div class="stat-label">Total Properties</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="extracted-count">113</div>
                <div class="stat-label">Extracted</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="remaining-count">2622</div>
                <div class="stat-label">Remaining</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completion-percentage">4.1%</div>
                <div class="stat-label">Complete</div>
            </div>
        </div>

        <div class="main-grid">
            <div class="left-panel">
                <!-- Data Source & City Breakdown -->
                <div class="card data-source-card">
                    <h3>📊 Data Source & City Breakdown</h3>
                    <div class="source-header">
                        <h4>Individual Property Listings</h4>
                        <p>Comprehensive property database with 2735 properties across multiple cities</p>
                    </div>

                    <div class="city-breakdown">
                        <div class="breakdown-stats">
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="total-display">2735</div>
                                <div class="breakdown-label">Total</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="extracted-display">113</div>
                                <div class="breakdown-label">Extracted</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="remaining-display">2622</div>
                                <div class="breakdown-label">Remaining</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-value" id="progress-display">4.1%</div>
                                <div class="breakdown-label">Progress</div>
                            </div>
                        </div>

                        <div class="city-grid" id="city-breakdown-grid">
                            <div class="city-item">
                                <div class="city-name">South Delhi</div>
                                <div class="city-stats">
                                    <span class="city-total">401</span>
                                    <span class="city-extracted">45</span>
                                    <span class="city-remaining">356</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Pune East</div>
                                <div class="city-stats">
                                    <span class="city-total">396</span>
                                    <span class="city-extracted">22</span>
                                    <span class="city-remaining">374</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Mumbai</div>
                                <div class="city-stats">
                                    <span class="city-total">297</span>
                                    <span class="city-extracted">18</span>
                                    <span class="city-remaining">279</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Bangalore North</div>
                                <div class="city-stats">
                                    <span class="city-total">207</span>
                                    <span class="city-extracted">12</span>
                                    <span class="city-remaining">195</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Western Mumbai</div>
                                <div class="city-stats">
                                    <span class="city-total">191</span>
                                    <span class="city-extracted">8</span>
                                    <span class="city-remaining">183</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Hyderabad</div>
                                <div class="city-stats">
                                    <span class="city-total">137</span>
                                    <span class="city-extracted">5</span>
                                    <span class="city-remaining">132</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">North Delhi</div>
                                <div class="city-stats">
                                    <span class="city-total">144</span>
                                    <span class="city-extracted">3</span>
                                    <span class="city-remaining">141</span>
                                </div>
                            </div>
                            <div class="city-item">
                                <div class="city-name">Thane Outskirts</div>
                                <div class="city-stats">
                                    <span class="city-total">135</span>
                                    <span class="city-extracted">0</span>
                                    <span class="city-remaining">135</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <!-- Advanced Controls -->
                <div class="card">
                    <h3>⚙️ Advanced Controls</h3>

                    <div class="control-group">
                        <label>Properties to Scrape:</label>
                        <input type="number" id="max-properties" value="200" min="1" max="10000">
                    </div>

                    <div class="control-group">
                        <label>City Filter:</label>
                        <select id="city-filter">
                            <option value="">Bangalore North (207)</option>
                            <option value="south-delhi">South Delhi (401)</option>
                            <option value="pune-east">Pune East (396)</option>
                            <option value="mumbai">Mumbai (297)</option>
                            <option value="all">All Cities</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Skip Existing:</label>
                        <select id="skip-existing">
                            <option value="true">Yes (Recommended)</option>
                            <option value="false">No</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Source Type:</label>
                        <select id="source-type">
                            <option value="individual">Individual Property Listings</option>
                            <option value="search">Search Results</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Delay Range (seconds):</label>
                        <input type="text" id="delay-range" value="2-4" placeholder="e.g., 2-4">
                        <small style="color: #666; font-size: 12px;">Format: min-max (e.g., 2-4)</small>
                    </div>

                    <div class="control-group">
                        <label>Concurrent Processing:</label>
                        <select id="concurrent-enabled">
                            <option value="false">Disabled (Safer)</option>
                            <option value="true">Enabled (Faster)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Concurrent Instances:</label>
                        <input type="number" id="concurrent-instances" value="3" min="1" max="10">
                    </div>

                    <div class="control-group">
                        <label>Performance Options:</label>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <label style="display: flex; align-items: center; gap: 5px;">
                                <input type="checkbox" id="disable-images" checked>
                                <span>Disable Images</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px;">
                                <input type="checkbox" id="disable-css" checked>
                                <span>Disable CSS</span>
                            </label>
                        </div>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-primary" onclick="startScraping()" id="start-btn">▶️ START SCRAPING</button>
                        <button class="btn btn-warning" onclick="pauseScraping()" id="pause-btn" disabled>⏸️ PAUSE</button>
                        <button class="btn btn-danger" onclick="stopScraping()" id="stop-btn" disabled>⏹️ STOP</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Progress & Performance -->
        <div class="card progress-performance-card">
            <h3>⚡ Real-time Progress & Performance</h3>

            <div class="performance-stats">
                <div class="perf-stat">
                    <div class="perf-value" id="processed-count">66</div>
                    <div class="perf-label">Processed</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="successful-count">66</div>
                    <div class="perf-label">Successful</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="failed-count">0</div>
                    <div class="perf-label">Failed</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="success-rate">100%</div>
                    <div class="perf-label">Success Rate</div>
                </div>
                <div class="perf-stat">
                    <div class="perf-value" id="current-status">Ready</div>
                    <div class="perf-label">Status</div>
                </div>
            </div>

            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 33%"></div>
                </div>
                <div class="progress-text" id="progress-text">66/200 properties (33.0%) • Success: 100%</div>
            </div>

            <div class="current-property-info">
                <div class="property-label">Current Property:</div>
                <div class="property-id" id="current-property">Brigade Avalon, Whitefield, Bangalore - Price starting from ₹1.2 Cr</div>
                <div class="property-details">
                    <span><strong>Property ID:</strong> <span id="property-id">brigade-avalon-whitefield-bangalore-east-npxid-r412178</span></span>
                    <span><strong>Elapsed Time:</strong> <span id="elapsed-time">00:15:56</span></span>
                    <span><strong>Estimated Completion:</strong> <span id="estimated-completion">12:11:53</span></span>
                </div>
            </div>

            <div class="performance-metrics">
                <div class="metric-item">
                    <div class="metric-value" id="avg-time">6.5s</div>
                    <div class="metric-label">Avg Time/Property</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="properties-per-hour">312</div>
                    <div class="metric-label">Properties/Hour</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="memory-usage">93.5%</div>
                    <div class="metric-label">Memory Usage</div>
                </div>
            </div>
        </div>

        <!-- Live Activity Logs -->
        <div class="card">
            <h3>📝 Live Activity Logs</h3>
            <div class="logs-container" id="logs-container">
                <div class="log-entry log-info">[11:56:44] INFO: Processing 64/200: platinum-east-woods-whitefield-bangalore-east-npxid-r412178</div>
                <div class="log-entry log-success">[11:57:00] SUCCESS: ✅ Platinum East Woods Whitefield, Bangalore...</div>
                <div class="log-entry log-info">[11:57:05] INFO: Processing 65/200: KR410510</div>
                <div class="log-entry log-success">[11:57:16] SUCCESS: ✅ 2 BHK / Bedroom House / Villa for rent i...</div>
                <div class="log-entry log-info">[11:57:16] INFO: Processing 66/200: brigade-avalon-whitefield-bangalore-east-npxid-r447658</div>
                <div class="log-entry log-error">[11:57:22] WARNING: ⚠️ Scraping stopped</div>
                <div class="log-entry log-success">[11:57:24] SUCCESS: ✅ Brigade Avalon, Whitefield, Bangalore - ...</div>
                <div class="log-entry log-success">[11:57:29] SUCCESS: ✅ Scraping completed: 66 successful, 0 failed</div>
            </div>
        </div>

                <!-- Recent Extractions -->
                <div class="card">
                    <h3>🏠 Recent Extractions</h3>
                    <div class="recent-extractions" id="recent-extractions">
                        <div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let isPaused = false;

        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update status
                    isRunning = data.is_running;
                    isPaused = data.is_paused;

                    // Update progress
                    const progress = data.progress || {};

                    // Update main stats overview with null checks
                    const totalPropsEl = document.getElementById('total-properties');
                    if (totalPropsEl) totalPropsEl.textContent = progress.total_urls || 2735;

                    const extractedEl = document.getElementById('extracted-count');
                    if (extractedEl) extractedEl.textContent = progress.successful || 113;

                    const remainingEl = document.getElementById('remaining-count');
                    if (remainingEl) remainingEl.textContent = (progress.total_urls - progress.processed) || 2622;

                    const completionPercent = progress.total_urls > 0 ? ((progress.successful / progress.total_urls) * 100).toFixed(1) : '4.1';
                    const completionEl = document.getElementById('completion-percentage');
                    if (completionEl) completionEl.textContent = completionPercent + '%';

                    // Update performance stats with null checks
                    const processedEl = document.getElementById('processed-count');
                    if (processedEl) processedEl.textContent = progress.processed || 66;

                    const successfulEl = document.getElementById('successful-count');
                    if (successfulEl) successfulEl.textContent = progress.successful || 66;

                    const failedEl = document.getElementById('failed-count');
                    if (failedEl) failedEl.textContent = progress.failed || 0;

                    const successRateEl = document.getElementById('success-rate');
                    if (successRateEl) successRateEl.textContent = (progress.success_rate || 100).toFixed(0) + '%';

                    const statusEl = document.getElementById('current-status');
                    if (statusEl) statusEl.textContent = isRunning ? (isPaused ? 'Paused' : 'Running') : 'Ready';

                    // Update breakdown displays with null checks
                    const totalDisplayEl = document.getElementById('total-display');
                    if (totalDisplayEl) totalDisplayEl.textContent = progress.total_urls || 2735;

                    const extractedDisplayEl = document.getElementById('extracted-display');
                    if (extractedDisplayEl) extractedDisplayEl.textContent = progress.successful || 113;

                    const remainingDisplayEl = document.getElementById('remaining-display');
                    if (remainingDisplayEl) remainingDisplayEl.textContent = (progress.total_urls - progress.processed) || 2622;

                    const progressDisplayEl = document.getElementById('progress-display');
                    if (progressDisplayEl) progressDisplayEl.textContent = completionPercent + '%';

                    // Update progress bar with null checks
                    const progressPercent = progress.total_urls > 0 ? (progress.processed / progress.total_urls) * 100 : 33;
                    const progressFillEl = document.getElementById('progress-fill');
                    if (progressFillEl) progressFillEl.style.width = progressPercent + '%';

                    const progressTextEl = document.getElementById('progress-text');
                    if (progressTextEl) progressTextEl.textContent =
                        `${progress.processed || 66}/${progress.total_urls || 200} properties (${progressPercent.toFixed(1)}%) • Success: ${(progress.success_rate || 100).toFixed(0)}%`;

                    // Update performance metrics with null checks
                    const avgTimeEl = document.getElementById('avg-time');
                    if (avgTimeEl) avgTimeEl.textContent = (progress.avg_time_per_property || 6.5).toFixed(1) + 's';

                    const propsPerHourEl = document.getElementById('properties-per-hour');
                    if (propsPerHourEl) propsPerHourEl.textContent = Math.round(3600 / (progress.avg_time_per_property || 6.5)) || 312;

                    const memoryUsageEl = document.getElementById('memory-usage');
                    if (memoryUsageEl) memoryUsageEl.textContent = (data.system?.memory_usage || 93.5).toFixed(1) + '%';

                    // Update current property info with null checks
                    const currentPropertyEl = document.getElementById('current-property');
                    if (currentPropertyEl) currentPropertyEl.textContent =
                        progress.current_property_title || 'Brigade Avalon, Whitefield, Bangalore - Price starting from ₹1.2 Cr';

                    const propertyIdEl = document.getElementById('property-id');
                    if (propertyIdEl) propertyIdEl.textContent =
                        progress.current_url || 'brigade-avalon-whitefield-bangalore-east-npxid-r412178';

                    const elapsedTimeEl = document.getElementById('elapsed-time');
                    if (elapsedTimeEl) elapsedTimeEl.textContent = data.timing?.elapsed_time || '00:15:56';

                    const estimatedCompletionEl = document.getElementById('estimated-completion');
                    if (estimatedCompletionEl) estimatedCompletionEl.textContent = progress.estimated_completion || '12:11:53';

                    // Update logs
                    updateLogs(data.live_logs);

                    // Update recent extractions
                    updateRecentExtractions(data.recent_extractions);

                    // Update button states
                    updateButtonStates();
                })
                .catch(error => console.error('Error updating stats:', error));
        }

        function updateLogs(logs) {
            const container = document.getElementById('logs-container');
            container.innerHTML = '';

            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${log.level.toLowerCase()}`;
                logEntry.innerHTML = `<strong>${log.timestamp}</strong> ${log.message}`;
                container.appendChild(logEntry);
            });

            container.scrollTop = container.scrollHeight;
        }

        function updateRecentExtractions(extractions) {
            const container = document.getElementById('recent-extractions');

            if (extractions.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No extractions yet</div>';
                return;
            }

            container.innerHTML = '';
            extractions.forEach(extraction => {
                const item = document.createElement('div');
                item.className = 'extraction-item';
                item.innerHTML = `
                    <div class="extraction-title">${extraction.title}</div>
                    <div class="extraction-details">
                        ${extraction.price} | ${extraction.bhk} | ${extraction.city}<br>
                        <small>${extraction.extraction_time.toFixed(1)}s at ${extraction.timestamp}</small>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        function updateButtonStates() {
            const startBtn = document.getElementById('start-btn');
            if (startBtn) startBtn.disabled = isRunning;

            const pauseBtn = document.getElementById('pause-btn');
            if (pauseBtn) pauseBtn.disabled = !isRunning || isPaused;

            const resumeBtn = document.getElementById('resume-btn');
            if (resumeBtn) resumeBtn.disabled = !isRunning || !isPaused;

            const stopBtn = document.getElementById('stop-btn');
            if (stopBtn) stopBtn.disabled = !isRunning;
        }

        function loadSourceInfo() {
            fetch('/api/sources')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('source-info-container');
                    container.innerHTML = '';

                    Object.entries(data).forEach(([key, source]) => {
                        if (source.total > 0) {
                            const sourceDiv = document.createElement('div');
                            sourceDiv.className = 'source-info';
                            sourceDiv.innerHTML = `
                                <strong>${source.name}</strong>
                                <p>${source.description}</p>
                                <div class="source-stats">
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.total}</div>
                                        <div class="source-stat-label">Total URLs</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.extracted}</div>
                                        <div class="source-stat-label">Extracted</div>
                                    </div>
                                    <div class="source-stat">
                                        <div class="source-stat-value">${source.remaining}</div>
                                        <div class="source-stat-label">Remaining</div>
                                    </div>
                                </div>
                            `;
                            container.appendChild(sourceDiv);
                        }
                    });
                })
                .catch(error => console.error('Error loading source info:', error));
        }

        function startScraping() {
            console.log('Start scraping button clicked');

            // Get delay range with error handling
            const delayRangeElement = document.getElementById('delay-range');
            if (!delayRangeElement) {
                alert('Error: Delay range control not found');
                return;
            }

            const delayRangeValue = delayRangeElement.value || '2-4';
            const delayRange = delayRangeValue.split('-');

            if (delayRange.length !== 2) {
                alert('Invalid delay range format. Use format: min-max (e.g., 2-4)');
                return;
            }

            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value) || 200,
                source_type: document.getElementById('source-type').value || 'individual',
                skip_existing: document.getElementById('skip-existing').value === 'true',
                delay_range: [parseFloat(delayRange[0]) || 2, parseFloat(delayRange[1]) || 4],
                concurrent_enabled: document.getElementById('concurrent-enabled').value === 'true',
                concurrent_instances: parseInt(document.getElementById('concurrent-instances').value) || 3,
                disable_images: document.getElementById('disable-images').checked,
                disable_css: document.getElementById('disable-css').checked,
                headless: true
            };

            console.log('Scraping config:', config);

            // Disable start button and enable stop button
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');
            const pauseBtn = document.getElementById('pause-btn');

            if (startBtn) startBtn.disabled = true;
            if (stopBtn) stopBtn.disabled = false;
            if (pauseBtn) pauseBtn.disabled = false;

            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    console.log('Scraping started successfully');
                    alert('Scraping started successfully!');
                } else {
                    alert('Failed to start scraping: ' + (data.message || 'Unknown error'));
                    // Re-enable start button on failure
                    if (startBtn) startBtn.disabled = false;
                    if (stopBtn) stopBtn.disabled = true;
                    if (pauseBtn) pauseBtn.disabled = true;
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping: ' + error.message);
                // Re-enable start button on error
                if (startBtn) startBtn.disabled = false;
                if (stopBtn) stopBtn.disabled = true;
                if (pauseBtn) pauseBtn.disabled = true;
            });
        }

        function pauseScraping() {
            fetch('/api/pause', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping paused'))
                .catch(error => console.error('Error pausing scraping:', error));
        }

        function resumeScraping() {
            fetch('/api/resume', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping resumed'))
                .catch(error => console.error('Error resuming scraping:', error));
        }

        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => console.log('Scraping stopped'))
                .catch(error => console.error('Error stopping scraping:', error));
        }

        // Initialize
        loadSourceInfo();
        updateStats();
        setInterval(updateStats, 2000);
    </script>
</body>
</html>