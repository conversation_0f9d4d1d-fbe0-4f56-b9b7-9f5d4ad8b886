2025-08-10 13:49:41,364 - magicbricks_scraper - ERROR - [SYSTEM] ConnectionError: Failed to connect to magicbricks.com
2025-08-10 13:49:41,364 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Failed to connect to magicbricks.com
Error Args: ('Failed to connect to magicbricks.com',)
2025-08-10 13:49:41,365 - magicbricks_scraper - DEBUG - Context: {'url': 'https://magicbricks.com'}
2025-08-10 13:49:41,365 - magicbricks_scraper - DEBUG - Traceback: Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\error_handling_system.py", line 541, in main
    raise ConnectionError("Failed to connect to magicbricks.com")
ConnectionError: Failed to connect to magicbricks.com

2025-08-10 13:49:41,365 - magicbricks_scraper - INFO - [PARSING] ValueError: Element not found: div.property-card
2025-08-10 13:49:41,365 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Element not found: div.property-card
Error Args: ('Element not found: div.property-card',)
2025-08-10 13:49:41,365 - magicbricks_scraper - DEBUG - Context: {'page': 1, 'selector': 'div.property-card'}
2025-08-10 13:49:41,365 - magicbricks_scraper - DEBUG - Traceback: Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\error_handling_system.py", line 548, in main
    raise ValueError("Element not found: div.property-card")
ValueError: Element not found: div.property-card

2025-08-10 13:49:41,366 - magicbricks_scraper - ERROR - [VALIDATION] TypeError: Invalid city parameter: must be string
2025-08-10 13:49:41,366 - magicbricks_scraper - DEBUG - Details: Error Type: TypeError
Error Message: Invalid city parameter: must be string
Error Args: ('Invalid city parameter: must be string',)
2025-08-10 13:49:41,366 - magicbricks_scraper - DEBUG - Context: {'city': 123}
2025-08-10 13:49:41,366 - magicbricks_scraper - DEBUG - Traceback: Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\error_handling_system.py", line 555, in main
    raise TypeError("Invalid city parameter: must be string")
TypeError: Invalid city parameter: must be string

2025-08-10 14:00:59,804 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test export error
2025-08-10 14:00:59,805 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test export error
Error Args: ('Test export error',)
2025-08-10 14:00:59,805 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,805 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,807 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 0
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 0
Error Args: ('Test error 0',)
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,807 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 1
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 1
Error Args: ('Test error 1',)
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,807 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 2
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 2
Error Args: ('Test error 2',)
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,807 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 3
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 3
Error Args: ('Test error 3',)
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,807 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 4
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 4
Error Args: ('Test error 4',)
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:00:59,807 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,808 - magicbricks_scraper - ERROR - [SYSTEM] ConnectionError: Failed to connect to server
2025-08-10 14:00:59,809 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Failed to connect to server
Error Args: ('Failed to connect to server',)
2025-08-10 14:00:59,809 - magicbricks_scraper - DEBUG - Context: {'url': 'test.com'}
2025-08-10 14:00:59,809 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:00:59,810 - magicbricks_scraper - ERROR - [VALIDATION] ValueError: Invalid input parameter
2025-08-10 14:00:59,810 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Invalid input parameter
Error Args: ('Invalid input parameter',)
2025-08-10 14:00:59,810 - magicbricks_scraper - DEBUG - Context: {'param': 'test'}
2025-08-10 14:00:59,810 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,655 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test export error
2025-08-10 14:03:53,655 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test export error
Error Args: ('Test export error',)
2025-08-10 14:03:53,655 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,655 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,658 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 0
2025-08-10 14:03:53,659 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 0
Error Args: ('Test error 0',)
2025-08-10 14:03:53,659 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,659 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,659 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 1
2025-08-10 14:03:53,659 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 1
Error Args: ('Test error 1',)
2025-08-10 14:03:53,659 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,660 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 2
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 2
Error Args: ('Test error 2',)
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,660 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 3
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 3
Error Args: ('Test error 3',)
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,660 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test error 4
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test error 4
Error Args: ('Test error 4',)
2025-08-10 14:03:53,660 - magicbricks_scraper - DEBUG - Context: {}
2025-08-10 14:03:53,661 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,662 - magicbricks_scraper - ERROR - [SYSTEM] ConnectionError: Failed to connect to server
2025-08-10 14:03:53,663 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Failed to connect to server
Error Args: ('Failed to connect to server',)
2025-08-10 14:03:53,663 - magicbricks_scraper - DEBUG - Context: {'url': 'test.com'}
2025-08-10 14:03:53,663 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:03:53,665 - magicbricks_scraper - ERROR - [VALIDATION] ValueError: Invalid input parameter
2025-08-10 14:03:53,665 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Invalid input parameter
Error Args: ('Invalid input parameter',)
2025-08-10 14:03:53,665 - magicbricks_scraper - DEBUG - Context: {'param': 'test'}
2025-08-10 14:03:53,665 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:40,753 - magicbricks_scraper - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:22:40,754 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:22:40,754 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,754 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:40,755 - magicbricks_scraper - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:22:40,755 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:22:40,755 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,756 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:40,756 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:22:40,756 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:22:40,756 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,756 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:46,920 - magicbricks_scraper - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:42:46,921 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:42:46,922 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,922 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:46,922 - magicbricks_scraper - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:42:46,922 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:42:46,922 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,922 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:46,922 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:42:46,923 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:42:46,923 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,923 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:12,791 - magicbricks_scraper - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:48:12,791 - magicbricks_scraper - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:48:12,791 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,792 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:12,792 - magicbricks_scraper - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:48:12,792 - magicbricks_scraper - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:48:12,792 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,792 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:12,792 - magicbricks_scraper - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:48:12,793 - magicbricks_scraper - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:48:12,793 - magicbricks_scraper - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,793 - magicbricks_scraper - DEBUG - Traceback: NoneType: None

