#!/usr/bin/env python3
"""
99acres Focused Deep Analysis
Analyzing specific property types and extracting comprehensive data schema
"""

import requests
import json
import time
from bs4 import BeautifulSoup
import re
from collections import defaultdict
from urllib.parse import urljoin

class PropertyAnalyzer:
    def __init__(self):
        self.base_url = "https://www.99acres.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.all_fields = defaultdict(set)
        self.property_samples = []

    def get_property_urls(self, search_urls):
        """Extract property URLs from search pages"""
        property_urls = []
        
        for search_url in search_urls:
            print(f"Fetching from: {search_url}")
            try:
                response = self.session.get(search_url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Find property links
                    links = soup.find_all('a', href=True)
                    for link in links:
                        href = link['href']
                        if any(pattern in href for pattern in ['-bhk-', '-bedroom-', 'spid-', 'npspid-']):
                            if href.startswith('/'):
                                full_url = urljoin(self.base_url, href)
                                property_urls.append(full_url)
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                print(f"Error fetching {search_url}: {e}")
        
        return list(set(property_urls))  # Remove duplicates

    def analyze_property(self, url):
        """Analyze individual property page"""
        print(f"Analyzing: {url}")
        
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return None
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract all possible data fields
            property_data = {
                'url': url,
                'fields': {}
            }
            
            # Basic property info
            self.extract_basic_fields(soup, property_data)
            
            # Specifications
            self.extract_specifications(soup, property_data)
            
            # Amenities
            self.extract_amenities(soup, property_data)
            
            # Location data
            self.extract_location_data(soup, property_data)
            
            # Financial data
            self.extract_financial_data(soup, property_data)
            
            # Legal and compliance
            self.extract_legal_data(soup, property_data)
            
            # Agent information
            self.extract_agent_data(soup, property_data)
            
            # Reviews and ratings
            self.extract_reviews_data(soup, property_data)
            
            # Track all fields found
            for field_name, field_value in property_data['fields'].items():
                self.all_fields[field_name].add(str(type(field_value).__name__))
            
            return property_data
            
        except Exception as e:
            print(f"Error analyzing {url}: {e}")
            return None

    def extract_basic_fields(self, soup, property_data):
        """Extract basic property information"""
        fields = property_data['fields']
        
        # Title
        title = soup.find('h1')
        if title:
            fields['title'] = title.get_text(strip=True)
        
        # Price
        price_patterns = [
            r'₹\s*[\d,]+\.?\d*\s*(Cr|Crore|L|Lac|Lakh)',
            r'Price[:\s]*₹[\d,]+',
            r'₹[\d,]+\s*per\s*sq\.?ft'
        ]
        
        for pattern in price_patterns:
            price_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if price_match:
                fields['price'] = price_match.strip()
                break
        
        # Area
        area_patterns = [
            r'\d+\.?\d*\s*sq\.?ft',
            r'\d+\.?\d*\s*sqft',
            r'Carpet\s*Area[:\s]*\d+',
            r'Built[- ]up\s*Area[:\s]*\d+'
        ]
        
        for pattern in area_patterns:
            area_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if area_match:
                fields['area'] = area_match.strip()
                break
        
        # Configuration
        config_match = soup.find(text=re.compile(r'\d+\s*BHK|\d+\s*Bedroom', re.IGNORECASE))
        if config_match:
            fields['configuration'] = config_match.strip()

    def extract_specifications(self, soup, property_data):
        """Extract detailed specifications"""
        fields = property_data['fields']
        
        # Floor details
        floor_match = soup.find(text=re.compile(r'\d+\w*\s*floor|\d+\w*\s*of\s*\d+', re.IGNORECASE))
        if floor_match:
            fields['floor_details'] = floor_match.strip()
        
        # Facing
        facing_match = soup.find(text=re.compile(r'(North|South|East|West)[-\s]*(East|West|North|South)?', re.IGNORECASE))
        if facing_match:
            fields['facing'] = facing_match.strip()
        
        # Property age
        age_match = soup.find(text=re.compile(r'\d+\s*(Year|Month)s?\s*Old|New\s*Construction', re.IGNORECASE))
        if age_match:
            fields['property_age'] = age_match.strip()
        
        # Furnishing
        furnishing_match = soup.find(text=re.compile(r'(Un)?[Ff]urnished|Semi[- ]?[Ff]urnished', re.IGNORECASE))
        if furnishing_match:
            fields['furnishing'] = furnishing_match.strip()
        
        # Parking
        parking_match = soup.find(text=re.compile(r'\d+\s*(Covered|Open)?\s*Parking', re.IGNORECASE))
        if parking_match:
            fields['parking'] = parking_match.strip()

    def extract_amenities(self, soup, property_data):
        """Extract amenities and features"""
        fields = property_data['fields']
        
        amenity_keywords = [
            'Swimming Pool', 'Gym', 'Gymnasium', 'Lift', 'Elevator', 'Security', 'CCTV',
            'Power Backup', 'Generator', 'Water Supply', 'Club House', 'Garden', 'Park',
            'Playground', 'Tennis Court', 'Badminton', 'Jogging Track', 'Library',
            'Intercom', 'Fire Safety', 'Waste Disposal', 'Rainwater Harvesting'
        ]
        
        found_amenities = []
        for amenity in amenity_keywords:
            if soup.find(text=re.compile(amenity, re.IGNORECASE)):
                found_amenities.append(amenity)
        
        if found_amenities:
            fields['amenities'] = found_amenities

    def extract_location_data(self, soup, property_data):
        """Extract location and nearby places"""
        fields = property_data['fields']
        
        # Nearby places
        nearby_keywords = [
            'School', 'Hospital', 'Mall', 'Metro', 'Railway', 'Airport', 'Bus Stop',
            'Market', 'ATM', 'Bank', 'Restaurant', 'Pharmacy', 'Temple', 'Mosque', 'Church'
        ]
        
        nearby_places = []
        for place in nearby_keywords:
            if soup.find(text=re.compile(place, re.IGNORECASE)):
                nearby_places.append(place)
        
        if nearby_places:
            fields['nearby_places'] = nearby_places
        
        # Locality rating
        rating_match = soup.find(text=re.compile(r'\d+\.\d+\s*out\s*of\s*5|\d+\.\d+\s*★', re.IGNORECASE))
        if rating_match:
            fields['locality_rating'] = rating_match.strip()

    def extract_financial_data(self, soup, property_data):
        """Extract financial and market data"""
        fields = property_data['fields']
        
        # EMI
        emi_match = soup.find(text=re.compile(r'EMI[:\s]*₹[\d,]+', re.IGNORECASE))
        if emi_match:
            fields['emi'] = emi_match.strip()
        
        # Price per sq ft
        price_sqft_match = soup.find(text=re.compile(r'₹[\d,]+\s*per\s*sq\.?ft', re.IGNORECASE))
        if price_sqft_match:
            fields['price_per_sqft'] = price_sqft_match.strip()

    def extract_legal_data(self, soup, property_data):
        """Extract legal and compliance data"""
        fields = property_data['fields']
        
        # RERA
        rera_match = soup.find(text=re.compile(r'RERA[:\s]*[A-Z0-9]+|Registration[:\s]*[A-Z0-9]+', re.IGNORECASE))
        if rera_match:
            fields['rera_registration'] = rera_match.strip()

    def extract_agent_data(self, soup, property_data):
        """Extract agent/dealer information"""
        fields = property_data['fields']
        
        # Agent type
        agent_match = soup.find(text=re.compile(r'(Owner|Dealer|Builder|Agent)', re.IGNORECASE))
        if agent_match:
            fields['posted_by'] = agent_match.strip()

    def extract_reviews_data(self, soup, property_data):
        """Extract reviews and ratings"""
        fields = property_data['fields']
        
        # Reviews
        review_match = soup.find(text=re.compile(r'\d+\s*(Review|Rating)s?', re.IGNORECASE))
        if review_match:
            fields['reviews_count'] = review_match.strip()

def main():
    """Run focused analysis on specific property types"""
    analyzer = PropertyAnalyzer()
    
    # Define search URLs for different property types and cities
    search_urls = [
        # Mumbai properties
        "https://www.99acres.com/search/property/buy/mumbai?page=1",
        "https://www.99acres.com/search/property/buy/mumbai?page=2",
        
        # Delhi properties  
        "https://www.99acres.com/search/property/buy/delhi?page=1",
        "https://www.99acres.com/search/property/buy/delhi?page=2",
        
        # Bangalore properties
        "https://www.99acres.com/search/property/buy/bangalore?page=1",
        "https://www.99acres.com/search/property/buy/bangalore?page=2",
        
        # Pune properties
        "https://www.99acres.com/search/property/buy/pune?page=1",
        
        # Chennai properties
        "https://www.99acres.com/search/property/buy/chennai?page=1"
    ]
    
    print("Starting comprehensive 99acres analysis...")
    print("Collecting property URLs from multiple cities...")
    
    # Get property URLs
    property_urls = analyzer.get_property_urls(search_urls)
    print(f"Found {len(property_urls)} property URLs")
    
    # Analyze properties (limit to first 100 for focused analysis)
    analyzed_count = 0
    max_properties = 100
    
    for url in property_urls[:max_properties]:
        property_data = analyzer.analyze_property(url)
        if property_data:
            analyzer.property_samples.append(property_data)
            analyzed_count += 1
            
            if analyzed_count % 10 == 0:
                print(f"Analyzed {analyzed_count} properties...")
        
        time.sleep(1)  # Rate limiting
    
    # Generate comprehensive report
    print(f"\nAnalysis complete! Analyzed {len(analyzer.property_samples)} properties")
    print(f"Found {len(analyzer.all_fields)} unique data fields")
    
    # Save results
    report = {
        'summary': {
            'total_properties_analyzed': len(analyzer.property_samples),
            'unique_fields_found': len(analyzer.all_fields),
            'field_coverage': dict(analyzer.all_fields)
        },
        'sample_properties': analyzer.property_samples[:5],  # First 5 as examples
        'all_fields_found': list(analyzer.all_fields.keys())
    }
    
    with open('99acres_focused_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("\nTop 20 Data Fields Found:")
    for i, field in enumerate(list(analyzer.all_fields.keys())[:20], 1):
        print(f"{i:2d}. {field}")
    
    print(f"\nDetailed report saved to: 99acres_focused_analysis_report.json")
    
    return report

if __name__ == "__main__":
    main()
