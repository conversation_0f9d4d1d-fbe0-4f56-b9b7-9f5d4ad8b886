[ROCKET] Starting MagicBricks CLI Scraper
   [CITY] City: gurgaon
   [MODE] Mode: full
   [PAGES] Max pages: 5
   [INDIVIDUAL] Individual pages: False
   [FORCE] Force full: False
   [HEADLESS] Headless: True
   [EXPORT] Export formats: csv, json

Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\cli_scraper.py", line 140, in main
    scraper = IntegratedMagicBricksScraper(
        headless=headless,
        incremental_enabled=not args.force_full,  # Disable incremental if force full
        custom_config=custom_config
    )
  File "D:\real estate\Scrapers\Magicbricks\integrated_magicbricks_scraper.py", line 56, in __init__
    self.incremental_system = IncrementalScrapingSystem()
                              ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\real estate\Scrapers\Magicbricks\incremental_scraping_system.py", line 33, in __init__
    self.db_schema = IncrementalDatabaseSchema(db_path)
                     ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\real estate\Scrapers\Magicbricks\incremental_database_schema.py", line 26, in __init__
    print("\U0001f5c4\ufe0f Incremental Database Schema Enhancement Initialized")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\cli_scraper.py", line 188, in <module>
    main()
    ~~~~^^
  File "D:\real estate\Scrapers\Magicbricks\cli_scraper.py", line 180, in main
    print(f"\n\u274c Unexpected error: {str(e)}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 2: character maps to <undefined>
