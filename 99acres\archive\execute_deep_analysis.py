#!/usr/bin/env python3
"""
Quick Execution Script for Optimized 99acres Deep Analysis
Demonstrates the systematic approach for analyzing 200+ individual property pages
"""

import asyncio
import sys
import time
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

async def run_optimized_analysis():
    """Execute the optimized deep analysis"""
    
    print("="*80)
    print("99ACRES OPTIMIZED DEEP ANALYSIS")
    print("="*80)
    print()
    
    print("🎯 ANALYSIS TARGETS:")
    print("   • 200+ individual property pages")
    print("   • 5 major cities (Mumbai, Delhi, Bangalore, Pune, Chennai)")
    print("   • 8+ property types per city")
    print("   • 3+ price ranges per property type")
    print("   • 80+ unique data fields expected")
    print()
    
    print("🚀 OPTIMIZED APPROACH:")
    print("   • Automated URL collection from search results")
    print("   • Intelligent batching (15 properties per batch)")
    print("   • Concurrent processing (8 simultaneous requests)")
    print("   • Adaptive rate limiting (3-6 seconds between batches)")
    print("   • Multi-pattern field extraction")
    print("   • Comprehensive quality assurance")
    print()
    
    print("⏱️  ESTIMATED EXECUTION TIME: 6-8 hours")
    print("📊 EXPECTED OUTCOMES:")
    print("   • Comprehensive data schema with 80+ fields")
    print("   • Property-type specific variations documented")
    print("   • Implementation strategies for each field type")
    print("   • Competitive analysis with quantified advantages")
    print()
    
    # Import and run the optimized analyzer
    try:
        from optimized_deep_analysis import OptimizedPropertyAnalyzer
        
        print("🔄 INITIALIZING OPTIMIZED ANALYZER...")
        analyzer = OptimizedPropertyAnalyzer()
        
        print(f"📋 ANALYSIS TARGETS CONFIGURED:")
        for i, target in enumerate(analyzer.analysis_targets, 1):
            print(f"   {i:2d}. {target.city.title()} - {target.property_type} ({target.price_range}) - {target.target_count} properties")
        
        total_properties = sum(target.target_count for target in analyzer.analysis_targets)
        print(f"\n📈 TOTAL TARGET: {total_properties} individual property pages")
        print()
        
        # Confirm execution
        response = input("🤔 Proceed with comprehensive deep analysis? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            print("\n🚀 STARTING COMPREHENSIVE ANALYSIS...")
            print("   This will systematically analyze 200+ individual property pages")
            print("   Progress will be logged in real-time")
            print()
            
            start_time = time.time()
            
            # Run the comprehensive analysis
            await analyzer.run_comprehensive_analysis()
            
            # Generate comprehensive report
            report = analyzer.generate_comprehensive_report()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print("\n" + "="*80)
            print("✅ COMPREHENSIVE DEEP ANALYSIS COMPLETE!")
            print("="*80)
            
            print(f"⏱️  Execution Time: {execution_time/3600:.1f} hours ({execution_time/60:.0f} minutes)")
            print(f"🏠 Properties Analyzed: {report['analysis_summary']['total_properties_analyzed']}")
            print(f"🔍 Unique Data Fields: {report['analysis_summary']['unique_data_fields']}")
            print(f"🎯 Analysis Targets: {report['analysis_summary']['total_targets']}")
            print()
            
            print("📊 PROPERTY TYPE BREAKDOWN:")
            for city, types in report['property_type_breakdown'].items():
                print(f"   📍 {city.title()}:")
                for prop_type, count in types.items():
                    print(f"      • {prop_type.replace('_', ' ').title()}: {count} properties")
            print()
            
            print("📁 GENERATED FILES:")
            print("   • 99acres_comprehensive_deep_analysis_report.json")
            print("   • 99acres_comprehensive_properties_data.csv")
            print()
            
            print("🎉 SUCCESS! You now have comprehensive understanding of 99acres data structure")
            print("   Ready to build production-ready scraper with 80+ data fields")
            
        else:
            print("\n❌ Analysis cancelled by user")
            
    except ImportError as e:
        print(f"❌ ERROR: Could not import optimized analyzer: {e}")
        print("\n💡 SOLUTION:")
        print("   1. Ensure 99acres_optimized_deep_analysis.py is in the same directory")
        print("   2. Install required dependencies: pip install aiohttp beautifulsoup4")
        print("   3. Run the analysis script directly: python 99acres_optimized_deep_analysis.py")
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        print("\n💡 TROUBLESHOOTING:")
        print("   1. Check internet connectivity")
        print("   2. Verify 99acres.com is accessible")
        print("   3. Ensure sufficient disk space for results")
        print("   4. Check Python version (3.7+ required)")

def show_analysis_comparison():
    """Show comparison between basic and optimized analysis"""
    
    print("\n" + "="*80)
    print("ANALYSIS APPROACH COMPARISON")
    print("="*80)
    
    print("\n❌ PREVIOUS BASIC ANALYSIS:")
    print("   • 8 individual property pages")
    print("   • Single city focus (Mumbai)")
    print("   • Limited property type coverage")
    print("   • Manual browsing approach")
    print("   • ~50 data fields identified")
    print("   • Insufficient for production use")
    
    print("\n✅ OPTIMIZED DEEP ANALYSIS:")
    print("   • 200+ individual property pages")
    print("   • 5 major cities covered")
    print("   • Comprehensive property type coverage")
    print("   • Automated systematic approach")
    print("   • 80+ data fields expected")
    print("   • Production-ready insights")
    
    print("\n📈 IMPROVEMENT METRICS:")
    print("   • 25x more properties analyzed")
    print("   • 5x more cities covered")
    print("   • 60% more data fields identified")
    print("   • 100% automated execution")
    print("   • Systematic quality assurance")
    
    print("\n🎯 BUSINESS IMPACT:")
    print("   • Comprehensive competitive advantage")
    print("   • Reduced development risks")
    print("   • Faster time-to-market")
    print("   • Superior data quality")
    print("   • Scalable foundation")

async def main():
    """Main execution function"""
    
    # Show comparison first
    show_analysis_comparison()
    
    print("\n" + "="*80)
    print("READY TO EXECUTE OPTIMIZED DEEP ANALYSIS")
    print("="*80)
    
    # Run the optimized analysis
    await run_optimized_analysis()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n❌ Analysis interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Fatal error: {e}")
        print("\n💡 Please check the error details and try again")
