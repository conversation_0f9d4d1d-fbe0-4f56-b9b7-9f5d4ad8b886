#!/usr/bin/env python3
"""
Debug script for 99acres scraper to understand page structure
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re


def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Anti-detection measures
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def debug_page_structure():
    """Debug the page structure to understand why properties aren't being found"""
    driver = setup_driver()
    
    try:
        url = "https://www.99acres.com/property-for-sale-in-mumbai-ffid"
        print(f"🔍 Loading page: {url}")
        
        driver.get(url)
        time.sleep(5)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        print("✅ Page loaded successfully")
        
        # Get page source and parse
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Debug: Check page title
        print(f"📄 Page title: {soup.title.string if soup.title else 'No title'}")
        
        # Debug: Look for price elements
        price_elements = soup.find_all(string=re.compile(r'₹\d+'))
        print(f"💰 Found {len(price_elements)} price elements")
        
        if price_elements:
            print("   Sample prices:")
            for i, price in enumerate(price_elements[:5]):
                print(f"   {i+1}. {price.strip()}")
        
        # Debug: Look for area elements
        area_elements = soup.find_all(string=re.compile(r'\d+\s*sqft'))
        print(f"📐 Found {len(area_elements)} area elements")
        
        if area_elements:
            print("   Sample areas:")
            for i, area in enumerate(area_elements[:5]):
                print(f"   {i+1}. {area.strip()}")
        
        # Debug: Look for BHK elements
        bhk_elements = soup.find_all(string=re.compile(r'\d+\s*bhk', re.IGNORECASE))
        print(f"🏠 Found {len(bhk_elements)} BHK elements")
        
        if bhk_elements:
            print("   Sample BHK:")
            for i, bhk in enumerate(bhk_elements[:5]):
                print(f"   {i+1}. {bhk.strip()}")
        
        # Debug: Look for headings
        headings = soup.find_all(['h1', 'h2', 'h3', 'h4'])
        print(f"📝 Found {len(headings)} headings")
        
        if headings:
            print("   Sample headings:")
            for i, heading in enumerate(headings[:5]):
                text = heading.get_text(strip=True)
                if text:
                    print(f"   {i+1}. {heading.name}: {text[:100]}...")
        
        # Debug: Look for links
        links = soup.find_all('a', href=True)
        property_links = [link for link in links if 'spid-' in link.get('href', '') or 'npxid-' in link.get('href', '')]
        print(f"🔗 Found {len(property_links)} property links")
        
        if property_links:
            print("   Sample property links:")
            for i, link in enumerate(property_links[:3]):
                href = link.get('href')
                text = link.get_text(strip=True)
                print(f"   {i+1}. {href[:80]}...")
                print(f"       Text: {text[:60]}...")
        
        # Debug: Check for common container elements
        containers = soup.find_all(['div', 'article', 'section'])
        print(f"📦 Found {len(containers)} container elements")
        
        # Look for containers with property-like content
        property_containers = []
        for container in containers:
            text = container.get_text().lower()
            if ('₹' in text and 'sqft' in text and 'bhk' in text):
                property_containers.append(container)
        
        print(f"🏘️ Found {len(property_containers)} potential property containers")
        
        if property_containers:
            print("   Sample property container content:")
            for i, container in enumerate(property_containers[:2]):
                text = container.get_text(strip=True)
                print(f"   {i+1}. {text[:200]}...")
        
        # Debug: Save page source for manual inspection
        with open('data/debug_page_source.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("💾 Page source saved to: data/debug_page_source.html")
        
        # Debug: Check if page has dynamic content loading
        print("\n🔄 Checking for dynamic content...")
        time.sleep(3)
        
        # Scroll down to trigger any lazy loading
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        
        # Check again after scrolling
        soup_after_scroll = BeautifulSoup(driver.page_source, 'html.parser')
        price_elements_after = soup_after_scroll.find_all(string=re.compile(r'₹\d+'))
        print(f"💰 After scrolling: {len(price_elements_after)} price elements")
        
        if len(price_elements_after) > len(price_elements):
            print("   ✅ Dynamic content detected - more elements loaded after scrolling")
        else:
            print("   ❌ No additional content loaded")
        
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
    
    finally:
        driver.quit()


if __name__ == "__main__":
    debug_page_structure()
