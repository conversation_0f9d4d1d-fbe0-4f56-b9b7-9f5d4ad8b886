#!/usr/bin/env python3
"""
Deep Research Tool for 99acres
Comprehensive analysis using browser automation only
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
import random
import os


class Deep99acresResearcher:
    """Deep research tool using browser automation"""
    
    def __init__(self):
        self.driver = None
        self.research_results = {
            'timestamp': datetime.now().isoformat(),
            'property_types_analyzed': [],
            'cities_analyzed': [],
            'total_properties_sampled': 0,
            'data_fields_discovered': {},
            'property_variations': {},
            'anti_scraping_analysis': {},
            'detailed_findings': []
        }
        
        # Research targets
        self.research_targets = [
            # Residential Sale - Different cities and property types
            ('https://www.99acres.com/property-for-sale-in-mumbai-ffid', 'residential_sale', 'mumbai'),
            ('https://www.99acres.com/property-for-sale-in-delhi-ffid', 'residential_sale', 'delhi'),
            ('https://www.99acres.com/property-for-sale-in-bangalore-ffid', 'residential_sale', 'bangalore'),
            
            # Residential Rent
            ('https://www.99acres.com/property-for-rent-in-mumbai-ffid', 'residential_rent', 'mumbai'),
            ('https://www.99acres.com/property-for-rent-in-delhi-ffid', 'residential_rent', 'delhi'),
            
            # Different property configurations
            ('https://www.99acres.com/1-bhk-apartment-flat-for-sale-in-mumbai-ffid', '1bhk_sale', 'mumbai'),
            ('https://www.99acres.com/2-bhk-apartment-flat-for-sale-in-mumbai-ffid', '2bhk_sale', 'mumbai'),
            ('https://www.99acres.com/3-bhk-apartment-flat-for-sale-in-mumbai-ffid', '3bhk_sale', 'mumbai'),
        ]
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser initialized for deep research")
    
    def analyze_page_comprehensively(self, url, property_type, city):
        """Comprehensive analysis of a single page"""
        print(f"\n🔍 DEEP ANALYSIS: {property_type} in {city}")
        print(f"   URL: {url}")
        
        try:
            # Load page
            self.driver.get(url)
            time.sleep(5)
            
            # Check if page loaded successfully
            page_title = self.driver.title
            if 'error' in page_title.lower() or 'not found' in page_title.lower():
                print(f"   ❌ Page error: {page_title}")
                return None
            
            print(f"   📄 Page title: {page_title}")
            
            # Scroll to load dynamic content
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Comprehensive analysis
            analysis = {
                'url': url,
                'property_type': property_type,
                'city': city,
                'page_title': page_title,
                'total_results': self._extract_total_results(soup),
                'property_samples': self._extract_property_samples(soup),
                'data_fields_found': self._analyze_data_fields(soup),
                'property_variations': self._analyze_property_variations(soup),
                'page_structure': self._analyze_page_structure(soup),
                'anti_scraping_indicators': self._analyze_anti_scraping(soup)
            }
            
            print(f"   ✅ Found {len(analysis['property_samples'])} property samples")
            print(f"   📊 Total results: {analysis['total_results']}")
            print(f"   🔍 Data fields discovered: {len(analysis['data_fields_found'])}")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {'error': str(e), 'url': url}
    
    def _extract_total_results(self, soup):
        """Extract total results count"""
        page_text = soup.get_text()
        
        patterns = [
            r'(\d+(?:,\d+)*)\s*results',
            r'(\d+(?:,\d+)*)\s*properties',
            r'showing\s*(\d+(?:,\d+)*)',
            r'(\d+(?:,\d+)*)\s*listings'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(1).replace(',', '')
        
        return "Unknown"
    
    def _extract_property_samples(self, soup):
        """Extract detailed samples of properties"""
        # Find property links
        property_links = soup.find_all('a', href=True)
        property_containers = []
        
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                # Find the container
                current = link
                for _ in range(20):
                    if current is None:
                        break
                    
                    text = current.get_text().lower()
                    if ('₹' in text and 'sqft' in text and ('bhk' in text or 'bedroom' in text)):
                        if current not in property_containers:
                            property_containers.append(current)
                        break
                    current = current.parent
        
        # Analyze first 10 properties in detail
        samples = []
        for i, container in enumerate(property_containers[:10]):
            sample = self._analyze_property_sample(container, i)
            samples.append(sample)
        
        return samples
    
    def _analyze_property_sample(self, container, index):
        """Detailed analysis of a single property"""
        text = container.get_text()
        
        sample = {
            'index': index,
            'raw_text': text[:500] + "..." if len(text) > 500 else text,
            'extracted_fields': {}
        }
        
        # Extract all possible fields
        field_patterns = {
            # Price fields
            'price_cr': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)',
            'price_lakh': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)',
            'price_range': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh)',
            'price_per_sqft': r'₹\s*([\d,]+)\s*/sqft',
            'emi': r'EMI\s*₹\s*([\d,]+)',
            
            # Area fields
            'carpet_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Carpet\s*Area',
            'buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Built-up\s*Area',
            'super_buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Super\s*Built-up\s*Area',
            'plot_area': r'(\d+(?:,\d+)*)\s*sqft\s*Plot\s*Area',
            'general_area': r'(\d+(?:,\d+)*)\s*sqft',
            
            # Configuration
            'bhk': r'(\d+)\s*BHK',
            'bedrooms': r'(\d+)\s*Bedroom',
            'bathrooms': r'(\d+)\s*Bath',
            'balconies': r'(\d+)\s*Balcon',
            'parking': r'(\d+)\s*Parking',
            
            # Property details
            'floor': r'(\d+)(?:st|nd|rd|th)\s*Floor',
            'total_floors': r'of\s*(\d+)\s*Floors',
            'facing': r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*Facing',
            'age': r'(\d+)\s*Years?\s*Old',
            'furnishing': r'(Furnished|Semi-Furnished|Unfurnished)',
            
            # Status and type
            'construction_status': r'(Ready To Move|Under Construction|New Launch|Partially Ready)',
            'property_type': r'(Apartment|Villa|Independent House|Builder Floor|Studio|Penthouse)',
            'transaction_type': r'(New Booking|Resale)',
            'possession': r'Possession\s*in\s*([^,\n]+)',
            'completion': r'Completion\s*in\s*([^,\n]+)',
            
            # Location
            'locality': r'in\s+([^,\n]+?)(?:,|\s+Mumbai|\s+Delhi|\s+Bangalore)',
            'society': r'^([A-Z][A-Za-z\s&]+?)(?:\s+RESALE|\s+NEW|\s+₹)',
            'builder': r'by\s+([A-Z][A-Za-z\s&]+)',
            
            # Contact and verification
            'rera_id': r'RERA\s*ID[:\s]*([A-Z0-9]+)',
            'agent_type': r'(Owner|Dealer|Builder)',
            'verified': r'(Verified|Premium)',
            'posted_time': r'(\d+[dwmy])\s*ago',
            
            # Amenities
            'amenities': r'(Gym|Swimming Pool|Club House|Garden|Security|Lift|Power Backup|Car Parking)',
        }
        
        for field_name, pattern in field_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                sample['extracted_fields'][field_name] = matches
        
        # Extract links
        links = []
        for link in container.find_all('a', href=True):
            href = link.get('href')
            if 'spid-' in href or 'npxid-' in href:
                links.append(href)
        sample['property_links'] = links
        
        # Extract images
        images = []
        for img in container.find_all('img'):
            src = img.get('src') or img.get('data-src')
            if src:
                images.append(src)
        sample['image_urls'] = images
        
        return sample
    
    def _analyze_data_fields(self, soup):
        """Analyze all data fields present on the page"""
        page_text = soup.get_text()
        
        # Look for structured data
        json_scripts = soup.find_all('script', type='application/ld+json')
        structured_fields = set()
        
        for script in json_scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    structured_fields.update(self._extract_keys_recursive(data))
            except:
                pass
        
        # Look for form fields and labels
        form_fields = set()
        for element in soup.find_all(['input', 'select', 'label']):
            name = element.get('name') or element.get('id') or element.get_text(strip=True)
            if name:
                form_fields.add(name)
        
        return {
            'structured_data_fields': list(structured_fields),
            'form_fields': list(form_fields),
            'total_unique_fields': len(structured_fields) + len(form_fields)
        }
    
    def _extract_keys_recursive(self, obj, prefix=''):
        """Recursively extract keys from nested objects"""
        keys = set()
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                full_key = f"{prefix}.{key}" if prefix else key
                keys.add(full_key)
                keys.update(self._extract_keys_recursive(value, full_key))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                keys.update(self._extract_keys_recursive(item, f"{prefix}[{i}]"))
        
        return keys
    
    def _analyze_property_variations(self, soup):
        """Analyze different types of properties on the page"""
        page_text = soup.get_text()
        
        variations = {
            'property_types': [],
            'price_ranges': [],
            'area_ranges': [],
            'bhk_configurations': [],
            'status_types': []
        }
        
        # Property types
        property_types = re.findall(r'(Apartment|Villa|Independent House|Builder Floor|Studio|Penthouse|Plot)', page_text, re.IGNORECASE)
        variations['property_types'] = list(set(property_types))
        
        # Price ranges
        prices = re.findall(r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh)', page_text, re.IGNORECASE)
        variations['price_ranges'] = prices[:10]  # Sample
        
        # Area ranges
        areas = re.findall(r'(\d+(?:,\d+)*)\s*sqft', page_text, re.IGNORECASE)
        variations['area_ranges'] = areas[:10]  # Sample
        
        # BHK configurations
        bhk_configs = re.findall(r'(\d+)\s*BHK', page_text, re.IGNORECASE)
        variations['bhk_configurations'] = list(set(bhk_configs))
        
        # Status types
        status_types = re.findall(r'(Ready To Move|Under Construction|New Launch|Partially Ready|Resale|New Booking)', page_text, re.IGNORECASE)
        variations['status_types'] = list(set(status_types))
        
        return variations
    
    def _analyze_page_structure(self, soup):
        """Analyze the overall page structure"""
        structure = {
            'total_elements': len(soup.find_all()),
            'scripts': len(soup.find_all('script')),
            'external_scripts': len(soup.find_all('script', src=True)),
            'forms': len(soup.find_all('form')),
            'inputs': len(soup.find_all('input')),
            'images': len(soup.find_all('img')),
            'links': len(soup.find_all('a', href=True)),
            'property_links': len([a for a in soup.find_all('a', href=True) if 'spid-' in a.get('href', '') or 'npxid-' in a.get('href', '')])
        }
        
        return structure
    
    def _analyze_anti_scraping(self, soup):
        """Analyze anti-scraping mechanisms"""
        page_text = soup.get_text().lower()
        
        indicators = {
            'captcha_present': 'captcha' in page_text,
            'bot_detection': any(keyword in page_text for keyword in ['bot', 'robot', 'automated']),
            'rate_limiting': any(keyword in page_text for keyword in ['rate limit', 'too many requests']),
            'javascript_heavy': len(soup.find_all('script')) > 20,
            'dynamic_content': 'react' in str(soup).lower() or 'angular' in str(soup).lower(),
            'external_resources': len(soup.find_all(['script', 'link'], src=True)) > 10
        }
        
        return indicators
    
    def run_deep_research(self):
        """Run comprehensive deep research"""
        print("🚀 STARTING DEEP 99ACRES RESEARCH")
        print("="*80)
        print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Targets: {len(self.research_targets)} different property types/cities")
        
        try:
            self.setup_driver()
            
            successful_analyses = []
            
            for i, (url, prop_type, city) in enumerate(self.research_targets):
                print(f"\n📍 ANALYSIS {i+1}/{len(self.research_targets)}")
                
                analysis = self.analyze_page_comprehensively(url, prop_type, city)
                if analysis and 'error' not in analysis:
                    successful_analyses.append(analysis)
                    self.research_results['detailed_findings'].append(analysis)
                
                # Delay between requests
                time.sleep(random.uniform(8, 15))
            
            # Compile final results
            self.research_results['total_properties_sampled'] = sum(
                len(analysis.get('property_samples', [])) for analysis in successful_analyses
            )
            
            # Save results
            self.save_research_results()
            
            print(f"\n✅ DEEP RESEARCH COMPLETED!")
            print(f"📊 Successfully analyzed: {len(successful_analyses)} pages")
            print(f"🏠 Total property samples: {self.research_results['total_properties_sampled']}")
            print(f"💾 Results saved to: data/deep_99acres_research.json")
            
        except Exception as e:
            print(f"❌ Research failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def save_research_results(self):
        """Save research results"""
        os.makedirs('data', exist_ok=True)
        
        with open('data/deep_99acres_research.json', 'w', encoding='utf-8') as f:
            json.dump(self.research_results, f, indent=2, ensure_ascii=False)
        
        # Generate summary
        self.generate_research_summary()
    
    def generate_research_summary(self):
        """Generate research summary"""
        summary = []
        summary.append("# Deep 99acres Research Summary")
        summary.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary.append("")
        
        summary.append(f"## Overview")
        summary.append(f"- Pages analyzed: {len(self.research_results['detailed_findings'])}")
        summary.append(f"- Property samples: {self.research_results['total_properties_sampled']}")
        summary.append("")
        
        # Property type analysis
        if self.research_results['detailed_findings']:
            summary.append("## Property Types Analyzed")
            for finding in self.research_results['detailed_findings']:
                if 'error' not in finding:
                    prop_type = finding.get('property_type', 'Unknown')
                    city = finding.get('city', 'Unknown')
                    samples = len(finding.get('property_samples', []))
                    total_results = finding.get('total_results', 'Unknown')
                    summary.append(f"- {prop_type} in {city}: {samples} samples, {total_results} total results")
        
        with open('data/deep_research_summary.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary))


if __name__ == "__main__":
    researcher = Deep99acresResearcher()
    researcher.run_deep_research()
