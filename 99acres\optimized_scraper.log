2025-08-11 10:47:40,478 - OptimizedScraper - INFO - Optimized database initialized
2025-08-11 10:47:40,490 - OptimizedScraper - INFO - Loaded 100 property URLs from database
2025-08-11 10:47:40,490 - OptimizedScraper - INFO - Starting optimized parallel scraping of 100 properties
2025-08-11 10:47:45,199 - scraper_instance_3 - ERROR - Instance 3 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff72d396b55+79621]
	GetHandleVerifier [0x0x7ff72d396bb0+79712]
	(No symbol) [0x0x7ff72d12c0ea]
	(No symbol) [0x0x7ff72d169e14]
	(No symbol) [0x0x7ff72d165385]
	(No symbol) [0x0x7ff72d1b8c5e]
	(No symbol) [0x0x7ff72d1b83f0]
	(No symbol) [0x0x7ff72d1aaf13]
	(No symbol) [0x0x7ff72d174151]
	(No symbol) [0x0x7ff72d174ee3]
	GetHandleVerifier [0x0x7ff72d65686d+2962461]
	GetHandleVerifier [0x0x7ff72d650b8d+2938685]
	GetHandleVerifier [0x0x7ff72d66f74d+3064573]
	GetHandleVerifier [0x0x7ff72d3b0c9e+186446]
	GetHandleVerifier [0x0x7ff72d3b8a6f+218655]
	GetHandleVerifier [0x0x7ff72d39f944+115956]
	GetHandleVerifier [0x0x7ff72d39faf9+116393]
	GetHandleVerifier [0x0x7ff72d385f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:47:45,643 - scraper_instance_0 - ERROR - Instance 0 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff72d396b55+79621]
	GetHandleVerifier [0x0x7ff72d396bb0+79712]
	(No symbol) [0x0x7ff72d12c0ea]
	(No symbol) [0x0x7ff72d169e14]
	(No symbol) [0x0x7ff72d165385]
	(No symbol) [0x0x7ff72d1b8c5e]
	(No symbol) [0x0x7ff72d1b83f0]
	(No symbol) [0x0x7ff72d1aaf13]
	(No symbol) [0x0x7ff72d174151]
	(No symbol) [0x0x7ff72d174ee3]
	GetHandleVerifier [0x0x7ff72d65686d+2962461]
	GetHandleVerifier [0x0x7ff72d650b8d+2938685]
	GetHandleVerifier [0x0x7ff72d66f74d+3064573]
	GetHandleVerifier [0x0x7ff72d3b0c9e+186446]
	GetHandleVerifier [0x0x7ff72d3b8a6f+218655]
	GetHandleVerifier [0x0x7ff72d39f944+115956]
	GetHandleVerifier [0x0x7ff72d39faf9+116393]
	GetHandleVerifier [0x0x7ff72d385f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:47:48,805 - scraper_instance_2 - ERROR - Instance 2 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff72d396b55+79621]
	GetHandleVerifier [0x0x7ff72d396bb0+79712]
	(No symbol) [0x0x7ff72d12c0ea]
	(No symbol) [0x0x7ff72d169e14]
	(No symbol) [0x0x7ff72d165385]
	(No symbol) [0x0x7ff72d1b8c5e]
	(No symbol) [0x0x7ff72d1b83f0]
	(No symbol) [0x0x7ff72d1aaf13]
	(No symbol) [0x0x7ff72d174151]
	(No symbol) [0x0x7ff72d174ee3]
	GetHandleVerifier [0x0x7ff72d65686d+2962461]
	GetHandleVerifier [0x0x7ff72d650b8d+2938685]
	GetHandleVerifier [0x0x7ff72d66f74d+3064573]
	GetHandleVerifier [0x0x7ff72d3b0c9e+186446]
	GetHandleVerifier [0x0x7ff72d3b8a6f+218655]
	GetHandleVerifier [0x0x7ff72d39f944+115956]
	GetHandleVerifier [0x0x7ff72d39faf9+116393]
	GetHandleVerifier [0x0x7ff72d385f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:47:49,250 - OptimizedScraper - ERROR - Instance 3 failed to setup driver
2025-08-11 10:47:49,252 - OptimizedScraper - INFO - Instance 1/4 completed
2025-08-11 10:47:49,699 - OptimizedScraper - ERROR - Instance 0 failed to setup driver
2025-08-11 10:47:49,700 - OptimizedScraper - INFO - Instance 2/4 completed
2025-08-11 10:47:52,874 - OptimizedScraper - ERROR - Instance 2 failed to setup driver
2025-08-11 10:47:52,875 - OptimizedScraper - INFO - Instance 3/4 completed
2025-08-11 10:48:44,359 - scraper_instance_1 - ERROR - Instance 1 driver setup failed: Message: session not created: DevToolsActivePort file doesn't exist
Stacktrace:
	GetHandleVerifier [0x0x7ff72d396b55+79621]
	GetHandleVerifier [0x0x7ff72d396bb0+79712]
	(No symbol) [0x0x7ff72d12c0ea]
	(No symbol) [0x0x7ff72d16f6f9]
	(No symbol) [0x0x7ff72d16aa9f]
	(No symbol) [0x0x7ff72d165385]
	(No symbol) [0x0x7ff72d1b8c5e]
	(No symbol) [0x0x7ff72d1b83f0]
	(No symbol) [0x0x7ff72d1aaf13]
	(No symbol) [0x0x7ff72d174151]
	(No symbol) [0x0x7ff72d174ee3]
	GetHandleVerifier [0x0x7ff72d65686d+2962461]
	GetHandleVerifier [0x0x7ff72d650b8d+2938685]
	GetHandleVerifier [0x0x7ff72d66f74d+3064573]
	GetHandleVerifier [0x0x7ff72d3b0c9e+186446]
	GetHandleVerifier [0x0x7ff72d3b8a6f+218655]
	GetHandleVerifier [0x0x7ff72d39f944+115956]
	GetHandleVerifier [0x0x7ff72d39faf9+116393]
	GetHandleVerifier [0x0x7ff72d385f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:48:48,423 - OptimizedScraper - ERROR - Instance 1 failed to setup driver
2025-08-11 10:48:48,424 - OptimizedScraper - INFO - Instance 4/4 completed
2025-08-11 10:48:48,425 - OptimizedScraper - INFO - Parallel scraping completed: 0/100 properties in 1.1 minutes
2025-08-11 10:48:48,428 - OptimizedScraper - INFO - Performance report saved: optimized_scraper_report_20250811_104848.json
2025-08-11 10:56:17,669 - OptimizedScraper - INFO - Optimized database initialized
2025-08-11 10:56:17,670 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:17] "POST /api/start HTTP/1.1" 200 -
2025-08-11 10:56:17,682 - OptimizedScraper - INFO - Loaded 132 property URLs from database
2025-08-11 10:56:17,683 - OptimizedScraper - INFO - Starting optimized parallel scraping of 132 properties
2025-08-11 10:56:20,607 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:20] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:21,317 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:21] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:21,347 - scraper_instance_0 - ERROR - Instance 0 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6dd846b55+79621]
	GetHandleVerifier [0x0x7ff6dd846bb0+79712]
	(No symbol) [0x0x7ff6dd5dc0ea]
	(No symbol) [0x0x7ff6dd619e14]
	(No symbol) [0x0x7ff6dd615385]
	(No symbol) [0x0x7ff6dd668c5e]
	(No symbol) [0x0x7ff6dd6683f0]
	(No symbol) [0x0x7ff6dd65af13]
	(No symbol) [0x0x7ff6dd624151]
	(No symbol) [0x0x7ff6dd624ee3]
	GetHandleVerifier [0x0x7ff6ddb0686d+2962461]
	GetHandleVerifier [0x0x7ff6ddb00b8d+2938685]
	GetHandleVerifier [0x0x7ff6ddb1f74d+3064573]
	GetHandleVerifier [0x0x7ff6dd860c9e+186446]
	GetHandleVerifier [0x0x7ff6dd868a6f+218655]
	GetHandleVerifier [0x0x7ff6dd84f944+115956]
	GetHandleVerifier [0x0x7ff6dd84faf9+116393]
	GetHandleVerifier [0x0x7ff6dd835f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:56:24,059 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:24] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:25,415 - OptimizedScraper - ERROR - Instance 0 failed to setup driver
2025-08-11 10:56:25,419 - OptimizedScraper - INFO - Instance 1/4 completed
2025-08-11 10:56:25,756 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:25] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:26,126 - scraper_instance_2 - ERROR - Instance 2 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6dd846b55+79621]
	GetHandleVerifier [0x0x7ff6dd846bb0+79712]
	(No symbol) [0x0x7ff6dd5dc0ea]
	(No symbol) [0x0x7ff6dd619e14]
	(No symbol) [0x0x7ff6dd615385]
	(No symbol) [0x0x7ff6dd668c5e]
	(No symbol) [0x0x7ff6dd6683f0]
	(No symbol) [0x0x7ff6dd65af13]
	(No symbol) [0x0x7ff6dd624151]
	(No symbol) [0x0x7ff6dd624ee3]
	GetHandleVerifier [0x0x7ff6ddb0686d+2962461]
	GetHandleVerifier [0x0x7ff6ddb00b8d+2938685]
	GetHandleVerifier [0x0x7ff6ddb1f74d+3064573]
	GetHandleVerifier [0x0x7ff6dd860c9e+186446]
	GetHandleVerifier [0x0x7ff6dd868a6f+218655]
	GetHandleVerifier [0x0x7ff6dd84f944+115956]
	GetHandleVerifier [0x0x7ff6dd84faf9+116393]
	GetHandleVerifier [0x0x7ff6dd835f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:56:26,266 - scraper_instance_1 - ERROR - Instance 1 driver setup failed: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6dd846b55+79621]
	GetHandleVerifier [0x0x7ff6dd846bb0+79712]
	(No symbol) [0x0x7ff6dd5dc0ea]
	(No symbol) [0x0x7ff6dd619e14]
	(No symbol) [0x0x7ff6dd615385]
	(No symbol) [0x0x7ff6dd668c5e]
	(No symbol) [0x0x7ff6dd6683f0]
	(No symbol) [0x0x7ff6dd65af13]
	(No symbol) [0x0x7ff6dd624151]
	(No symbol) [0x0x7ff6dd624ee3]
	GetHandleVerifier [0x0x7ff6ddb0686d+2962461]
	GetHandleVerifier [0x0x7ff6ddb00b8d+2938685]
	GetHandleVerifier [0x0x7ff6ddb1f74d+3064573]
	GetHandleVerifier [0x0x7ff6dd860c9e+186446]
	GetHandleVerifier [0x0x7ff6dd868a6f+218655]
	GetHandleVerifier [0x0x7ff6dd84f944+115956]
	GetHandleVerifier [0x0x7ff6dd84faf9+116393]
	GetHandleVerifier [0x0x7ff6dd835f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:56:28,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:28] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:29,757 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:29] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:30,189 - OptimizedScraper - ERROR - Instance 2 failed to setup driver
2025-08-11 10:56:30,190 - OptimizedScraper - INFO - Instance 2/4 completed
2025-08-11 10:56:30,329 - OptimizedScraper - ERROR - Instance 1 failed to setup driver
2025-08-11 10:56:30,330 - OptimizedScraper - INFO - Instance 3/4 completed
2025-08-11 10:56:32,052 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:32] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:33,748 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:33] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:36,071 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:36] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:37,763 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:37] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:40,052 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:41,749 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:41] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:44,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:44] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:45,746 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:45] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:48,068 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:48] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:50,059 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:50] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:51,758 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:51] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:54,073 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:54] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:55,751 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:55] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:56:58,059 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:56:58] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:00,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:00] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:01,750 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:01] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:04,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:04] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:05,759 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:05] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:08,052 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:08] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:10,048 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:10] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:11,762 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:11] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:14,109 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:14] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:15,752 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:15] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:18,053 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:18] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:20,067 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:20] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:21,074 - scraper_instance_3 - ERROR - Instance 3 driver setup failed: Message: session not created: DevToolsActivePort file doesn't exist
Stacktrace:
	GetHandleVerifier [0x0x7ff6dd846b55+79621]
	GetHandleVerifier [0x0x7ff6dd846bb0+79712]
	(No symbol) [0x0x7ff6dd5dc0ea]
	(No symbol) [0x0x7ff6dd61f6f9]
	(No symbol) [0x0x7ff6dd61aa9f]
	(No symbol) [0x0x7ff6dd615385]
	(No symbol) [0x0x7ff6dd668c5e]
	(No symbol) [0x0x7ff6dd6683f0]
	(No symbol) [0x0x7ff6dd65af13]
	(No symbol) [0x0x7ff6dd624151]
	(No symbol) [0x0x7ff6dd624ee3]
	GetHandleVerifier [0x0x7ff6ddb0686d+2962461]
	GetHandleVerifier [0x0x7ff6ddb00b8d+2938685]
	GetHandleVerifier [0x0x7ff6ddb1f74d+3064573]
	GetHandleVerifier [0x0x7ff6dd860c9e+186446]
	GetHandleVerifier [0x0x7ff6dd868a6f+218655]
	GetHandleVerifier [0x0x7ff6dd84f944+115956]
	GetHandleVerifier [0x0x7ff6dd84faf9+116393]
	GetHandleVerifier [0x0x7ff6dd835f28+10968]
	BaseThreadInitThunk [0x0x7ff90e7ce8d7+23]
	RtlUserThreadStart [0x0x7ff910a3c34c+44]

2025-08-11 10:57:21,752 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:21] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:57:25,150 - OptimizedScraper - ERROR - Instance 3 failed to setup driver
2025-08-11 10:57:25,151 - OptimizedScraper - INFO - Instance 4/4 completed
2025-08-11 10:57:25,153 - OptimizedScraper - INFO - Parallel scraping completed: 0/132 properties in 1.1 minutes
2025-08-11 10:57:40,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:57:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:58:40,075 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:58:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 10:59:39,750 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 10:59:39] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:28,121 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:28] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:29,334 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:29] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:31,625 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:31] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:33,764 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:33] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:36,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:36] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:37,754 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:37] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:40,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:41,755 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:41] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:44,056 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:44] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:45,754 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:45] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:48,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:48] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:50,070 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:50] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:51,757 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:51] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:54,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:54] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:55,760 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:55] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:00:58,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:00:58] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:00,073 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:00] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:01,762 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:01] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:04,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:04] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:05,746 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:05] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:08,055 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:08] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:09,746 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:09] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:12,054 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:12] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:13,775 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:13] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:16,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:16] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:17,751 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:17] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:20,051 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:20] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:21,751 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:21] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:24,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:24] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:25,749 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:25] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:28,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:28] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:29,754 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:29] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:32,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:32] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:33,756 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:33] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:36,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:36] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:37,758 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:37] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:40,063 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:41,755 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:41] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:44,069 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:44] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:46,069 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:46] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:47,764 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:47] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:50,828 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:50] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:51,314 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:51] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:53,636 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:53] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:55,748 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:55] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:58,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:58] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:01:59,759 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:01:59] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:02,063 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:02] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:03,750 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:03] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:06,071 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:06] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:07,774 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:07] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:10,075 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:10] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:12,492 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:12] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:14,063 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:14] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:15,823 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:15] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:18,074 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:18] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:19,762 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:19] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:22,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:22] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:23,767 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:23] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:26,068 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:26] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:27,756 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:27] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:30,063 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:30] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:31,775 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:31] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:34,087 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:34] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:35,757 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:35] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:38,067 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:38] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:40,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:41,758 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:41] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:44,056 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:44] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:45,749 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:45] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:48,081 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:48] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:49,761 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:49] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:52,074 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:52] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:02:53,750 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:02:53] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:03:40,071 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:03:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:04:40,061 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:04:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:04:56,329 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:04:56] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:04:57,320 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:04:57] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:04:59,622 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:04:59] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:01,328 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:01] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:04,068 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:04] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:06,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:06] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:07,771 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:07] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:10,059 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:10] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:11,757 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:11] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:14,069 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:14] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:16,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:16] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:17,759 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:17] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:20,063 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:20] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:21,768 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:21] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:24,066 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:24] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:25,752 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:25] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:28,068 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:28] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:29,751 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:29] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:32,064 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:32] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:33,761 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:33] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:36,069 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:36] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:37,754 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:37] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:40,065 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:41,766 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:41] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:44,072 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:44] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:46,070 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:46] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:47,768 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:47] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:50,059 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:50] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:51,765 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:51] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:54,065 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:54] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:56,061 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:56] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:05:57,761 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:05:57] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:06:00,069 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:06:00] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:06:01,752 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:06:01] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:06:40,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:06:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:07:40,062 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:07:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:08:40,060 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:08:40] "GET /api/stats HTTP/1.1" 200 -
2025-08-11 11:09:40,085 - werkzeug - INFO - 127.0.0.1 - - [11/Aug/2025 11:09:40] "GET /api/stats HTTP/1.1" 200 -
