#!/usr/bin/env python3
"""
Ultimate Production Dashboard
Restored all optimizations: concurrent processing, time controls, browser optimizations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
import threading
import time
import queue
from datetime import datetime, timedelta
import psutil
import logging
from dataclasses import dataclass
from typing import Tuple, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

app = Flask(__name__)

@dataclass
class OptimizedScrapingConfig:
    """Advanced configuration for optimized scraping"""
    # Concurrent Processing
    max_concurrent_instances: int = 4
    max_properties_per_instance: int = 250
    
    # Time Controls
    base_delay_range: Tuple[float, float] = (1.5, 3.0)  # Optimized delays
    page_load_timeout: int = 15
    element_wait_timeout: int = 8
    
    # Browser Optimizations
    headless: bool = True
    enable_images: bool = False
    enable_css: bool = False
    enable_javascript: bool = True
    
    # Performance Settings
    memory_cleanup_interval: int = 25
    session_rotation_interval: int = 50
    retry_attempts: int = 3
    retry_delay: float = 2.0
    
    # Batch Processing
    batch_size: int = 20
    smart_batching: bool = True
    
    # Anti-Detection
    user_agent_rotation: bool = True
    viewport_randomization: bool = True
    request_headers_rotation: bool = True

class OptimizedPropertyScraper:
    """Optimized property scraper with all performance enhancements"""
    
    def __init__(self, config: OptimizedScrapingConfig, instance_id: int = 0):
        self.config = config
        self.instance_id = instance_id
        self.driver = None
        self.logger = logging.getLogger(f"OptimizedScraper-{instance_id}")
        
        # Performance tracking
        self.extraction_times = []
        self.success_count = 0
        self.failure_count = 0
        
        # User agents for rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"
        ]
    
    def setup_optimized_driver(self):
        """Setup optimized Chrome driver with all performance enhancements"""
        try:
            chrome_options = Options()
            
            # Basic settings
            if self.config.headless:
                chrome_options.add_argument("--headless")
            
            # Performance optimizations
            if not self.config.enable_images:
                chrome_options.add_argument("--disable-images")
                chrome_options.add_experimental_option("prefs", {
                    "profile.managed_default_content_settings.images": 2
                })
            
            if not self.config.enable_css:
                chrome_options.add_argument("--disable-web-security")
                chrome_options.add_experimental_option("prefs", {
                    "profile.managed_default_content_settings.stylesheets": 2
                })
            
            # Speed optimizations
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            
            # Memory optimizations
            chrome_options.add_argument("--memory-pressure-off")
            chrome_options.add_argument("--max_old_space_size=4096")
            
            # Anti-detection
            if self.config.user_agent_rotation:
                user_agent = random.choice(self.user_agents)
                chrome_options.add_argument(f"--user-agent={user_agent}")
            
            if self.config.viewport_randomization:
                width = random.randint(1366, 1920)
                height = random.randint(768, 1080)
                chrome_options.add_argument(f"--window-size={width},{height}")
            
            # Page load strategy
            chrome_options.add_argument("--page-load-strategy=eager")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(self.config.page_load_timeout)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup driver: {str(e)}")
            return False
    
    def extract_optimized_property_data(self, url: str) -> Dict[str, Any]:
        """Extract property data with optimized performance"""
        start_time = time.time()
        
        try:
            # Navigate with timeout
            self.driver.get(url)
            
            # Smart wait for content
            try:
                WebDriverWait(self.driver, self.config.element_wait_timeout).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except TimeoutException:
                pass  # Continue anyway
            
            # Get page source and parse
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Extract key data points (optimized extraction)
            property_data = {
                'property_url': url,
                'title': self._extract_title(soup),
                'price_display': self._extract_price(soup),
                'bhk_config': self._extract_bhk(soup),
                'area': self._extract_area(soup),
                'location': self._extract_location(soup),
                'property_type': self._extract_property_type(soup),
                'extraction_time': time.time() - start_time,
                'scraped_at': datetime.now().isoformat(),
                'instance_id': self.instance_id
            }
            
            self.extraction_times.append(property_data['extraction_time'])
            self.success_count += 1
            
            return property_data
            
        except Exception as e:
            self.failure_count += 1
            self.logger.error(f"Error extracting {url}: {str(e)}")
            return None
    
    def _extract_title(self, soup):
        """Extract property title"""
        selectors = [
            'h1[data-label="PC_Heading"]',
            'h1.pdp-property-title',
            'h1',
            '.property-title'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_price(self, soup):
        """Extract property price"""
        selectors = [
            '[data-label="PC_Price"]',
            '.price-info',
            '.property-price',
            '.price'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_bhk(self, soup):
        """Extract BHK configuration"""
        # Look for BHK in title or specific elements
        title = self._extract_title(soup)
        if title:
            import re
            bhk_match = re.search(r'(\d+)\s*BHK', title, re.IGNORECASE)
            if bhk_match:
                return f"{bhk_match.group(1)} BHK"
        return None
    
    def _extract_area(self, soup):
        """Extract property area"""
        selectors = [
            '[data-label="PC_BuiltUpArea"]',
            '.area-info',
            '.property-area'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_location(self, soup):
        """Extract property location"""
        selectors = [
            '[data-label="PC_Locality"]',
            '.location-info',
            '.property-location'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_property_type(self, soup):
        """Extract property type"""
        title = self._extract_title(soup)
        if title:
            if 'apartment' in title.lower() or 'flat' in title.lower():
                return 'Apartment'
            elif 'villa' in title.lower():
                return 'Villa'
            elif 'house' in title.lower():
                return 'House'
        return 'Apartment'  # Default
    
    def save_to_database(self, property_data: Dict[str, Any]) -> bool:
        """Save property data to database"""
        try:
            conn = sqlite3.connect('data/individual_properties_comprehensive.db')
            cursor = conn.cursor()
            
            # Insert data
            cursor.execute("""
                INSERT OR REPLACE INTO individual_properties 
                (property_url, title, price_display, bhk_config, area, location, property_type, extraction_time, scraped_at, instance_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                property_data.get('property_url'),
                property_data.get('title'),
                property_data.get('price_display'),
                property_data.get('bhk_config'),
                property_data.get('area'),
                property_data.get('location'),
                property_data.get('property_type'),
                property_data.get('extraction_time'),
                property_data.get('scraped_at'),
                property_data.get('instance_id')
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Database save error: {str(e)}")
            return False
    
    def intelligent_delay(self):
        """Intelligent delay with randomization"""
        min_delay, max_delay = self.config.base_delay_range
        delay = random.uniform(min_delay, max_delay)
        
        # Adaptive delay based on performance
        if self.extraction_times:
            avg_time = sum(self.extraction_times[-10:]) / len(self.extraction_times[-10:])
            if avg_time > 5.0:  # If extraction is slow, reduce delay
                delay *= 0.8
            elif avg_time < 2.0:  # If extraction is fast, can afford longer delay
                delay *= 1.2
        
        time.sleep(delay)
    
    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

class UltimateProductionMonitor:
    """Ultimate production monitor with all optimizations restored"""
    
    def __init__(self):
        self.scrapers = {}
        self.scraper_threads = {}
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.config = OptimizedScrapingConfig()
        
        # Advanced progress tracking
        self.progress = {
            'total_urls': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'current_batch': 0,
            'total_batches': 0,
            'instances_running': 0,
            'avg_time_per_property': 0,
            'properties_per_hour': 0,
            'success_rate': 0,
            'estimated_completion': '',
            'current_urls': {},  # Per instance
            'instance_stats': {}  # Per instance performance
        }
        
        # Performance metrics
        self.performance_metrics = {
            'total_extraction_time': 0,
            'fastest_extraction': float('inf'),
            'slowest_extraction': 0,
            'memory_usage_history': [],
            'cpu_usage_history': [],
            'concurrent_performance': {}
        }
        
        # Logs with categories
        self.logs = []
        self.max_logs = 100
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("UltimateMonitor")
    
    def add_log(self, level, message):
        """Add categorized log entry"""
        log_entry = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'level': level,
            'message': message,
            'category': self._categorize_log(message)
        }
        self.logs.append(log_entry)
        
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)
        
        print(f"[{log_entry['timestamp']}] {level}: {message}")
    
    def _categorize_log(self, message):
        """Categorize log messages"""
        if any(word in message.lower() for word in ['success', 'completed', 'saved', '✅']):
            return 'success'
        elif any(word in message.lower() for word in ['error', 'failed', 'exception', '❌']):
            return 'error'
        elif any(word in message.lower() for word in ['warning', 'skip', 'duplicate', '⚠️']):
            return 'warning'
        elif any(word in message.lower() for word in ['concurrent', 'parallel', 'instance', '🚀']):
            return 'performance'
        else:
            return 'info'
    
    def get_comprehensive_sources(self):
        """Get comprehensive URL sources"""
        try:
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            # Total available URLs
            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE 'https://www.99acres.com/%'")
            total_urls = cursor.fetchone()[0]
            
            # City breakdown
            cursor.execute("""
                SELECT city, COUNT(*) FROM properties 
                WHERE property_url LIKE 'https://www.99acres.com/%'
                GROUP BY city ORDER BY COUNT(*) DESC LIMIT 15
            """)
            city_breakdown = cursor.fetchall()
            
            conn.close()
            
            # Individual properties database
            extracted_count = 0
            try:
                conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT COUNT(*) FROM individual_properties")
                extracted_count = cursor2.fetchone()[0]
                conn2.close()
            except:
                extracted_count = 0
            
            return {
                'individual_listings': {
                    'name': 'Optimized Individual Property Listings',
                    'total': total_urls,
                    'extracted': extracted_count,
                    'remaining': total_urls - extracted_count,
                    'progress_percentage': (extracted_count/total_urls)*100 if total_urls > 0 else 0,
                    'description': f'Ultra-fast concurrent scraping • {total_urls} properties • 4x parallel processing',
                    'city_breakdown': city_breakdown,
                    'optimization_status': 'ENABLED',
                    'concurrent_instances': self.config.max_concurrent_instances,
                    'estimated_speed': f"{self.config.max_concurrent_instances * 1200} properties/hour"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sources: {str(e)}")
            return {}
    
    def load_urls_for_scraping(self, max_properties=50, skip_existing=True, city_filter=None):
        """Load URLs with advanced filtering"""
        try:
            self.add_log('INFO', f"🔍 Loading URLs (max: {max_properties}, skip: {skip_existing}, city: {city_filter or 'All'})")
            
            # Build query
            base_query = "SELECT property_url FROM properties WHERE property_url LIKE 'https://www.99acres.com/%'"
            params = []
            
            if city_filter and city_filter != 'All':
                base_query += " AND city = ?"
                params.append(city_filter)
            
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            cursor.execute(base_query + " ORDER BY RANDOM()", params)
            all_urls = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if skip_existing:
                try:
                    conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                    cursor2 = conn2.cursor()
                    cursor2.execute("SELECT property_url FROM individual_properties WHERE property_url IS NOT NULL")
                    extracted_urls = set(row[0] for row in cursor2.fetchall())
                    conn2.close()
                    
                    filtered_urls = [url for url in all_urls if url not in extracted_urls]
                    self.add_log('INFO', f"📊 Filtered {len(all_urls)} -> {len(filtered_urls)} URLs (skipped {len(extracted_urls)} existing)")
                    urls = filtered_urls[:max_properties]
                except Exception as e:
                    self.add_log('WARNING', f"⚠️ Could not filter existing URLs: {str(e)}")
                    urls = all_urls[:max_properties]
            else:
                urls = all_urls[:max_properties]
            
            self.add_log('SUCCESS', f"✅ Selected {len(urls)} URLs for concurrent processing")
            return urls
            
        except Exception as e:
            self.add_log('ERROR', f"❌ Error loading URLs: {str(e)}")
            return []
