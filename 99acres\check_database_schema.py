#!/usr/bin/env python3
"""
Check Database Schema
"""

import sqlite3

def check_database_schema():
    """Check the database schema to understand the structure"""
    
    db_path = 'data/99acres_properties.db'
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 Found tables: {[table[0] for table in tables]}")
        
        # Check properties table schema
        cursor.execute("PRAGMA table_info(properties)")
        columns = cursor.fetchall()
        
        print(f"\n📋 Properties table schema:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # Get sample data
        cursor.execute("SELECT * FROM properties LIMIT 3")
        sample_data = cursor.fetchall()
        
        print(f"\n📄 Sample data (first 3 rows):")
        for i, row in enumerate(sample_data, 1):
            print(f"   Row {i}: {row}")
        
        # Count total records
        cursor.execute("SELECT COUNT(*) FROM properties")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 Total properties in database: {total_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {str(e)}")

if __name__ == "__main__":
    print("🔍 Checking Database Schema")
    print("=" * 40)
    check_database_schema()
