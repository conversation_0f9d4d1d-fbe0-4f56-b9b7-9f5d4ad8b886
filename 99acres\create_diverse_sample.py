#!/usr/bin/env python3
"""
Create Diverse Sample for Field Analysis
Simple script to create a diverse sample of properties for comprehensive field analysis
"""

import sqlite3
import json
from datetime import datetime

def create_diverse_sample():
    """Create a diverse sample of properties for analysis"""
    
    db_path = 'data/99acres_properties.db'
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🎯 Creating Diverse Sample for Field Analysis")
        print("=" * 50)
        
        # Get diverse sample based on different criteria
        diverse_sample = []
        
        # 1. Different bedroom configurations
        print("📋 Selecting properties by bedroom configuration...")
        
        # 1 BHK properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE bedrooms = '1' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 2
        """)
        bhk1_props = cursor.fetchall()
        diverse_sample.extend(bhk1_props)
        print(f"   1 BHK: {len(bhk1_props)} properties")
        
        # 2 BHK properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE bedrooms = '2' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        bhk2_props = cursor.fetchall()
        diverse_sample.extend(bhk2_props)
        print(f"   2 BHK: {len(bhk2_props)} properties")
        
        # 3 BHK properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE bedrooms = '3' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 8
        """)
        bhk3_props = cursor.fetchall()
        diverse_sample.extend(bhk3_props)
        print(f"   3 BHK: {len(bhk3_props)} properties")
        
        # 4 BHK properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE bedrooms = '4' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 6
        """)
        bhk4_props = cursor.fetchall()
        diverse_sample.extend(bhk4_props)
        print(f"   4 BHK: {len(bhk4_props)} properties")
        
        # 5+ BHK properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE bedrooms IN ('5', '6', '7') AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 4
        """)
        bhk5plus_props = cursor.fetchall()
        diverse_sample.extend(bhk5plus_props)
        print(f"   5+ BHK: {len(bhk5plus_props)} properties")
        
        # 2. Different localities
        print(f"\n📍 Selecting properties by locality...")
        
        # Dombivli East
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE locality = 'Dombivli East' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        dombivli_props = cursor.fetchall()
        print(f"   Dombivli East: {len(dombivli_props)} properties")
        
        # Waghbil
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE locality = 'Waghbil' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        waghbil_props = cursor.fetchall()
        print(f"   Waghbil: {len(waghbil_props)} properties")
        
        # Other localities
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE locality NOT IN ('Dombivli East', 'Waghbil') AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        other_locality_props = cursor.fetchall()
        print(f"   Other localities: {len(other_locality_props)} properties")
        
        # Combine all and remove duplicates
        all_urls = set()
        final_sample = []
        
        for prop_list in [diverse_sample, dombivli_props, waghbil_props, other_locality_props]:
            for prop in prop_list:
                url = prop[0]
                if url not in all_urls:
                    all_urls.add(url)
                    final_sample.append(prop)
        
        # Format the final sample
        diverse_urls = []
        for i, (url, title, price, locality, bedrooms, city) in enumerate(final_sample, 1):
            diverse_urls.append({
                'index': i,
                'url': url,
                'title': title,
                'price': price,
                'locality': locality,
                'bedrooms': bedrooms,
                'city': city
            })
        
        # Save diverse sample
        diverse_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_diverse_properties': len(diverse_urls),
            'selection_criteria': {
                'bedroom_configurations': ['1 BHK', '2 BHK', '3 BHK', '4 BHK', '5+ BHK'],
                'localities': ['Dombivli East', 'Waghbil', 'Other localities'],
                'property_types': ['Apartment', 'Flat']
            },
            'diverse_sample': diverse_urls
        }
        
        with open('diverse_property_sample.json', 'w', encoding='utf-8') as f:
            json.dump(diverse_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Created diverse sample of {len(diverse_urls)} properties")
        print(f"💾 Saved to: diverse_property_sample.json")
        
        # Print sample
        print(f"\n📋 Sample Properties for Analysis:")
        for prop in diverse_urls[:15]:
            print(f"   {prop['index']:2d}. {prop['title'][:45]}...")
            print(f"       {prop['bedrooms']} BHK | {prop['price']} | {prop['locality']}")
            print(f"       URL: {prop['url']}")
            print()
        
        if len(diverse_urls) > 15:
            print(f"   ... and {len(diverse_urls) - 15} more properties")
        
        conn.close()
        return diverse_urls
        
    except Exception as e:
        print(f"❌ Error creating diverse sample: {str(e)}")
        return []

if __name__ == "__main__":
    diverse_sample = create_diverse_sample()
    
    if diverse_sample:
        print(f"\n🎉 Successfully created diverse sample of {len(diverse_sample)} properties")
        print("📄 Ready for comprehensive field analysis!")
        print("🔍 This sample includes:")
        print("   - Different bedroom configurations (1-7 BHK)")
        print("   - Multiple localities in Thane region")
        print("   - Various price ranges")
        print("   - Different property types")
    else:
        print("❌ Failed to create diverse sample")
