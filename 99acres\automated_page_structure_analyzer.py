#!/usr/bin/env python3
"""
Automated Page Structure Analyzer for 99acres Individual Properties
Systematically analyzes page structure, selectors, and data patterns
"""

import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
from collections import defaultdict

class AutomatedPageStructureAnalyzer:
    def __init__(self):
        self.driver = None
        self.analysis_results = []
        self.common_selectors = defaultdict(set)
        self.field_patterns = defaultdict(list)
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        print("✅ Browser initialized for automated analysis")
    
    def analyze_property_page_structure(self, property_info):
        """Comprehensive analysis of a property page structure"""
        url = property_info['url']
        category = property_info['category']
        
        print(f"\n🔍 Analyzing: {property_info['title'][:50]}...")
        print(f"   Category: {category}")
        print(f"   URL: {url}")
        
        try:
            # Navigate to page
            self.driver.get(url)
            time.sleep(5)  # Wait for page load
            
            # Wait for key elements to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                print("   ⚠️ Page load timeout")
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Comprehensive analysis
            analysis = {
                'property_info': property_info,
                'timestamp': datetime.now().isoformat(),
                'page_structure': self.analyze_page_structure(soup),
                'data_extraction': self.extract_all_data_fields(soup),
                'selector_mapping': self.map_css_selectors(soup),
                'javascript_elements': self.analyze_javascript_elements(),
                'image_gallery': self.analyze_image_gallery(soup),
                'contact_info': self.extract_contact_information(soup),
                'amenities': self.extract_amenities(soup),
                'location_data': self.extract_location_data(soup)
            }
            
            self.analysis_results.append(analysis)
            
            # Update common patterns
            self.update_common_patterns(analysis)
            
            print(f"   ✅ Analysis complete: {len(analysis['data_extraction'])} fields extracted")
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing page: {str(e)}")
            return None
    
    def analyze_page_structure(self, soup):
        """Analyze overall page structure"""
        structure = {
            'total_elements': len(soup.find_all()),
            'div_count': len(soup.find_all('div')),
            'span_count': len(soup.find_all('span')),
            'img_count': len(soup.find_all('img')),
            'script_count': len(soup.find_all('script')),
            'unique_classes': len(set([cls for elem in soup.find_all(class_=True) for cls in elem.get('class', [])])),
            'unique_ids': len(set([elem.get('id') for elem in soup.find_all(id=True)])),
            'main_containers': []
        }
        
        # Find main content containers
        main_containers = soup.find_all(['div', 'section', 'main'], class_=re.compile(r'(main|content|property|detail)', re.I))
        for container in main_containers[:5]:
            if container.get('class'):
                structure['main_containers'].append({
                    'tag': container.name,
                    'classes': container.get('class'),
                    'id': container.get('id')
                })
        
        return structure
    
    def extract_all_data_fields(self, soup):
        """Extract all possible data fields from the page"""
        data = {}
        page_text = soup.get_text()
        
        # 1. Price Information (comprehensive patterns)
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr(?:ore)?', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh?', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/\s*sqft', 'price_per_sqft'),
            (r'₹\s*([\d,]+)\s*/\s*month', 'rent_per_month'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges'),
            (r'Security\s*Deposit\s*₹\s*([\d,]+)', 'security_deposit'),
            (r'Registration\s*₹\s*([\d,]+)', 'registration_charges'),
            (r'Stamp\s*Duty\s*₹\s*([\d,]+)', 'stamp_duty')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches
        
        # 2. Property Specifications
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'Carpet\s*Area\s*(\d+)', 'carpet_area'),
            (r'Built-up\s*Area\s*(\d+)', 'builtup_area'),
            (r'Super\s*Area\s*(\d+)', 'super_area'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches
        
        # 3. Builder/Developer Information
        builder_patterns = [
            (r'By\s*([A-Z][^,\n]+(?:Group|Developers?|Builders?|Construction|Realty|Properties))', 'builder_name'),
            (r'Developer\s*:\s*([^,\n]+)', 'developer_name'),
            (r'RERA\s*(?:ID|Number)\s*:\s*([A-Z0-9]+)', 'rera_number'),
            (r'Project\s*:\s*([^,\n]+)', 'project_name')
        ]
        
        for pattern, field_name in builder_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches
        
        # 4. Contact Information
        contact_patterns = [
            (r'(\+91[\s-]?\d{10})', 'phone_numbers'),
            (r'(\d{10})', 'mobile_numbers'),
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 'email_addresses')
        ]
        
        for pattern, field_name in contact_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                data[field_name] = list(set(matches))
        
        return data
    
    def map_css_selectors(self, soup):
        """Map CSS selectors for key data elements"""
        selectors = {
            'price_selectors': [],
            'specification_selectors': [],
            'amenity_selectors': [],
            'contact_selectors': [],
            'image_selectors': [],
            'description_selectors': []
        }
        
        # Price selectors
        price_elements = soup.find_all(text=re.compile(r'₹|Price|EMI|Rent', re.IGNORECASE))
        for elem in price_elements[:10]:
            parent = elem.parent
            if parent and (parent.get('class') or parent.get('id')):
                selectors['price_selectors'].append({
                    'tag': parent.name,
                    'classes': parent.get('class'),
                    'id': parent.get('id'),
                    'text_sample': str(elem)[:50]
                })
        
        # Specification selectors
        spec_keywords = ['BHK', 'sqft', 'Bedroom', 'Bathroom', 'Floor', 'Balcony']
        for keyword in spec_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for elem in elements[:5]:
                parent = elem.parent
                if parent and (parent.get('class') or parent.get('id')):
                    selectors['specification_selectors'].append({
                        'keyword': keyword,
                        'tag': parent.name,
                        'classes': parent.get('class'),
                        'id': parent.get('id'),
                        'text_sample': str(elem)[:50]
                    })
        
        # Image selectors
        images = soup.find_all('img', src=True)
        for img in images[:10]:
            if 'property' in img.get('src', '').lower() or 'image' in img.get('src', '').lower():
                selectors['image_selectors'].append({
                    'tag': img.name,
                    'classes': img.get('class'),
                    'id': img.get('id'),
                    'src': img.get('src')[:100]
                })
        
        return selectors
    
    def analyze_javascript_elements(self):
        """Analyze JavaScript-loaded elements"""
        js_elements = {}
        
        try:
            # Check for dynamically loaded content
            dynamic_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-*], [ng-*], [v-*], [react-*]")
            js_elements['dynamic_elements_count'] = len(dynamic_elements)
            
            # Check for AJAX calls or API endpoints
            scripts = self.driver.find_elements(By.TAG_NAME, "script")
            js_elements['script_count'] = len(scripts)
            
            # Check for lazy-loaded images
            lazy_images = self.driver.find_elements(By.CSS_SELECTOR, "img[data-src], img[loading='lazy']")
            js_elements['lazy_images_count'] = len(lazy_images)
            
        except Exception as e:
            js_elements['error'] = str(e)
        
        return js_elements
    
    def analyze_image_gallery(self, soup):
        """Analyze image gallery structure"""
        gallery = {
            'total_images': 0,
            'gallery_containers': [],
            'image_sources': []
        }
        
        # Find all images
        images = soup.find_all('img', src=True)
        gallery['total_images'] = len(images)
        
        # Find gallery containers
        gallery_containers = soup.find_all(['div', 'section'], class_=re.compile(r'(gallery|image|photo)', re.I))
        for container in gallery_containers[:5]:
            gallery['gallery_containers'].append({
                'tag': container.name,
                'classes': container.get('class'),
                'id': container.get('id'),
                'image_count': len(container.find_all('img'))
            })
        
        # Sample image sources
        for img in images[:10]:
            src = img.get('src')
            if src and ('property' in src.lower() or 'image' in src.lower()):
                gallery['image_sources'].append(src)
        
        return gallery
    
    def extract_contact_information(self, soup):
        """Extract contact information structure"""
        contact = {
            'contact_sections': [],
            'phone_elements': [],
            'email_elements': []
        }
        
        # Find contact sections
        contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'(contact|agent|broker)', re.I))
        for section in contact_sections[:3]:
            contact['contact_sections'].append({
                'tag': section.name,
                'classes': section.get('class'),
                'id': section.get('id'),
                'text_sample': section.get_text()[:100]
            })
        
        return contact
    
    def extract_amenities(self, soup):
        """Extract amenities structure"""
        amenities = {
            'amenity_sections': [],
            'amenity_lists': [],
            'common_amenities': []
        }
        
        # Find amenity sections
        amenity_sections = soup.find_all(['div', 'section'], class_=re.compile(r'(amenity|amenities|facility|facilities)', re.I))
        for section in amenity_sections[:3]:
            amenities['amenity_sections'].append({
                'tag': section.name,
                'classes': section.get('class'),
                'id': section.get('id'),
                'text_sample': section.get_text()[:100]
            })
        
        # Find amenity lists
        amenity_lists = soup.find_all(['ul', 'ol'], class_=re.compile(r'(amenity|facility)', re.I))
        for alist in amenity_lists[:3]:
            amenities['amenity_lists'].append({
                'tag': alist.name,
                'classes': alist.get('class'),
                'item_count': len(alist.find_all('li'))
            })
        
        return amenities
    
    def extract_location_data(self, soup):
        """Extract location and map data"""
        location = {
            'location_sections': [],
            'map_elements': [],
            'address_elements': []
        }
        
        # Find location sections
        location_sections = soup.find_all(['div', 'section'], class_=re.compile(r'(location|address|map)', re.I))
        for section in location_sections[:3]:
            location['location_sections'].append({
                'tag': section.name,
                'classes': section.get('class'),
                'id': section.get('id'),
                'text_sample': section.get_text()[:100]
            })
        
        # Find map elements
        map_elements = soup.find_all(['div', 'iframe'], class_=re.compile(r'(map|google)', re.I))
        for elem in map_elements[:3]:
            location['map_elements'].append({
                'tag': elem.name,
                'classes': elem.get('class'),
                'id': elem.get('id')
            })
        
        return location
    
    def update_common_patterns(self, analysis):
        """Update common patterns across properties"""
        # Update selector patterns
        for selector_type, selectors in analysis['selector_mapping'].items():
            for selector in selectors:
                if selector.get('classes'):
                    self.common_selectors[selector_type].update(selector['classes'])
        
        # Update field patterns
        for field_name, field_value in analysis['data_extraction'].items():
            self.field_patterns[field_name].append(field_value)

def main():
    """Main analysis function"""
    print("🔍 99acres Automated Page Structure Analyzer")
    print("=" * 60)
    
    # Load sample URLs
    try:
        with open('manual_analysis_sample_fixed.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        sample_properties = data['sample_urls']
    except Exception as e:
        print(f"❌ Error loading sample URLs: {str(e)}")
        return
    
    analyzer = AutomatedPageStructureAnalyzer()
    
    try:
        # Setup browser
        analyzer.setup_driver()
        
        # Analyze first 10 properties for detailed structure analysis
        print(f"\n🚀 Starting Automated Analysis of {min(10, len(sample_properties))} Properties")
        print("=" * 60)
        
        for i, prop in enumerate(sample_properties[:10], 1):
            print(f"\n📍 Property {i}/10")
            analysis = analyzer.analyze_property_page_structure(prop)
            
            if analysis:
                time.sleep(3)  # Respectful delay
        
        # Generate comprehensive report
        print(f"\n📊 Generating Comprehensive Structure Analysis Report...")
        
        report = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_properties_analyzed': len(analyzer.analysis_results),
                'analyzer_version': 'Automated Structure 1.0'
            },
            'common_selector_patterns': {k: list(v) for k, v in analyzer.common_selectors.items()},
            'field_frequency': {k: len(v) for k, v in analyzer.field_patterns.items()},
            'detailed_analysis': analyzer.analysis_results
        }
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'automated_structure_analysis_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🎉 AUTOMATED STRUCTURE ANALYSIS COMPLETE!")
        print("="*60)
        print(f"📊 Properties Analyzed: {report['analysis_metadata']['total_properties_analyzed']}")
        print(f"🔍 Common Selector Types: {len(report['common_selector_patterns'])}")
        print(f"📄 Report saved: {filename}")
        
        # Print key findings
        print(f"\n🏆 KEY FINDINGS:")
        for selector_type, selectors in report['common_selector_patterns'].items():
            if selectors:
                print(f"   {selector_type}: {len(selectors)} unique selectors")
        
        print(f"\n📋 Ready to build comprehensive individual listing scraper!")
        
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
    finally:
        if analyzer.driver:
            analyzer.driver.quit()

if __name__ == "__main__":
    main()
