usage: cli_scraper.py [-h] [--city CITY]
                      [--mode {incremental,full,conservative,date_range,custom}]
                      [--max-pages MAX_PAGES] [--include-individual-pages]
                      [--force-full] [--headless] [--no-headless]
                      [--export-json] [--export-excel]
                      [--output-dir OUTPUT_DIR]
cli_scraper.py: error: argument --mode: invalid choice: 'quick' (choose from 'incremental', 'full', 'conservative', 'date_range', 'custom')
