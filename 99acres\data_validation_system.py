#!/usr/bin/env python3
"""
Comprehensive Data Validation and Cleaning System
Based on deep research findings across 6 cities and 10+ property types
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

class DataValidationSystem:
    """Comprehensive data validation based on deep research findings"""
    
    def __init__(self):
        self.validation_rules = self._initialize_validation_rules()
        self.cleaning_patterns = self._initialize_cleaning_patterns()
        self.validation_stats = {
            'total_validated': 0,
            'total_cleaned': 0,
            'validation_errors': [],
            'cleaning_actions': []
        }
    
    def _initialize_validation_rules(self) -> Dict:
        """Initialize validation rules based on deep research findings"""
        return {
            'price': {
                'patterns': [
                    r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)',
                    r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*-\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)',
                    r'Starting from ₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)',
                    r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)\s*onwards'
                ],
                'min_value': 1000,  # Minimum ₹1000
                'max_value': 1000000000,  # Maximum ₹100 Cr
                'valid_units': ['Cr', 'Lakh', 'Lac']
            },
            'area': {
                'patterns': [
                    r'\d+(?:,\d+)*\s*sqft',
                    r'\d+(?:,\d+)*\s*sq\.ft',
                    r'\d+(?:,\d+)*\s*-\s*\d+(?:,\d+)*\s*sqft'
                ],
                'min_value': 100,  # Minimum 100 sqft
                'max_value': 50000,  # Maximum 50,000 sqft
                'valid_units': ['sqft', 'sq.ft', 'sq ft']
            },
            'bedrooms': {
                'patterns': [r'\d+\s*BHK', r'\d+\s*Bedroom'],
                'min_value': 1,
                'max_value': 10,
                'valid_formats': ['1 BHK', '2 BHK', '3 BHK', '4 BHK', '5 BHK']
            },
            'location': {
                'required_components': ['locality'],
                'valid_cities': [
                    'Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Hyderabad', 'Chennai',
                    'Ahmedabad', 'Kolkata', 'Gurgaon', 'Noida', 'Thane', 'Navi Mumbai'
                ],
                'pincode_pattern': r'\d{6}'
            },
            'property_type': {
                'valid_types': [
                    'Apartment', 'Flat', 'Villa', 'Independent House', 'Builder Floor',
                    'Studio', 'Penthouse', 'Plot', 'Duplex', 'Triplex'
                ]
            },
            'transaction_type': {
                'valid_types': ['Resale', 'New Booking', 'Ready to Move', 'Under Construction']
            },
            'listing_type': {
                'valid_types': ['Individual', 'Project']
            }
        }
    
    def _initialize_cleaning_patterns(self) -> Dict:
        """Initialize data cleaning patterns based on research findings"""
        return {
            'price_normalization': [
                (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lac(?!\s*-)', r'₹\1 Lakh'),
                (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*lac(?!\s*-)', r'₹\1 Lakh'),
                (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*crore(?!\s*-)', r'₹\1 Cr'),
                (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*CRORE(?!\s*-)', r'₹\1 Cr')
            ],
            'area_normalization': [
                (r'(\d+(?:,\d+)*)\s*sq\.ft', r'\1 sqft'),
                (r'(\d+(?:,\d+)*)\s*sq\s*ft', r'\1 sqft'),
                (r'(\d+(?:,\d+)*)\s*SQFT', r'\1 sqft')
            ],
            'text_cleaning': [
                (r'\s+', ' '),  # Multiple spaces to single space
                (r'^\s+|\s+$', ''),  # Trim whitespace
                (r'[^\w\s₹.,()-]', ''),  # Remove special characters except common ones
            ],
            'property_type_normalization': [
                (r'(?i)apartment', 'Apartment'),
                (r'(?i)flat', 'Flat'),
                (r'(?i)villa', 'Villa'),
                (r'(?i)independent\s*house', 'Independent House'),
                (r'(?i)builder\s*floor', 'Builder Floor')
            ]
        }
    
    def validate_property_data(self, property_data: Dict) -> Tuple[bool, List[str], Dict]:
        """Validate complete property data"""
        is_valid = True
        errors = []
        cleaned_data = property_data.copy()
        
        # Validate and clean each field
        for field_name, field_value in property_data.items():
            if field_value and str(field_value).strip():
                field_valid, field_errors, cleaned_value = self._validate_field(
                    field_name, field_value
                )
                
                if not field_valid:
                    is_valid = False
                    errors.extend([f"{field_name}: {error}" for error in field_errors])
                
                if cleaned_value != field_value:
                    cleaned_data[field_name] = cleaned_value
                    self.validation_stats['cleaning_actions'].append({
                        'field': field_name,
                        'original': field_value,
                        'cleaned': cleaned_value
                    })
        
        # Cross-field validation
        cross_validation_errors = self._cross_field_validation(cleaned_data)
        if cross_validation_errors:
            is_valid = False
            errors.extend(cross_validation_errors)
        
        # Calculate data quality score
        quality_score = self._calculate_quality_score(cleaned_data)
        cleaned_data['data_quality_score'] = quality_score
        cleaned_data['validation_status'] = 'valid' if is_valid else 'invalid'
        
        self.validation_stats['total_validated'] += 1
        if not is_valid:
            self.validation_stats['validation_errors'].extend(errors)
        
        return is_valid, errors, cleaned_data
    
    def _validate_field(self, field_name: str, field_value: Any) -> Tuple[bool, List[str], Any]:
        """Validate individual field based on research findings"""
        errors = []
        cleaned_value = self._clean_field_value(field_name, field_value)
        
        if field_name == 'price':
            return self._validate_price(cleaned_value)
        elif field_name == 'area':
            return self._validate_area(cleaned_value)
        elif field_name == 'bedrooms':
            return self._validate_bedrooms(cleaned_value)
        elif field_name in ['locality', 'city']:
            return self._validate_location(cleaned_value)
        elif field_name == 'property_type':
            return self._validate_property_type(cleaned_value)
        elif field_name == 'transaction_type':
            return self._validate_transaction_type(cleaned_value)
        elif field_name == 'listing_type':
            return self._validate_listing_type(cleaned_value)
        elif field_name == 'pincode':
            return self._validate_pincode(cleaned_value)
        else:
            # Generic validation for other fields
            return True, [], cleaned_value
    
    def _validate_price(self, price_value: str) -> Tuple[bool, List[str], str]:
        """Validate price based on deep research patterns"""
        errors = []
        
        if not price_value:
            return False, ['Price is required'], price_value
        
        # Check if price matches any valid pattern
        valid_pattern = False
        for pattern in self.validation_rules['price']['patterns']:
            if re.search(pattern, price_value, re.IGNORECASE):
                valid_pattern = True
                break
        
        if not valid_pattern:
            errors.append(f'Invalid price format: {price_value}')
        
        # Extract numeric value for range validation
        numeric_match = re.search(r'(\d+(?:,\d+)*(?:\.\d+)?)', price_value)
        if numeric_match:
            try:
                numeric_value = float(numeric_match.group(1).replace(',', ''))
                unit = 'Lakh'
                if 'Cr' in price_value:
                    numeric_value *= 10000000  # Convert Cr to actual value
                    unit = 'Cr'
                elif 'Lakh' in price_value or 'Lac' in price_value:
                    numeric_value *= 100000  # Convert Lakh to actual value
                
                if numeric_value < self.validation_rules['price']['min_value']:
                    errors.append(f'Price too low: {price_value}')
                elif numeric_value > self.validation_rules['price']['max_value']:
                    errors.append(f'Price too high: {price_value}')
                    
            except ValueError:
                errors.append(f'Cannot parse price value: {price_value}')
        
        return len(errors) == 0, errors, price_value
    
    def _validate_area(self, area_value: str) -> Tuple[bool, List[str], str]:
        """Validate area based on research patterns"""
        errors = []
        
        if not area_value:
            return False, ['Area is required'], area_value
        
        # Extract numeric value
        numeric_match = re.search(r'(\d+(?:,\d+)*)', str(area_value))
        if numeric_match:
            try:
                numeric_value = int(numeric_match.group(1).replace(',', ''))
                
                if numeric_value < self.validation_rules['area']['min_value']:
                    errors.append(f'Area too small: {area_value}')
                elif numeric_value > self.validation_rules['area']['max_value']:
                    errors.append(f'Area too large: {area_value}')
                    
            except ValueError:
                errors.append(f'Cannot parse area value: {area_value}')
        else:
            errors.append(f'Invalid area format: {area_value}')
        
        return len(errors) == 0, errors, area_value
    
    def _validate_bedrooms(self, bedrooms_value: str) -> Tuple[bool, List[str], str]:
        """Validate bedrooms configuration"""
        errors = []
        
        if not bedrooms_value:
            return True, [], bedrooms_value  # Optional field
        
        # Extract numeric value
        numeric_match = re.search(r'(\d+)', str(bedrooms_value))
        if numeric_match:
            try:
                numeric_value = int(numeric_match.group(1))
                
                if numeric_value < self.validation_rules['bedrooms']['min_value']:
                    errors.append(f'Invalid bedroom count: {bedrooms_value}')
                elif numeric_value > self.validation_rules['bedrooms']['max_value']:
                    errors.append(f'Too many bedrooms: {bedrooms_value}')
                    
            except ValueError:
                errors.append(f'Cannot parse bedroom value: {bedrooms_value}')
        
        return len(errors) == 0, errors, bedrooms_value
    
    def _validate_location(self, location_value: str) -> Tuple[bool, List[str], str]:
        """Validate location information"""
        errors = []
        
        if not location_value or len(str(location_value).strip()) < 2:
            errors.append('Location too short or empty')
        
        return len(errors) == 0, errors, location_value
    
    def _validate_property_type(self, property_type: str) -> Tuple[bool, List[str], str]:
        """Validate property type against research findings"""
        errors = []
        
        if property_type and property_type not in self.validation_rules['property_type']['valid_types']:
            # Check for partial matches
            normalized_type = self._normalize_property_type(property_type)
            if normalized_type not in self.validation_rules['property_type']['valid_types']:
                errors.append(f'Unknown property type: {property_type}')
            else:
                property_type = normalized_type
        
        return len(errors) == 0, errors, property_type
    
    def _validate_transaction_type(self, transaction_type: str) -> Tuple[bool, List[str], str]:
        """Validate transaction type"""
        errors = []
        
        if transaction_type and transaction_type not in self.validation_rules['transaction_type']['valid_types']:
            errors.append(f'Unknown transaction type: {transaction_type}')
        
        return len(errors) == 0, errors, transaction_type
    
    def _validate_listing_type(self, listing_type: str) -> Tuple[bool, List[str], str]:
        """Validate listing type"""
        errors = []
        
        if listing_type and listing_type not in self.validation_rules['listing_type']['valid_types']:
            errors.append(f'Unknown listing type: {listing_type}')
        
        return len(errors) == 0, errors, listing_type
    
    def _validate_pincode(self, pincode: str) -> Tuple[bool, List[str], str]:
        """Validate pincode format"""
        errors = []
        
        if pincode and not re.match(self.validation_rules['location']['pincode_pattern'], str(pincode)):
            errors.append(f'Invalid pincode format: {pincode}')
        
        return len(errors) == 0, errors, pincode
    
    def _cross_field_validation(self, property_data: Dict) -> List[str]:
        """Perform cross-field validation"""
        errors = []
        
        # Price vs Area validation
        if property_data.get('price') and property_data.get('area'):
            # Add logic for price per sqft validation if needed
            pass
        
        # Location consistency validation
        if property_data.get('city') and property_data.get('locality'):
            # Add city-locality consistency checks if needed
            pass
        
        return errors
    
    def _clean_field_value(self, field_name: str, field_value: Any) -> str:
        """Clean field value based on research patterns"""
        if not field_value:
            return field_value
        
        cleaned_value = str(field_value)
        
        # Apply general text cleaning
        for pattern, replacement in self.cleaning_patterns['text_cleaning']:
            cleaned_value = re.sub(pattern, replacement, cleaned_value)
        
        # Apply field-specific cleaning
        if field_name == 'price':
            for pattern, replacement in self.cleaning_patterns['price_normalization']:
                cleaned_value = re.sub(pattern, replacement, cleaned_value)
        elif field_name == 'area':
            for pattern, replacement in self.cleaning_patterns['area_normalization']:
                cleaned_value = re.sub(pattern, replacement, cleaned_value)
        elif field_name == 'property_type':
            for pattern, replacement in self.cleaning_patterns['property_type_normalization']:
                cleaned_value = re.sub(pattern, replacement, cleaned_value)
        
        return cleaned_value
    
    def _normalize_property_type(self, property_type: str) -> str:
        """Normalize property type variations"""
        property_type_lower = property_type.lower()
        
        if 'apartment' in property_type_lower:
            return 'Apartment'
        elif 'flat' in property_type_lower:
            return 'Flat'
        elif 'villa' in property_type_lower:
            return 'Villa'
        elif 'independent' in property_type_lower and 'house' in property_type_lower:
            return 'Independent House'
        elif 'builder' in property_type_lower and 'floor' in property_type_lower:
            return 'Builder Floor'
        elif 'studio' in property_type_lower:
            return 'Studio'
        elif 'penthouse' in property_type_lower:
            return 'Penthouse'
        
        return property_type
    
    def _calculate_quality_score(self, property_data: Dict) -> int:
        """Calculate data quality score based on field completeness and validity"""
        # High priority fields (weight: 3)
        high_priority = ['title', 'price', 'area', 'bedrooms', 'locality', 'property_url']
        # Medium priority fields (weight: 2)
        medium_priority = ['bathrooms', 'property_type', 'city', 'transaction_type', 'construction_status']
        # Low priority fields (weight: 1)
        low_priority = ['amenities', 'rera_id', 'facing', 'parking', 'balconies']
        
        total_weight = len(high_priority) * 3 + len(medium_priority) * 2 + len(low_priority) * 1
        achieved_weight = 0
        
        for field in high_priority:
            if property_data.get(field) and str(property_data[field]).strip():
                achieved_weight += 3
        
        for field in medium_priority:
            if property_data.get(field) and str(property_data[field]).strip():
                achieved_weight += 2
        
        for field in low_priority:
            if property_data.get(field) and str(property_data[field]).strip():
                achieved_weight += 1
        
        return int((achieved_weight / total_weight) * 100)
    
    def get_validation_stats(self) -> Dict:
        """Get validation statistics"""
        return self.validation_stats.copy()
    
    def reset_stats(self):
        """Reset validation statistics"""
        self.validation_stats = {
            'total_validated': 0,
            'total_cleaned': 0,
            'validation_errors': [],
            'cleaning_actions': []
        }
