#!/usr/bin/env python3
"""
Analyze Database Variety
Check the variety of properties in the database across cities, types, price ranges
"""

import sqlite3
import json
from collections import defaultdict
from datetime import datetime

def analyze_database_variety():
    """Analyze the variety of properties in the database"""
    
    db_path = 'data/99acres_properties.db'
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Analyzing Database Variety")
        print("=" * 50)
        
        # 1. City Distribution
        cursor.execute("SELECT city, COUNT(*) FROM properties WHERE city IS NOT NULL GROUP BY city ORDER BY COUNT(*) DESC")
        cities = cursor.fetchall()
        print(f"\n📍 City Distribution ({len(cities)} cities):")
        for city, count in cities:
            print(f"   {city}: {count} properties")
        
        # 2. Property Type Distribution
        cursor.execute("SELECT property_type, COUNT(*) FROM properties WHERE property_type IS NOT NULL GROUP BY property_type ORDER BY COUNT(*) DESC")
        prop_types = cursor.fetchall()
        print(f"\n🏠 Property Type Distribution ({len(prop_types)} types):")
        for prop_type, count in prop_types:
            print(f"   {prop_type}: {count} properties")
        
        # 3. Bedroom Configuration
        cursor.execute("SELECT bedrooms, COUNT(*) FROM properties WHERE bedrooms IS NOT NULL GROUP BY bedrooms ORDER BY COUNT(*) DESC")
        bedrooms = cursor.fetchall()
        print(f"\n🛏️ Bedroom Configuration ({len(bedrooms)} configs):")
        for bedroom, count in bedrooms:
            print(f"   {bedroom} BHK: {count} properties")
        
        # 4. Price Range Analysis
        cursor.execute("SELECT price, COUNT(*) FROM properties WHERE price IS NOT NULL AND price != '' GROUP BY price ORDER BY COUNT(*) DESC LIMIT 10")
        prices = cursor.fetchall()
        print(f"\n💰 Top 10 Price Ranges:")
        for price, count in prices:
            print(f"   {price}: {count} properties")
        
        # 5. Locality Distribution
        cursor.execute("SELECT locality, COUNT(*) FROM properties WHERE locality IS NOT NULL GROUP BY locality ORDER BY COUNT(*) DESC LIMIT 15")
        localities = cursor.fetchall()
        print(f"\n📍 Top 15 Localities:")
        for locality, count in localities:
            print(f"   {locality}: {count} properties")
        
        # 6. Get diverse sample for analysis
        print(f"\n🎯 Creating Diverse Sample for Analysis:")
        
        # Get properties from different cities
        diverse_sample = []
        
        # Mumbai properties (different price ranges)
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE city LIKE '%Mumbai%' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 8
        """)
        mumbai_props = cursor.fetchall()
        diverse_sample.extend(mumbai_props)
        print(f"   Mumbai: {len(mumbai_props)} properties")
        
        # Delhi properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE city LIKE '%Delhi%' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        delhi_props = cursor.fetchall()
        diverse_sample.extend(delhi_props)
        print(f"   Delhi: {len(delhi_props)} properties")
        
        # Bangalore properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE city LIKE '%Bangalore%' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 4
        """)
        bangalore_props = cursor.fetchall()
        diverse_sample.extend(bangalore_props)
        print(f"   Bangalore: {len(bangalore_props)} properties")
        
        # Pune properties
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE city LIKE '%Pune%' AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 3
        """)
        pune_props = cursor.fetchall()
        diverse_sample.extend(pune_props)
        print(f"   Pune: {len(pune_props)} properties")
        
        # Other cities
        cursor.execute("""
            SELECT property_url, title, price, locality, bedrooms, city 
            FROM properties 
            WHERE city NOT LIKE '%Mumbai%' AND city NOT LIKE '%Delhi%' 
            AND city NOT LIKE '%Bangalore%' AND city NOT LIKE '%Pune%'
            AND property_url IS NOT NULL 
            ORDER BY RANDOM() LIMIT 5
        """)
        other_props = cursor.fetchall()
        diverse_sample.extend(other_props)
        print(f"   Other cities: {len(other_props)} properties")
        
        # Format diverse sample
        diverse_urls = []
        for i, (url, title, price, locality, bedrooms, city) in enumerate(diverse_sample, 1):
            diverse_urls.append({
                'index': i,
                'url': url,
                'title': title,
                'price': price,
                'locality': locality,
                'bedrooms': bedrooms,
                'city': city
            })

        # If we don't have enough diversity, get more from available data
        if len(diverse_urls) < 20:
            print(f"   📋 Getting additional diverse properties from available data...")
            cursor.execute("""
                SELECT property_url, title, price, locality, bedrooms, city
                FROM properties
                WHERE property_url IS NOT NULL
                ORDER BY RANDOM() LIMIT 20
            """)
            additional_props = cursor.fetchall()

            for url, title, price, locality, bedrooms, city in additional_props:
                if url not in [p['url'] for p in diverse_urls]:  # Avoid duplicates
                    diverse_urls.append({
                        'index': len(diverse_urls) + 1,
                        'url': url,
                        'title': title,
                        'price': price,
                        'locality': locality,
                        'bedrooms': bedrooms,
                        'city': city
                    })

        # Save diverse sample
        diverse_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_diverse_properties': len(diverse_urls),
            'city_distribution': dict(cities),
            'property_type_distribution': dict(prop_types),
            'bedroom_distribution': dict(bedrooms),
            'diverse_sample': diverse_urls
        }
        
        with open('diverse_property_sample.json', 'w', encoding='utf-8') as f:
            json.dump(diverse_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Created diverse sample of {len(diverse_urls)} properties")
        print(f"💾 Saved to: diverse_property_sample.json")
        
        # Print sample
        print(f"\n📋 Sample Properties for Analysis:")
        for prop in diverse_urls[:10]:
            print(f"   {prop['index']}. {prop['title'][:40]}...")
            print(f"      City: {prop['city']}, Price: {prop['price']}, BHK: {prop['bedrooms']}")
            print(f"      URL: {prop['url']}")
            print()
        
        conn.close()
        return diverse_urls
        
    except Exception as e:
        print(f"❌ Error analyzing database: {str(e)}")
        return []

if __name__ == "__main__":
    diverse_sample = analyze_database_variety()
    
    if diverse_sample:
        print(f"\n🎉 Successfully created diverse sample of {len(diverse_sample)} properties")
        print("📄 Ready for comprehensive field analysis across multiple cities and property types!")
    else:
        print("❌ Failed to create diverse sample")
