#!/usr/bin/env python3
"""
Check the enhanced database with all 50+ fields
"""

import sqlite3
import pandas as pd

def check_enhanced_database():
    """Check what data is in the enhanced database"""
    conn = sqlite3.connect('data/99acres_properties.db')
    
    # Get total count
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_count = cursor.fetchone()[0]
    print(f"📊 Total properties in database: {total_count}")
    
    # Get sample data with enhanced fields
    df = pd.read_sql_query("""
        SELECT title, price, area, bedrooms, bathrooms, balconies,
               locality, society, city, property_type, construction_status,
               rera_id, amenities, data_quality_score, property_url
        FROM properties 
        ORDER BY data_quality_score DESC, id DESC 
        LIMIT 5
    """, conn)
    
    print("\n📋 Top Quality Properties:")
    for i, row in df.iterrows():
        print(f"\n{i+1}. {row['title']}")
        print(f"   Price: {row['price'] or 'N/A'}")
        print(f"   Area: {row['area'] or 'N/A'} sqft")
        print(f"   Config: {row['bedrooms'] or 'N/A'} BHK, {row['bathrooms'] or 'N/A'} Bath, {row['balconies'] or 'N/A'} Balcony")
        print(f"   Location: {row['society'] or 'N/A'}, {row['locality'] or 'N/A'}")
        print(f"   Type: {row['property_type'] or 'N/A'}")
        print(f"   Status: {row['construction_status'] or 'N/A'}")
        print(f"   RERA: {row['rera_id'] or 'N/A'}")
        print(f"   Amenities: {(row['amenities'] or 'N/A')[:50]}...")
        print(f"   Quality Score: {row['data_quality_score'] or 0}%")
    
    # Check data completeness for all fields
    print(f"\n📈 Data Completeness Analysis:")
    
    # High priority fields
    high_priority = ['title', 'price', 'area', 'bedrooms', 'locality', 'property_url']
    print(f"\n🔥 High Priority Fields:")
    for field in high_priority:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Medium priority fields
    medium_priority = ['bathrooms', 'property_type', 'city', 'construction_status', 'society']
    print(f"\n🔶 Medium Priority Fields:")
    for field in medium_priority:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Enhanced fields from research
    enhanced_fields = ['balconies', 'facing', 'rera_id', 'amenities', 'data_quality_score']
    print(f"\n✨ Enhanced Fields:")
    for field in enhanced_fields:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != '' AND {field} != '0'")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Average data quality score
    cursor.execute("SELECT AVG(data_quality_score) FROM properties WHERE data_quality_score > 0")
    avg_quality = cursor.fetchone()[0]
    print(f"\n🎯 Average Data Quality Score: {avg_quality:.1f}%")
    
    # Quality distribution
    cursor.execute("""
        SELECT 
            CASE 
                WHEN data_quality_score >= 80 THEN 'Excellent (80-100%)'
                WHEN data_quality_score >= 60 THEN 'Good (60-79%)'
                WHEN data_quality_score >= 40 THEN 'Fair (40-59%)'
                ELSE 'Poor (0-39%)'
            END as quality_range,
            COUNT(*) as count
        FROM properties 
        GROUP BY quality_range
        ORDER BY MIN(data_quality_score) DESC
    """)
    
    quality_dist = cursor.fetchall()
    print(f"\n📊 Quality Distribution:")
    for quality_range, count in quality_dist:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {quality_range}: {count} properties ({percentage:.1f}%)")
    
    conn.close()

if __name__ == "__main__":
    check_enhanced_database()
