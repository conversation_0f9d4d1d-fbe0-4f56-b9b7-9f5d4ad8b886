#!/usr/bin/env python3
"""
99acres CSS Selector Mapping Tool
Identifies and maps exact CSS selectors for property data extraction.
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
from datetime import datetime
import re


class NinetyNineAcresSelectorMapper:
    """Tool for mapping CSS selectors for 99acres property data"""
    
    def __init__(self):
        self.driver = None
        self.selectors = {
            'property_cards': [],
            'title': [],
            'price': [],
            'area': [],
            'bhk': [],
            'location': [],
            'status': [],
            'agent': [],
            'contact': [],
            'images': [],
            'links': [],
            'rera': [],
            'amenities': []
        }
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def analyze_property_cards(self, url):
        """Analyze property cards and extract selector patterns"""
        print(f"🔍 Analyzing property cards on: {url}")
        
        self.driver.get(url)
        time.sleep(5)
        
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        
        # Find property cards by looking for price patterns
        price_elements = soup.find_all(text=re.compile(r'₹\d+'))
        property_cards = []
        
        for price_elem in price_elements:
            # Find the parent container that likely contains the full property card
            current = price_elem.parent
            for _ in range(10):  # Look up to 10 levels up
                if current is None:
                    break
                    
                # Check if this container has multiple property-related elements
                if self._is_property_card(current):
                    property_cards.append(current)
                    break
                current = current.parent
        
        # Remove duplicates
        unique_cards = []
        for card in property_cards:
            if card not in unique_cards:
                unique_cards.append(card)
        
        print(f"   ✅ Found {len(unique_cards)} unique property cards")
        
        # Analyze each card for data patterns
        for i, card in enumerate(unique_cards[:5]):  # Analyze first 5 cards
            print(f"\n   📋 Analyzing card {i+1}:")
            self._analyze_card_selectors(card, i)
            
        return unique_cards
    
    def _is_property_card(self, element):
        """Check if element is likely a property card container"""
        text = element.get_text().lower()
        
        # Must contain price
        if not re.search(r'₹\d+', text):
            return False
            
        # Must contain area information
        if 'sqft' not in text:
            return False
            
        # Must contain BHK information
        if 'bhk' not in text:
            return False
            
        return True
    
    def _analyze_card_selectors(self, card, card_index):
        """Analyze selectors within a property card"""
        
        # 1. Title selectors
        titles = card.find_all(['h1', 'h2', 'h3'])
        for title in titles:
            if 'bhk' in title.get_text().lower():
                selector = self._generate_selector(title)
                if selector not in self.selectors['title']:
                    self.selectors['title'].append(selector)
                    print(f"      📝 Title: {selector} -> {title.get_text()[:50]}...")
        
        # 2. Price selectors
        price_elements = card.find_all(text=re.compile(r'₹\d+'))
        for price_elem in price_elements:
            parent = price_elem.parent
            selector = self._generate_selector(parent)
            if selector not in self.selectors['price']:
                self.selectors['price'].append(selector)
                print(f"      💰 Price: {selector} -> {price_elem.strip()}")
        
        # 3. Area selectors
        area_elements = card.find_all(text=re.compile(r'\d+\s*sqft'))
        for area_elem in area_elements:
            parent = area_elem.parent
            selector = self._generate_selector(parent)
            if selector not in self.selectors['area']:
                self.selectors['area'].append(selector)
                print(f"      📐 Area: {selector} -> {area_elem.strip()}")
        
        # 4. BHK selectors
        bhk_elements = card.find_all(text=re.compile(r'\d+\s*bhk', re.IGNORECASE))
        for bhk_elem in bhk_elements:
            parent = bhk_elem.parent
            selector = self._generate_selector(parent)
            if selector not in self.selectors['bhk']:
                self.selectors['bhk'].append(selector)
                print(f"      🏠 BHK: {selector} -> {bhk_elem.strip()}")
        
        # 5. Contact buttons
        contact_buttons = card.find_all(['button', 'a'], text=re.compile(r'contact|view number', re.IGNORECASE))
        for button in contact_buttons:
            selector = self._generate_selector(button)
            if selector not in self.selectors['contact']:
                self.selectors['contact'].append(selector)
                print(f"      📞 Contact: {selector} -> {button.get_text()}")
        
        # 6. Property links
        links = card.find_all('a', href=True)
        for link in links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                selector = self._generate_selector(link)
                if selector not in self.selectors['links']:
                    self.selectors['links'].append(selector)
                    print(f"      🔗 Link: {selector} -> {href[:50]}...")
        
        # 7. Images
        images = card.find_all('img')
        for img in images:
            if img.get('src') or img.get('data-src'):
                selector = self._generate_selector(img)
                if selector not in self.selectors['images']:
                    self.selectors['images'].append(selector)
                    print(f"      🖼️ Image: {selector}")
    
    def _generate_selector(self, element):
        """Generate CSS selector for an element"""
        selectors = []
        
        # Add tag name
        tag = element.name
        selectors.append(tag)
        
        # Add class if available
        if element.get('class'):
            classes = element.get('class')
            if isinstance(classes, list):
                class_str = '.'.join(classes)
            else:
                class_str = classes
            selectors.append(f"{tag}.{class_str}")
        
        # Add id if available
        if element.get('id'):
            selectors.append(f"{tag}#{element.get('id')}")
        
        # Add attribute selectors for common attributes
        for attr in ['data-testid', 'role', 'type']:
            if element.get(attr):
                selectors.append(f"{tag}[{attr}='{element.get(attr)}']")
        
        return selectors[0] if selectors else tag
    
    def save_selectors(self):
        """Save identified selectors to JSON file"""
        output = {
            'timestamp': datetime.now().isoformat(),
            'selectors': self.selectors,
            'summary': {
                'total_selectors': sum(len(v) for v in self.selectors.values()),
                'categories': list(self.selectors.keys())
            }
        }
        
        with open('data/99acres_css_selectors.json', 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Selectors saved to: data/99acres_css_selectors.json")
        print(f"📊 Total selectors identified: {output['summary']['total_selectors']}")
    
    def run_analysis(self):
        """Run complete selector analysis"""
        print("🚀 Starting 99acres CSS selector analysis...")
        print("="*60)
        
        try:
            self.setup_driver()
            
            # Analyze Mumbai sale page
            mumbai_url = "https://www.99acres.com/property-for-sale-in-mumbai-ffid"
            self.analyze_property_cards(mumbai_url)
            
            # Save results
            self.save_selectors()
            
            print("\n✅ CSS selector analysis completed successfully!")
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()


if __name__ == "__main__":
    mapper = NinetyNineAcresSelectorMapper()
    mapper.run_analysis()
