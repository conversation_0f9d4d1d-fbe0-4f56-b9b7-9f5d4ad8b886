#!/usr/bin/env python3
"""
Real-Time Scraper Dashboard
Web-based monitoring interface for the optimized large-scale scraper
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request, send_from_directory
import sqlite3
import json
import threading
import time
from datetime import datetime, timedelta
import psutil
from optimized_large_scale_scraper import OptimizedLargeScaleScraper, ScrapingConfig

app = Flask(__name__)

class ScraperMonitor:
    """Real-time monitoring for scraper operations"""
    
    def __init__(self):
        self.scraper = None
        self.is_running = False
        self.current_stats = {
            'total_scraped': 0,
            'success_rate': 0,
            'avg_time_per_property': 0,
            'active_instances': 0,
            'memory_usage': 0,
            'cpu_usage': 0,
            'start_time': None,
            'estimated_completion': None
        }
        self.recent_properties = []
        self.error_log = []
        
    def start_scraping(self, config_data):
        """Start scraping with given configuration"""
        if self.is_running:
            return False, "<PERSON>rap<PERSON> is already running"
        
        try:
            # Create configuration
            config = ScrapingConfig(
                max_concurrent_instances=config_data.get('concurrent_instances', 4),
                max_properties_per_instance=config_data.get('properties_per_instance', 250),
                base_delay_range=(config_data.get('min_delay', 1.5), config_data.get('max_delay', 3.0)),
                headless=config_data.get('headless', True),
                enable_images=config_data.get('enable_images', False)
            )
            
            self.scraper = OptimizedLargeScaleScraper(config)
            
            # Start scraping in background thread
            scraping_thread = threading.Thread(
                target=self._run_scraping,
                args=(config_data.get('max_properties', 100),)
            )
            scraping_thread.daemon = True
            scraping_thread.start()
            
            self.is_running = True
            self.current_stats['start_time'] = datetime.now().isoformat()
            
            return True, "Scraping started successfully"
            
        except Exception as e:
            return False, f"Failed to start scraping: {str(e)}"
    
    def _run_scraping(self, max_properties):
        """Run scraping in background"""
        try:
            # Load URLs
            property_urls = self.scraper.load_properties_from_database(max_properties)
            
            if property_urls:
                # Start scraping
                results = self.scraper.scrape_properties_parallel(property_urls, max_properties)
                
                # Update final stats
                self.current_stats.update(self.scraper.performance_metrics)
                
        except Exception as e:
            self.error_log.append({
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            })
        finally:
            self.is_running = False
    
    def get_current_stats(self):
        """Get current scraping statistics"""
        # Update system stats
        self.current_stats['memory_usage'] = psutil.virtual_memory().percent
        self.current_stats['cpu_usage'] = psutil.cpu_percent()
        
        # Get database stats
        try:
            conn = sqlite3.connect('data/optimized_individual_properties.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM optimized_properties")
            total_count = cursor.fetchone()[0]
            self.current_stats['total_scraped'] = total_count
            
            # Get recent properties
            cursor.execute("""
                SELECT title, city, bhk_config, price_crores, extraction_time, scraped_timestamp
                FROM optimized_properties 
                ORDER BY scraped_timestamp DESC 
                LIMIT 10
            """)
            
            self.recent_properties = []
            for row in cursor.fetchall():
                self.recent_properties.append({
                    'title': row[0],
                    'city': row[1],
                    'bhk_config': row[2],
                    'price_crores': row[3],
                    'extraction_time': row[4],
                    'timestamp': row[5]
                })
            
            conn.close()
            
        except Exception as e:
            pass
        
        return self.current_stats
    
    def stop_scraping(self):
        """Stop current scraping operation"""
        self.is_running = False
        return True, "Scraping stopped"

# Global monitor instance
monitor = ScraperMonitor()

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/stats')
def get_stats():
    """API endpoint for current statistics"""
    stats = monitor.get_current_stats()
    return jsonify({
        'stats': stats,
        'recent_properties': monitor.recent_properties,
        'is_running': monitor.is_running,
        'error_count': len(monitor.error_log)
    })

@app.route('/api/start', methods=['POST'])
def start_scraping():
    """API endpoint to start scraping"""
    config_data = request.json
    success, message = monitor.start_scraping(config_data)
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_scraping():
    """API endpoint to stop scraping"""
    success, message = monitor.stop_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/config')
def get_config():
    """API endpoint for default configuration"""
    return jsonify({
        'concurrent_instances': 4,
        'properties_per_instance': 250,
        'max_properties': 100,
        'min_delay': 1.5,
        'max_delay': 3.0,
        'headless': True,
        'enable_images': False
    })

@app.route('/api/database')
def get_database_stats():
    """API endpoint for database statistics"""
    try:
        conn = sqlite3.connect('data/optimized_individual_properties.db')
        cursor = conn.cursor()
        
        # Total count
        cursor.execute("SELECT COUNT(*) FROM optimized_properties")
        total_count = cursor.fetchone()[0]
        
        # City distribution
        cursor.execute("SELECT city, COUNT(*) FROM optimized_properties GROUP BY city ORDER BY COUNT(*) DESC LIMIT 10")
        city_distribution = [{'city': row[0], 'count': row[1]} for row in cursor.fetchall()]
        
        # BHK distribution
        cursor.execute("SELECT bhk_config, COUNT(*) FROM optimized_properties GROUP BY bhk_config ORDER BY COUNT(*) DESC")
        bhk_distribution = [{'bhk': row[0], 'count': row[1]} for row in cursor.fetchall()]
        
        # Performance stats
        cursor.execute("SELECT AVG(extraction_time), MIN(extraction_time), MAX(extraction_time) FROM optimized_properties WHERE extraction_time IS NOT NULL")
        perf_stats = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'total_properties': total_count,
            'city_distribution': city_distribution,
            'bhk_distribution': bhk_distribution,
            'performance': {
                'avg_extraction_time': perf_stats[0] or 0,
                'min_extraction_time': perf_stats[1] or 0,
                'max_extraction_time': perf_stats[2] or 0
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)})

# Create templates directory and HTML template
def create_dashboard_template():
    """Create the dashboard HTML template"""
    os.makedirs('templates', exist_ok=True)
    
    html_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>99acres Scraper Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        .control-panel { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-primary { background: #667eea; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn:hover { opacity: 0.8; }
        .recent-properties { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .property-item { padding: 10px; border-bottom: 1px solid #eee; }
        .status-running { color: #27ae60; }
        .status-stopped { color: #e74c3c; }
        .config-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .config-item { display: flex; flex-direction: column; }
        .config-item label { margin-bottom: 5px; font-weight: bold; }
        .config-item input, .config-item select { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 99acres Optimized Scraper Dashboard</h1>
            <p>Real-time monitoring and control for large-scale property scraping</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-scraped">0</div>
                <div class="stat-label">Total Properties Scraped</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="success-rate">0%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avg-time">0s</div>
                <div class="stat-label">Avg Time per Property</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="status">Stopped</div>
                <div class="stat-label">Status</div>
            </div>
        </div>
        
        <div class="control-panel">
            <h3>Scraper Control</h3>
            <div class="config-grid">
                <div class="config-item">
                    <label>Max Properties:</label>
                    <input type="number" id="max-properties" value="100" min="1" max="10000">
                </div>
                <div class="config-item">
                    <label>Concurrent Instances:</label>
                    <input type="number" id="concurrent-instances" value="4" min="1" max="8">
                </div>
                <div class="config-item">
                    <label>Min Delay (s):</label>
                    <input type="number" id="min-delay" value="1.5" step="0.1" min="0.5">
                </div>
                <div class="config-item">
                    <label>Max Delay (s):</label>
                    <input type="number" id="max-delay" value="3.0" step="0.1" min="1.0">
                </div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="startScraping()">Start Scraping</button>
                <button class="btn btn-danger" onclick="stopScraping()">Stop Scraping</button>
            </div>
        </div>
        
        <div class="recent-properties">
            <h3>Recent Properties</h3>
            <div id="recent-list">Loading...</div>
        </div>
    </div>

    <script>
        let isRunning = false;
        
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-scraped').textContent = data.stats.total_scraped;
                    document.getElementById('success-rate').textContent = data.stats.success_rate.toFixed(1) + '%';
                    document.getElementById('avg-time').textContent = data.stats.avg_time_per_property.toFixed(1) + 's';
                    
                    const statusElement = document.getElementById('status');
                    if (data.is_running) {
                        statusElement.textContent = 'Running';
                        statusElement.className = 'stat-value status-running';
                    } else {
                        statusElement.textContent = 'Stopped';
                        statusElement.className = 'stat-value status-stopped';
                    }
                    
                    // Update recent properties
                    const recentList = document.getElementById('recent-list');
                    if (data.recent_properties.length > 0) {
                        recentList.innerHTML = data.recent_properties.map(prop => 
                            `<div class="property-item">
                                <strong>${prop.title || 'N/A'}</strong><br>
                                <small>${prop.city} | ${prop.bhk_config} | ₹${prop.price_crores}Cr | ${prop.extraction_time}s</small>
                            </div>`
                        ).join('');
                    } else {
                        recentList.innerHTML = '<div class="property-item">No properties scraped yet</div>';
                    }
                    
                    isRunning = data.is_running;
                })
                .catch(error => console.error('Error updating stats:', error));
        }
        
        function startScraping() {
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                concurrent_instances: parseInt(document.getElementById('concurrent-instances').value),
                min_delay: parseFloat(document.getElementById('min-delay').value),
                max_delay: parseFloat(document.getElementById('max-delay').value),
                headless: true,
                enable_images: false
            };
            
            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Scraping started successfully!');
                } else {
                    alert('Failed to start scraping: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping');
            });
        }
        
        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Scraping stopped successfully!');
                    } else {
                        alert('Failed to stop scraping: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error stopping scraping:', error);
                    alert('Error stopping scraping');
                });
        }
        
        // Update stats every 2 seconds
        setInterval(updateStats, 2000);
        updateStats(); // Initial load
    </script>
</body>
</html>'''
    
    with open('templates/dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_template)

def main():
    """Run the dashboard"""
    print("🖥️ Starting 99acres Scraper Dashboard")
    print("=" * 50)
    
    # Create dashboard template
    create_dashboard_template()
    
    print("✅ Dashboard template created")
    print("🌐 Starting web server...")
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("🔧 Use the dashboard to configure and monitor scraping operations")
    
    # Run Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == "__main__":
    main()
