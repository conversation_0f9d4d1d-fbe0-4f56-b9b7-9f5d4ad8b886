#!/usr/bin/env python3
"""
Large-Scale Testing Framework for Comprehensive Individual Listing Scraper
Tests scraper across 100+ properties covering all cities, property types, and transaction types
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
import json
import time
from datetime import datetime
from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper
from collections import defaultdict

class LargeScaleTestingFramework:
    def __init__(self):
        self.scraper = ComprehensiveIndividualListingScraper(headless=False)
        self.test_results = []
        self.error_log = []
        self.performance_metrics = {}
        self.field_coverage_analysis = defaultdict(int)
        
    def create_diverse_test_sample(self, target_count=100):
        """Create a diverse test sample from the database"""
        print("🎯 Creating Diverse Test Sample for Large-Scale Testing")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            # Get total available properties
            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NOT NULL")
            total_available = cursor.fetchone()[0]
            print(f"📊 Total available properties: {total_available}")
            
            test_sample = []
            
            # Strategy 1: Sample by cities (proportional distribution)
            cities = ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Hyderabad', 'Chennai', 'Kolkata', 'Ahmedabad']
            properties_per_city = max(10, target_count // len(cities))
            
            print(f"\n🏙️ Sampling {properties_per_city} properties per city:")
            
            for city in cities:
                # Sale properties
                cursor.execute("""
                    SELECT property_url, title, city, locality, bedrooms, price, property_type
                    FROM properties 
                    WHERE city LIKE ? AND property_url IS NOT NULL 
                    AND property_url NOT LIKE '%httpswww%'
                    ORDER BY RANDOM() LIMIT ?
                """, (f'%{city}%', properties_per_city // 2))
                
                city_properties = cursor.fetchall()
                
                for prop in city_properties:
                    url, title, city_name, locality, bedrooms, price, prop_type = prop
                    
                    # Fix URL format
                    if url.startswith('http'):
                        fixed_url = url
                    elif url.startswith('www.'):
                        fixed_url = f"https://{url}"
                    else:
                        fixed_url = f"https://www.99acres.com/{url}"
                    
                    test_sample.append({
                        'url': fixed_url,
                        'title': title,
                        'city': city_name,
                        'locality': locality,
                        'bedrooms': bedrooms,
                        'price': price,
                        'property_type': prop_type,
                        'test_category': f"{city}_{prop_type}_{bedrooms}BHK"
                    })
                
                print(f"   {city}: {len(city_properties)} properties")
            
            # Strategy 2: Sample by property configurations
            print(f"\n🛏️ Adding diverse BHK configurations:")
            
            for bhk in ['1', '2', '3', '4', '5', '6', '7']:
                cursor.execute("""
                    SELECT property_url, title, city, locality, bedrooms, price, property_type
                    FROM properties 
                    WHERE bedrooms = ? AND property_url IS NOT NULL 
                    AND property_url NOT LIKE '%httpswww%'
                    ORDER BY RANDOM() LIMIT 5
                """, (bhk,))
                
                bhk_properties = cursor.fetchall()
                
                for prop in bhk_properties:
                    url, title, city_name, locality, bedrooms, price, prop_type = prop
                    
                    # Fix URL format
                    if url.startswith('http'):
                        fixed_url = url
                    elif url.startswith('www.'):
                        fixed_url = f"https://{url}"
                    else:
                        fixed_url = f"https://www.99acres.com/{url}"
                    
                    # Avoid duplicates
                    if not any(existing['url'] == fixed_url for existing in test_sample):
                        test_sample.append({
                            'url': fixed_url,
                            'title': title,
                            'city': city_name,
                            'locality': locality,
                            'bedrooms': bedrooms,
                            'price': price,
                            'property_type': prop_type,
                            'test_category': f"BHK_{bhk}_{city_name}"
                        })
                
                print(f"   {bhk} BHK: {len(bhk_properties)} properties")
            
            # Strategy 3: Random sampling to reach target
            remaining_needed = max(0, target_count - len(test_sample))
            if remaining_needed > 0:
                print(f"\n🎲 Adding {remaining_needed} random properties to reach target:")
                
                cursor.execute("""
                    SELECT property_url, title, city, locality, bedrooms, price, property_type
                    FROM properties 
                    WHERE property_url IS NOT NULL 
                    AND property_url NOT LIKE '%httpswww%'
                    ORDER BY RANDOM() LIMIT ?
                """, (remaining_needed,))
                
                random_properties = cursor.fetchall()
                
                for prop in random_properties:
                    url, title, city_name, locality, bedrooms, price, prop_type = prop
                    
                    # Fix URL format
                    if url.startswith('http'):
                        fixed_url = url
                    elif url.startswith('www.'):
                        fixed_url = f"https://{url}"
                    else:
                        fixed_url = f"https://www.99acres.com/{url}"
                    
                    # Avoid duplicates
                    if not any(existing['url'] == fixed_url for existing in test_sample):
                        test_sample.append({
                            'url': fixed_url,
                            'title': title,
                            'city': city_name,
                            'locality': locality,
                            'bedrooms': bedrooms,
                            'price': price,
                            'property_type': prop_type,
                            'test_category': f"Random_{city_name}"
                        })
                
                print(f"   Random: {len(random_properties)} properties")
            
            # Remove duplicates and limit to target
            seen_urls = set()
            final_sample = []
            for prop in test_sample:
                if prop['url'] not in seen_urls and len(final_sample) < target_count:
                    seen_urls.add(prop['url'])
                    final_sample.append(prop)
            
            # Save test sample
            test_data = {
                'creation_timestamp': datetime.now().isoformat(),
                'target_count': target_count,
                'actual_count': len(final_sample),
                'test_sample': final_sample
            }
            
            with open('large_scale_test_sample.json', 'w', encoding='utf-8') as f:
                json.dump(test_data, f, indent=2, ensure_ascii=False)
            
            # Print sample summary
            print(f"\n✅ Created test sample of {len(final_sample)} properties")
            print(f"💾 Saved to: large_scale_test_sample.json")
            
            # Analyze sample diversity
            city_counts = defaultdict(int)
            bhk_counts = defaultdict(int)
            type_counts = defaultdict(int)
            
            for prop in final_sample:
                city_counts[prop['city']] += 1
                bhk_counts[prop['bedrooms']] += 1
                type_counts[prop['property_type']] += 1
            
            print(f"\n📊 Sample Diversity Analysis:")
            print(f"   Cities: {dict(city_counts)}")
            print(f"   BHK Configs: {dict(bhk_counts)}")
            print(f"   Property Types: {dict(type_counts)}")
            
            conn.close()
            return final_sample
            
        except Exception as e:
            print(f"❌ Error creating test sample: {str(e)}")
            return []
    
    def run_large_scale_test(self, test_sample, max_properties=100):
        """Run large-scale testing on the sample"""
        print(f"\n🚀 Starting Large-Scale Testing")
        print(f"🎯 Target: {min(max_properties, len(test_sample))} properties")
        print("=" * 60)
        
        try:
            # Setup scraper
            self.scraper.setup_driver()
            
            # Track performance
            start_time = datetime.now()
            successful_extractions = 0
            failed_extractions = 0
            
            # Test each property
            for i, prop in enumerate(test_sample[:max_properties], 1):
                print(f"\n📍 Testing Property {i}/{min(max_properties, len(test_sample))}")
                print(f"   Title: {prop['title'][:50]}...")
                print(f"   Category: {prop['test_category']}")
                print(f"   URL: {prop['url']}")
                
                # Record test start
                test_start = datetime.now()
                
                try:
                    # Extract data
                    property_data = self.scraper.extract_comprehensive_property_data(prop['url'])
                    
                    if property_data:
                        # Calculate extraction time
                        extraction_time = (datetime.now() - test_start).total_seconds()
                        
                        # Count extracted fields
                        extracted_fields = len([k for k, v in property_data.items() if v])
                        
                        # Save to database
                        if self.scraper.save_to_database(property_data):
                            successful_extractions += 1
                            
                            # Record success
                            test_result = {
                                'property_index': i,
                                'test_category': prop['test_category'],
                                'url': prop['url'],
                                'status': 'SUCCESS',
                                'extracted_fields': extracted_fields,
                                'extraction_time': extraction_time,
                                'timestamp': datetime.now().isoformat()
                            }
                            
                            self.test_results.append(test_result)
                            
                            # Update field coverage
                            for field_name, field_value in property_data.items():
                                if field_value:
                                    self.field_coverage_analysis[field_name] += 1
                            
                            print(f"   ✅ SUCCESS: {extracted_fields} fields in {extraction_time:.1f}s")
                        else:
                            print(f"   ⚠️ Database save failed")
                    else:
                        failed_extractions += 1
                        
                        # Record failure
                        test_result = {
                            'property_index': i,
                            'test_category': prop['test_category'],
                            'url': prop['url'],
                            'status': 'FAILED',
                            'extracted_fields': 0,
                            'extraction_time': 0,
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        self.test_results.append(test_result)
                        print(f"   ❌ FAILED: No data extracted")
                
                except Exception as e:
                    failed_extractions += 1
                    error_msg = str(e)
                    
                    # Log error
                    self.error_log.append({
                        'property_index': i,
                        'url': prop['url'],
                        'error': error_msg,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    print(f"   ❌ ERROR: {error_msg}")
                
                # Respectful delay
                time.sleep(3)
            
            # Calculate performance metrics
            total_time = (datetime.now() - start_time).total_seconds()
            success_rate = (successful_extractions / min(max_properties, len(test_sample))) * 100
            avg_time_per_property = total_time / min(max_properties, len(test_sample))
            
            self.performance_metrics = {
                'total_properties_tested': min(max_properties, len(test_sample)),
                'successful_extractions': successful_extractions,
                'failed_extractions': failed_extractions,
                'success_rate_percentage': success_rate,
                'total_test_time_seconds': total_time,
                'average_time_per_property': avg_time_per_property,
                'total_errors': len(self.error_log)
            }
            
            print(f"\n" + "=" * 60)
            print(f"🎉 LARGE-SCALE TESTING COMPLETE!")
            print(f"=" * 60)
            print(f"📊 Properties Tested: {self.performance_metrics['total_properties_tested']}")
            print(f"✅ Successful: {successful_extractions}")
            print(f"❌ Failed: {failed_extractions}")
            print(f"📈 Success Rate: {success_rate:.1f}%")
            print(f"⏱️ Total Time: {total_time/60:.1f} minutes")
            print(f"⚡ Avg Time/Property: {avg_time_per_property:.1f} seconds")
            print(f"🐛 Total Errors: {len(self.error_log)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Large-scale testing failed: {str(e)}")
            return False
        finally:
            if self.scraper.driver:
                self.scraper.driver.quit()
    
    def generate_comprehensive_test_report(self):
        """Generate comprehensive test report"""
        try:
            # Generate detailed report
            report = {
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'test_type': 'Large-Scale Individual Listing Scraper Test',
                    'scraper_version': 'Comprehensive Individual 1.0'
                },
                'performance_metrics': self.performance_metrics,
                'field_coverage_analysis': dict(self.field_coverage_analysis),
                'test_results': self.test_results,
                'error_log': self.error_log,
                'recommendations': self.generate_recommendations()
            }
            
            # Save report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'large_scale_test_report_{timestamp}.json'
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\n📄 Comprehensive test report saved: {filename}")
            return report
            
        except Exception as e:
            print(f"❌ Error generating test report: {str(e)}")
            return None
    
    def generate_recommendations(self):
        """Generate recommendations based on test results"""
        recommendations = []
        
        if self.performance_metrics.get('success_rate_percentage', 0) < 85:
            recommendations.append("SUCCESS_RATE: Success rate below 85%. Review error patterns and improve error handling.")
        
        if self.performance_metrics.get('average_time_per_property', 0) > 10:
            recommendations.append("PERFORMANCE: Average extraction time > 10s. Consider optimization.")
        
        if len(self.error_log) > self.performance_metrics.get('total_properties_tested', 0) * 0.1:
            recommendations.append("ERROR_RATE: High error rate detected. Review error patterns.")
        
        # Field coverage recommendations
        total_tests = self.performance_metrics.get('successful_extractions', 0)
        if total_tests > 0:
            low_coverage_fields = []
            for field, count in self.field_coverage_analysis.items():
                coverage = (count / total_tests) * 100
                if coverage < 50 and field not in ['property_id', 'latitude', 'longitude']:
                    low_coverage_fields.append(f"{field} ({coverage:.1f}%)")
            
            if low_coverage_fields:
                recommendations.append(f"FIELD_COVERAGE: Low coverage fields need improvement: {', '.join(low_coverage_fields[:5])}")
        
        if not recommendations:
            recommendations.append("EXCELLENT: All metrics within acceptable ranges. Scraper ready for production.")
        
        return recommendations

def main():
    """Main testing function"""
    print("🔍 99acres Large-Scale Individual Listing Scraper Testing")
    print("=" * 60)
    
    framework = LargeScaleTestingFramework()
    
    # Create diverse test sample
    test_sample = framework.create_diverse_test_sample(target_count=100)
    
    if not test_sample:
        print("❌ Failed to create test sample")
        return
    
    # Run large-scale test
    success = framework.run_large_scale_test(test_sample, max_properties=100)
    
    if success:
        # Generate comprehensive report
        report = framework.generate_comprehensive_test_report()
        
        if report:
            print(f"\n🎯 TESTING RECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"   • {rec}")
    else:
        print("❌ Large-scale testing failed")

if __name__ == "__main__":
    main()
