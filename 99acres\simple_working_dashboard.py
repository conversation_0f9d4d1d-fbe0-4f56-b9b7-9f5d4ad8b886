#!/usr/bin/env python3
"""
Simple Working Dashboard
Minimal dashboard that actually works with proper database connections
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
import threading
import time
import queue
from datetime import datetime, timedelta
import psutil
import logging
from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper

app = Flask(__name__)

class SimpleScraperMonitor:
    """Simple working monitor with fixed database connections"""
    
    def __init__(self):
        self.scraper = None
        self.scraper_thread = None
        self.is_running = False
        self.start_time = None
        
        # Simple progress tracking
        self.progress = {
            'total_urls': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'current_url': '',
            'current_title': ''
        }
        
        # Simple logs
        self.logs = []
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("SimpleMonitor")
    
    def add_log(self, message):
        """Add simple log entry"""
        log_entry = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'message': message
        }
        self.logs.append(log_entry)
        
        # Keep only last 20 logs
        if len(self.logs) > 20:
            self.logs.pop(0)
        
        print(f"[{log_entry['timestamp']}] {message}")
    
    def get_url_sources(self):
        """Get URL sources with working database connections"""
        try:
            # Main database
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM properties 
                WHERE property_url IS NOT NULL AND property_url != ''
                AND property_url NOT LIKE '%httpswww%'
            """)
            total_urls = cursor.fetchone()[0]
            conn.close()
            
            # Individual properties database
            extracted_count = 0
            try:
                conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT COUNT(*) FROM individual_properties")
                extracted_count = cursor2.fetchone()[0]
                conn2.close()
            except:
                extracted_count = 0
            
            return {
                'individual_listings': {
                    'name': 'Individual Property Listings',
                    'total': total_urls,
                    'extracted': extracted_count,
                    'remaining': total_urls - extracted_count,
                    'description': f'Detailed property pages ({total_urls} total, {extracted_count} extracted)'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sources: {str(e)}")
            return {}
    
    def load_urls_for_scraping(self, max_properties=10, skip_existing=True):
        """Load URLs for scraping with working logic"""
        try:
            self.add_log(f"Loading URLs (max: {max_properties}, skip_existing: {skip_existing})")
            
            # Get main URLs
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            cursor.execute("""
                SELECT property_url FROM properties 
                WHERE property_url IS NOT NULL AND property_url != ''
                AND property_url NOT LIKE '%httpswww%'
                ORDER BY RANDOM()
            """)
            all_urls = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if skip_existing:
                # Get extracted URLs
                try:
                    conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                    cursor2 = conn2.cursor()
                    cursor2.execute("SELECT property_url FROM individual_properties WHERE property_url IS NOT NULL")
                    extracted_urls = set(row[0] for row in cursor2.fetchall())
                    conn2.close()
                    
                    # Filter out extracted URLs
                    filtered_urls = [url for url in all_urls if url not in extracted_urls]
                    self.add_log(f"Filtered {len(all_urls)} -> {len(filtered_urls)} URLs (skipped {len(extracted_urls)} existing)")
                    urls = filtered_urls[:max_properties]
                except Exception as e:
                    self.add_log(f"Warning: Could not filter existing URLs: {str(e)}")
                    urls = all_urls[:max_properties]
            else:
                urls = all_urls[:max_properties]
            
            self.add_log(f"Selected {len(urls)} URLs for scraping")
            return urls
            
        except Exception as e:
            self.add_log(f"Error loading URLs: {str(e)}")
            return []
    
    def start_scraping(self, config):
        """Start scraping with simple config"""
        if self.is_running:
            return False, "Already running"
        
        try:
            max_properties = config.get('max_properties', 10)
            skip_existing = config.get('skip_existing', True)
            
            # Load URLs
            urls = self.load_urls_for_scraping(max_properties, skip_existing)
            
            if not urls:
                return False, "No URLs to scrape"
            
            # Reset progress
            self.progress = {
                'total_urls': len(urls),
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'current_url': '',
                'current_title': ''
            }
            
            # Start scraping thread
            self.scraper_thread = threading.Thread(target=self._run_scraping, args=(urls,))
            self.scraper_thread.daemon = True
            self.scraper_thread.start()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            return True, f"Started scraping {len(urls)} properties"
            
        except Exception as e:
            return False, f"Failed to start: {str(e)}"
    
    def _run_scraping(self, urls):
        """Run scraping with simple logic"""
        try:
            self.add_log("Initializing scraper...")
            
            # Create scraper
            self.scraper = ComprehensiveIndividualListingScraper(headless=True)
            
            # Setup driver
            if not self.scraper.setup_driver():
                self.add_log("ERROR: Failed to setup browser")
                return
            
            self.add_log("Browser setup successful, starting extraction...")
            
            # Process each URL
            for i, url in enumerate(urls, 1):
                if not self.is_running:
                    break
                
                self.progress['processed'] = i
                self.progress['current_url'] = url
                
                self.add_log(f"Processing {i}/{len(urls)}: {url}")
                
                try:
                    # Extract data
                    property_data = self.scraper.extract_comprehensive_property_data(url)
                    
                    if property_data:
                        # Save to database
                        if self.scraper.save_to_database(property_data):
                            self.progress['successful'] += 1
                            self.progress['current_title'] = property_data.get('title', 'Unknown')[:50]
                            self.add_log(f"SUCCESS: {property_data.get('title', 'Unknown')[:50]}")
                        else:
                            self.progress['failed'] += 1
                            self.add_log(f"ERROR: Failed to save to database")
                    else:
                        self.progress['failed'] += 1
                        self.add_log(f"ERROR: No data extracted")
                
                except Exception as e:
                    self.progress['failed'] += 1
                    self.add_log(f"ERROR: {str(e)}")
                
                # Simple delay
                time.sleep(3)
            
            self.add_log(f"Scraping completed: {self.progress['successful']} successful, {self.progress['failed']} failed")
            
        except Exception as e:
            self.add_log(f"Scraping thread error: {str(e)}")
        finally:
            self.is_running = False
            if self.scraper and self.scraper.driver:
                try:
                    self.scraper.driver.quit()
                except:
                    pass
    
    def stop_scraping(self):
        """Stop scraping"""
        self.is_running = False
        self.add_log("Scraping stopped")
        return True, "Stopped"
    
    def get_stats(self):
        """Get current stats"""
        elapsed_time = "00:00:00"
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_time = str(elapsed).split('.')[0]
        
        return {
            'is_running': self.is_running,
            'progress': self.progress,
            'logs': self.logs[-10:],  # Last 10 logs
            'system': {
                'memory_usage': psutil.virtual_memory().percent,
                'cpu_usage': psutil.cpu_percent()
            },
            'timing': {
                'elapsed_time': elapsed_time
            }
        }

# Global monitor
monitor = SimpleScraperMonitor()

@app.route('/')
def dashboard():
    """Simple dashboard"""
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>Simple 99acres Scraper Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin: 20px 0; }
        .stat { text-align: center; padding: 15px; background: #e3f2fd; border-radius: 8px; }
        .stat-value { font-size: 2em; font-weight: bold; color: #1976d2; }
        .stat-label { color: #666; margin-top: 5px; }
        .controls { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #1976d2; color: white; }
        .btn-danger { background: #d32f2f; color: white; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .logs { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; }
        .log-entry { margin: 5px 0; }
        .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #1976d2; transition: width 0.3s; }
        .source-info { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 Simple 99acres Scraper Dashboard</h1>
            <p>Working dashboard with proper database connections</p>
        </div>
        
        <div class="card">
            <h3>📊 Data Source Information</h3>
            <div id="source-info">Loading...</div>
        </div>
        
        <div class="card">
            <h3>🔧 Controls</h3>
            <div class="controls">
                <label>Max Properties: <input type="number" id="max-properties" value="10" min="1" max="100"></label>
                <label>Skip Existing: 
                    <select id="skip-existing">
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </label>
                <br><br>
                <button class="btn btn-primary" onclick="startScraping()" id="start-btn">▶️ Start Scraping</button>
                <button class="btn btn-danger" onclick="stopScraping()" id="stop-btn" disabled>⏹️ Stop</button>
            </div>
        </div>
        
        <div class="card">
            <h3>📈 Progress</h3>
            <div class="stats">
                <div class="stat">
                    <div class="stat-value" id="processed">0</div>
                    <div class="stat-label">Processed</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="successful">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="failed">0</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="status">Stopped</div>
                    <div class="stat-label">Status</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progress-text" style="text-align: center; margin-top: 10px;">Ready to start</div>
            
            <div style="margin-top: 15px;">
                <strong>Current:</strong> <span id="current-property">None</span><br>
                <strong>Elapsed:</strong> <span id="elapsed-time">00:00:00</span>
            </div>
        </div>
        
        <div class="card">
            <h3>📝 Activity Logs</h3>
            <div class="logs" id="logs">Ready to start...</div>
        </div>
    </div>

    <script>
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update progress
                    document.getElementById('processed').textContent = data.progress.processed;
                    document.getElementById('successful').textContent = data.progress.successful;
                    document.getElementById('failed').textContent = data.progress.failed;
                    document.getElementById('status').textContent = data.is_running ? 'Running' : 'Stopped';
                    
                    // Update progress bar
                    const percent = data.progress.total_urls > 0 ? 
                        (data.progress.processed / data.progress.total_urls) * 100 : 0;
                    document.getElementById('progress-fill').style.width = percent + '%';
                    document.getElementById('progress-text').textContent = 
                        `${data.progress.processed}/${data.progress.total_urls} (${percent.toFixed(1)}%)`;
                    
                    // Update current property
                    document.getElementById('current-property').textContent = 
                        data.progress.current_title || data.progress.current_url || 'None';
                    
                    // Update timing
                    document.getElementById('elapsed-time').textContent = data.timing.elapsed_time;
                    
                    // Update logs
                    const logsContainer = document.getElementById('logs');
                    logsContainer.innerHTML = data.logs.map(log => 
                        `<div class="log-entry">[${log.timestamp}] ${log.message}</div>`
                    ).join('');
                    logsContainer.scrollTop = logsContainer.scrollHeight;
                    
                    // Update buttons
                    document.getElementById('start-btn').disabled = data.is_running;
                    document.getElementById('stop-btn').disabled = !data.is_running;
                })
                .catch(error => console.error('Error:', error));
        }
        
        function loadSourceInfo() {
            fetch('/api/sources')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('source-info');
                    container.innerHTML = '';
                    
                    Object.values(data).forEach(source => {
                        const div = document.createElement('div');
                        div.className = 'source-info';
                        div.innerHTML = `
                            <strong>${source.name}</strong><br>
                            ${source.description}<br>
                            <strong>Total:</strong> ${source.total} | 
                            <strong>Extracted:</strong> ${source.extracted} | 
                            <strong>Remaining:</strong> ${source.remaining}
                        `;
                        container.appendChild(div);
                    });
                })
                .catch(error => console.error('Error loading sources:', error));
        }
        
        function startScraping() {
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                skip_existing: document.getElementById('skip-existing').value === 'true'
            };
            
            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Failed to start: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error starting scraping');
            });
        }
        
        function stopScraping() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .catch(error => console.error('Error:', error));
        }
        
        // Initialize
        loadSourceInfo();
        updateStats();
        setInterval(updateStats, 2000);
    </script>
</body>
</html>
    '''

@app.route('/api/stats')
def get_stats():
    return jsonify(monitor.get_stats())

@app.route('/api/sources')
def get_sources():
    return jsonify(monitor.get_url_sources())

@app.route('/api/start', methods=['POST'])
def start_scraping():
    config = request.json
    success, message = monitor.start_scraping(config)
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_scraping():
    success, message = monitor.stop_scraping()
    return jsonify({'success': success, 'message': message})

def main():
    print("🖥️ Starting Simple Working Dashboard")
    print("=" * 50)
    print("🌐 Dashboard: http://localhost:5002")
    print("🔧 Fixed database connections and browser setup")
    print("📊 Ready for comprehensive testing")
    
    app.run(debug=True, host='0.0.0.0', port=5002)

if __name__ == "__main__":
    main()
