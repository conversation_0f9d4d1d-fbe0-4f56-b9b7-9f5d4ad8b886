{"website": {"base_url": "https://www.magicbricks.com/property-for-sale-in-gurgaon-pppfs", "properties_per_page": 30, "max_pages": 200, "pagination_selector": ".mb-srp__pagination button[aria-label*='Next']"}, "selectors": {"property_cards": ".mb-srp__card", "title": "h2.mb-srp__card--title", "price": {"primary": ".mb-srp__card__price--amount", "fallback": "[class*='price'] span:contains('₹')", "regex": "₹[\\d.,\\s]+(?:Cr|Lakh|crore|lakh)?"}, "price_per_sqft": {"primary": ".mb-srp__card__price--per-unit", "fallback": "[class*='price']:contains('per sqft')", "regex": "₹[\\d.,\\s]+per sqft"}, "society": {"primary": "a[href*='pdpid']", "fallback": ["*:contains('Society') + *", ".mb-srp__card__society--name", ".mb-srp__card__builder--name", ".mb-srp__card__project--name"]}, "area": {"super_area": "*:contains('Super Area') + *", "carpet_area": "*:contains('Carpet Area') + *", "plot_area": "*:contains('Plot Area') + *", "fallback": ["*:contains('sqft')", "*:contains('sqyrd')", "*:contains('Super Area')", "*:contains('Carpet Area')", "*:contains('Plot Area')"], "regex_patterns": ["Super Area[\\s\\S]*?(\\d+(?:,\\d+)*[\\s]*(?:sqft|sq\\.?\\s*ft))", "Carpet Area[\\s\\S]*?(\\d+(?:,\\d+)*[\\s]*(?:sqft|sq\\.?\\s*ft|sqyrd|sq\\.?\\s*yard))", "Plot Area[\\s\\S]*?(\\d+(?:,\\d+)*[\\s]*(?:sqft|sq\\.?\\s*ft|sqyrd|sq\\.?\\s*yard|acres?))", "\\d+(?:,\\d+)*[\\s]*(?:sqft|sq\\.?\\s*ft)", "\\d+(?:,\\d+)*[\\s]*(?:sqyrd|sq\\.?\\s*yard)", "\\d+(?:,\\d+)*[\\s]*acres?"], "property_type_mapping": {"apartment": ["super_area", "carpet_area"], "house": ["carpet_area", "super_area"], "plot": ["plot_area"], "land": ["plot_area"], "villa": ["super_area", "carpet_area"], "floor": ["super_area", "carpet_area"]}}, "bedrooms": {"primary": "[data-summary='bedrooms'] .mb-srp__card__summary--value", "fallback": ".mb-srp__card--title", "regex": "(\\d+)\\s*BHK"}, "bathrooms": "[data-summary='bathrooms'] .mb-srp__card__summary--value", "floor": "[data-summary='floor'] .mb-srp__card__summary--value", "furnishing": "[data-summary='furnishing'] .mb-srp__card__summary--value", "status": {"primary": "[data-summary='status'] .mb-srp__card__summary--value", "fallback": [".mb-srp__card__status", "[class*='status']", ".mb-srp__card__possession", "[class*='possession']", ".mb-srp__card__ready", "[class*='ready']"], "regex_patterns": ["Ready to Move", "Poss\\. by \\w+ '\\d+", "Possession by \\w+ '\\d+", "Under Construction", "New Property", "New Launch", "Resale", "Immediate Possession", "Available"], "plot_alternatives": ["*:contains('Transaction')", "*:contains('Resale')", "*:contains('New')"], "conditional_logic": {"plot_keywords": ["Plot", "Land", "plot", "land"], "plot_status_mapping": {"Resale": "Resale Plot", "New": "New Plot", "Transaction": "Available"}}}, "facing": "[data-summary='facing'] .mb-srp__card__summary--value", "overlooking": "[data-summary='overlooking'] .mb-srp__card__summary--value", "ownership": "[data-summary='ownership'] .mb-srp__card__summary--value", "parking": "[data-summary='parking'] .mb-srp__card__summary--value", "balcony": "[data-summary='balcony'] .mb-srp__card__summary--value", "age": "[data-summary='age'] .mb-srp__card__summary--value", "transaction_type": "[data-summary='transaction'] .mb-srp__card__summary--value", "possession_date": "[data-summary='possession'] .mb-srp__card__summary--value", "image": "img.mb-srp__card__photo__fig--graphic", "property_url": "a[href*='propertyDetails'], a[href*='property']", "owner_info": ".mb-srp__card__ads--name", "contact_button": ".mb-srp__card__contact button, [class*='contact'] button", "description": ".mb-srp__card__description p, .mb-srp__card__summary__description"}, "browser": {"headless": true, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"], "viewport_sizes": [{"width": 1920, "height": 1080}, {"width": 1366, "height": 768}, {"width": 1536, "height": 864}, {"width": 1440, "height": 900}], "chrome_options": ["--no-sandbox", "--disable-dev-shm-usage", "--disable-blink-features=AutomationControlled", "--exclude-switches=enable-automation", "--disable-extensions-file-access-check", "--disable-extensions-http-throttling", "--disable-extensions-except", "--disable-plugins-discovery", "--disable-preconnect", "--disable-sync", "--no-first-run", "--no-default-browser-check", "--disable-default-apps"]}, "delays": {"page_load_min": 3, "page_load_max": 7, "between_requests_min": 2, "between_requests_max": 5, "react_render_wait": 3, "element_wait_timeout": 10, "page_timeout": 30}, "performance": {"enable_optimization": true, "parallel_processing": {"enabled": true, "max_workers": 4, "batch_size": 5}, "browser_optimization": {"headless": false, "disable_images": true, "disable_css": false, "disable_javascript": false, "page_load_strategy": "normal"}, "optimized_delays": {"page_load_min": 2, "page_load_max": 4, "between_requests_min": 1, "between_requests_max": 3, "react_render_wait": 2, "element_wait_timeout": 8, "page_timeout": 20, "smart_waiting": true}}, "retry": {"max_retries": 3, "backoff_factor": 2, "max_consecutive_failures": 5, "circuit_breaker_threshold": 10}, "output": {"csv_filename": "magicbricks_properties_{timestamp}.csv", "json_filename": "magicbricks_properties_{timestamp}.json", "checkpoint_filename": "scraper_checkpoint_{timestamp}.json", "log_filename": "scraper_log_{timestamp}.log", "export_directory": "output", "checkpoint_interval": 50}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "console_output": true, "file_output": true, "detailed_progress": true, "performance_metrics": true}, "data_validation": {"required_fields": ["title", "price"], "price_range": {"min": 100000, "max": 1000000000}, "area_range": {"min": 100, "max": 50000}, "duplicate_check_fields": ["title", "society", "price", "area"]}}