#!/usr/bin/env python3
"""
Database Diagnostic Tool
Comprehensive analysis of all databases and their structure
"""

import sqlite3
import os

def check_database(db_path, db_name):
    """Check database structure and content"""
    print(f"\n{'='*60}")
    print(f"🔍 ANALYZING: {db_name}")
    print(f"📁 Path: {db_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file does not exist!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ No tables found in database!")
            conn.close()
            return
        
        print(f"📊 Tables found: {len(tables)}")
        
        for table_name, in tables:
            print(f"\n📋 Table: {table_name}")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"   Columns: {len(columns)}")
            for col in columns[:5]:  # Show first 5 columns
                print(f"     - {col[1]} ({col[2]})")
            if len(columns) > 5:
                print(f"     ... and {len(columns) - 5} more columns")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   Records: {count}")
            
            # Sample data for properties table
            if table_name == 'properties' and count > 0:
                cursor.execute("SELECT property_url FROM properties WHERE property_url IS NOT NULL AND property_url != '' LIMIT 3")
                sample_urls = cursor.fetchall()
                print(f"   Sample URLs:")
                for url, in sample_urls:
                    print(f"     - {url}")
        
        conn.close()
        print(f"✅ {db_name} analysis complete")
        
    except Exception as e:
        print(f"❌ Error analyzing {db_name}: {str(e)}")

def main():
    """Main diagnostic function"""
    print("🔍 99acres Database Comprehensive Diagnostic")
    print("=" * 60)
    
    # Check all databases
    databases = [
        ('data/99acres_properties.db', 'Main Properties Database'),
        ('data/individual_properties_comprehensive.db', 'Individual Properties Database'),
        ('data/optimized_individual_properties.db', 'Optimized Individual Properties'),
        ('data/optimized_single_test.db', 'Optimized Single Test Database')
    ]
    
    for db_path, db_name in databases:
        check_database(db_path, db_name)
    
    print(f"\n{'='*60}")
    print("🎯 DIAGNOSTIC SUMMARY")
    print(f"{'='*60}")
    
    # Check which database has the most properties
    main_db_count = 0
    try:
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NOT NULL AND property_url != ''")
        main_db_count = cursor.fetchone()[0]
        conn.close()
        print(f"📊 Main database has {main_db_count} valid property URLs")
    except:
        print("❌ Could not count main database properties")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    if main_db_count > 0:
        print(f"✅ Use main database (99acres_properties.db) as URL source")
        print(f"✅ Table name should be 'properties', not 'individual_properties_comprehensive'")
    else:
        print(f"❌ Main database has no valid URLs - need to populate it first")

if __name__ == "__main__":
    main()
