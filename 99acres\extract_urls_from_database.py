#!/usr/bin/env python3
"""
Extract Property URLs from Existing Database
Simple script to get individual property URLs from the existing scraped data
"""

import sqlite3
import json
from datetime import datetime

def extract_property_urls():
    """Extract property URLs from the existing database"""
    
    db_path = 'data/99acres_properties.db'
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 Found tables: {[table[0] for table in tables]}")
        
        # Get property URLs
        cursor.execute("SELECT property_url, title, price, locality, bedrooms FROM properties WHERE property_url IS NOT NULL AND property_url != '' LIMIT 50")
        properties = cursor.fetchall()
        
        print(f"✅ Found {len(properties)} properties with URLs")
        
        # Format the data
        property_urls = []
        for i, (url, title, price, location, bedrooms) in enumerate(properties, 1):
            property_urls.append({
                'index': i,
                'url': url,
                'title': title,
                'price': price,
                'location': location,
                'bedrooms': bedrooms
            })
        
        # Save to JSON for easy access
        output_data = {
            'extraction_timestamp': datetime.now().isoformat(),
            'total_urls': len(property_urls),
            'property_urls': property_urls
        }
        
        with open('property_urls_for_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        # Print sample URLs
        print(f"\n📋 Sample Property URLs:")
        for prop in property_urls[:10]:
            print(f"   {prop['index']}. {prop['title'][:50]}...")
            print(f"      URL: {prop['url']}")
            print(f"      Price: {prop['price']}, Location: {prop['location']}")
            print()
        
        print(f"💾 Saved {len(property_urls)} URLs to: property_urls_for_analysis.json")
        
        conn.close()
        return property_urls
        
    except Exception as e:
        print(f"❌ Error extracting URLs: {str(e)}")
        return []

if __name__ == "__main__":
    print("🔍 Extracting Property URLs from Database")
    print("=" * 50)
    
    urls = extract_property_urls()
    
    if urls:
        print(f"\n✅ Successfully extracted {len(urls)} property URLs")
        print("📄 Ready for individual property field analysis!")
    else:
        print("❌ No URLs found. Check database connection.")
