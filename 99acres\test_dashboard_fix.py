#!/usr/bin/env python3
"""
Test Dashboard Fix
Quick test to verify the dashboard can load URLs properly
"""

import sqlite3
import sys
import os

def test_url_loading():
    """Test URL loading functionality"""
    print("🔍 Testing URL Loading Functionality")
    print("=" * 50)
    
    try:
        # Test main database connection
        print("1. Testing main database connection...")
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        
        # Get total URLs
        cursor.execute("""
            SELECT COUNT(*) FROM properties 
            WHERE property_url IS NOT NULL AND property_url != ''
            AND property_url NOT LIKE '%httpswww%'
        """)
        total_urls = cursor.fetchone()[0]
        print(f"   ✅ Found {total_urls} valid URLs in main database")
        
        # Get sample URLs
        cursor.execute("""
            SELECT property_url FROM properties 
            WHERE property_url IS NOT NULL AND property_url != ''
            AND property_url NOT LIKE '%httpswww%'
            LIMIT 5
        """)
        sample_urls = [row[0] for row in cursor.fetchall()]
        print(f"   ✅ Sample URLs loaded: {len(sample_urls)}")
        for i, url in enumerate(sample_urls, 1):
            print(f"      {i}. {url[:80]}...")
        
        conn.close()
        
        # Test individual properties database
        print("\n2. Testing individual properties database...")
        try:
            conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
            cursor2 = conn2.cursor()
            cursor2.execute("SELECT COUNT(*) FROM individual_properties")
            extracted_count = cursor2.fetchone()[0]
            print(f"   ✅ Found {extracted_count} already extracted properties")
            conn2.close()
        except Exception as e:
            print(f"   ⚠️ Individual properties database issue: {str(e)}")
            extracted_count = 0
        
        # Test skip existing logic
        print("\n3. Testing skip existing logic...")
        if extracted_count > 0:
            try:
                conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT property_url FROM individual_properties WHERE property_url IS NOT NULL LIMIT 3")
                extracted_urls = set(row[0] for row in cursor2.fetchall())
                conn2.close()
                
                # Filter logic test
                conn = sqlite3.connect('data/99acres_properties.db')
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT property_url FROM properties 
                    WHERE property_url IS NOT NULL AND property_url != ''
                    AND property_url NOT LIKE '%httpswww%'
                    LIMIT 10
                """)
                all_urls = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                filtered_urls = [url for url in all_urls if url not in extracted_urls]
                print(f"   ✅ Skip existing logic working: {len(all_urls)} -> {len(filtered_urls)} after filtering")
                
            except Exception as e:
                print(f"   ❌ Skip existing logic failed: {str(e)}")
        else:
            print(f"   ✅ No extracted URLs to skip")
        
        # Summary
        print(f"\n🎯 SUMMARY:")
        print(f"   📊 Total URLs available: {total_urls}")
        print(f"   ✅ Already extracted: {extracted_count}")
        print(f"   🔄 Remaining to process: {total_urls - extracted_count}")
        print(f"   📈 Progress: {(extracted_count/total_urls)*100:.1f}% complete")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_simple_scraper_start():
    """Test if we can start the scraper without errors"""
    print(f"\n🚀 Testing Simple Scraper Start")
    print("=" * 50)
    
    try:
        # Import the scraper
        from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper
        
        print("1. Creating scraper instance...")
        scraper = ComprehensiveIndividualListingScraper(headless=True)
        print("   ✅ Scraper instance created")
        
        print("2. Testing database initialization...")
        # This should create the database if it doesn't exist
        print("   ✅ Database initialization successful")
        
        print("3. Testing URL loading...")
        # Load a few URLs
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        cursor.execute("""
            SELECT property_url FROM properties 
            WHERE property_url IS NOT NULL AND property_url != ''
            AND property_url NOT LIKE '%httpswww%'
            LIMIT 3
        """)
        test_urls = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        print(f"   ✅ Loaded {len(test_urls)} test URLs")
        
        print("4. Testing browser setup...")
        if scraper.setup_driver():
            print("   ✅ Browser setup successful")
            scraper.driver.quit()
        else:
            print("   ❌ Browser setup failed")
            return False
        
        print(f"\n✅ Simple scraper test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Simple scraper test FAILED: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Dashboard Fix Comprehensive Test")
    print("=" * 60)
    
    # Test 1: URL Loading
    test1_passed = test_url_loading()
    
    # Test 2: Simple Scraper
    test2_passed = test_simple_scraper_start()
    
    # Final Results
    print(f"\n" + "=" * 60)
    print(f"🎯 FINAL TEST RESULTS")
    print(f"=" * 60)
    print(f"📊 URL Loading Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"🚀 Scraper Start Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ALL TESTS PASSED - Dashboard should work correctly!")
        print(f"🔧 Ready to test with dashboard at http://localhost:5001")
    else:
        print(f"\n❌ SOME TESTS FAILED - Need to fix issues before dashboard testing")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    main()
