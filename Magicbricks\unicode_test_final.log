[ROCKET] Starting MagicBricks CLI Scraper
Traceback (most recent call last):
  File "D:\real estate\Scrapers\Magicbricks\cli_scraper.py", line 188, in <module>
    main()
    ~~~~^^
  File "D:\real estate\Scrapers\Magicbricks\cli_scraper.py", line 123, in main
    print(f"   \U0001f3d9\ufe0f City: {args.city}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode characters in position 3-4: character maps to <undefined>
