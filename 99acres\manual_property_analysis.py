#!/usr/bin/env python3
"""
Manual Property Analysis for 99acres
Get properly formatted URLs and analyze them manually
"""

import sqlite3
import json
from datetime import datetime

def get_sample_urls():
    """Get a sample of properly formatted URLs for manual analysis"""
    
    try:
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        
        print("🔍 Getting Sample URLs for Manual Analysis")
        print("=" * 50)
        
        # Get diverse sample with proper URL formatting
        sample_urls = []
        
        # Different property types and cities
        queries = [
            # Mumbai properties
            ("Mumbai", "Sale", 5),
            ("Mumbai", "Rent", 5),
            # Delhi properties  
            ("Delhi", "Sale", 5),
            ("Delhi", "Rent", 5),
            # Bangalore properties
            ("Bangalore", "Sale", 5),
            ("Bangalore", "Rent", 5),
            # Pune properties
            ("Pune", "Sale", 3),
            ("Pune", "Rent", 3),
        ]
        
        for city, prop_type, limit in queries:
            print(f"\n🏙️ Getting {city} {prop_type} properties:")
            
            cursor.execute("""
                SELECT property_url, title, price, locality, bedrooms, city
                FROM properties 
                WHERE city LIKE ? AND transaction_type LIKE ? 
                AND property_url IS NOT NULL AND property_url != ''
                ORDER BY RANDOM() LIMIT ?
            """, (f'%{city}%', f'%{prop_type}%', limit))
            
            results = cursor.fetchall()
            
            for url, title, price, locality, bedrooms, city in results:
                # Fix URL format
                if url.startswith('http'):
                    fixed_url = url
                elif url.startswith('www.'):
                    fixed_url = f"https://{url}"
                else:
                    fixed_url = f"https://www.99acres.com/{url}"
                
                sample_urls.append({
                    'url': fixed_url,
                    'title': title,
                    'price': price,
                    'locality': locality,
                    'bedrooms': bedrooms,
                    'city': city,
                    'property_type': prop_type,
                    'category': f"{city}_{prop_type}_{bedrooms}BHK"
                })
            
            print(f"   Found {len(results)} properties")
        
        # Save sample
        sample_data = {
            'creation_timestamp': datetime.now().isoformat(),
            'total_sample_size': len(sample_urls),
            'sample_urls': sample_urls
        }
        
        with open('manual_analysis_sample.json', 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Created sample of {len(sample_urls)} URLs")
        print(f"💾 Saved to: manual_analysis_sample.json")
        
        # Print first 10 URLs for manual browsing
        print(f"\n📋 Sample URLs for Manual Analysis:")
        for i, prop in enumerate(sample_urls[:10], 1):
            print(f"\n{i:2d}. {prop['title'][:50]}...")
            print(f"    Category: {prop['category']}")
            print(f"    URL: {prop['url']}")
        
        if len(sample_urls) > 10:
            print(f"\n... and {len(sample_urls) - 10} more URLs in the JSON file")
        
        conn.close()
        return sample_urls
        
    except Exception as e:
        print(f"❌ Error getting sample URLs: {str(e)}")
        return []

def create_analysis_template():
    """Create a template for manual analysis documentation"""
    
    template = {
        "manual_analysis_documentation": {
            "analysis_date": datetime.now().isoformat(),
            "methodology": "Manual browsing and inspection of individual property pages",
            "properties_analyzed": [],
            "common_patterns": {
                "page_structure": {},
                "data_fields": {},
                "css_selectors": {},
                "javascript_elements": {}
            },
            "property_types": {
                "sale_properties": {
                    "unique_fields": [],
                    "common_selectors": [],
                    "page_layout": ""
                },
                "rent_properties": {
                    "unique_fields": [],
                    "common_selectors": [],
                    "page_layout": ""
                }
            },
            "field_extraction_strategy": {
                "price_fields": {},
                "specification_fields": {},
                "location_fields": {},
                "amenity_fields": {},
                "contact_fields": {},
                "image_fields": {}
            },
            "recommendations": []
        }
    }
    
    with open('manual_analysis_template.json', 'w', encoding='utf-8') as f:
        json.dump(template, f, indent=2, ensure_ascii=False)
    
    print(f"📋 Created analysis template: manual_analysis_template.json")

if __name__ == "__main__":
    print("🔍 99acres Manual Property Analysis Setup")
    print("=" * 50)
    
    # Get sample URLs
    sample_urls = get_sample_urls()
    
    # Create analysis template
    create_analysis_template()
    
    if sample_urls:
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Open the URLs from manual_analysis_sample.json")
        print(f"2. Manually browse each property page")
        print(f"3. Document findings in manual_analysis_template.json")
        print(f"4. Focus on:")
        print(f"   - Page structure and layout")
        print(f"   - CSS selectors for key data")
        print(f"   - JavaScript-loaded content")
        print(f"   - Differences between Sale vs Rent")
        print(f"   - Differences between property types")
        print(f"   - Common patterns across cities")
    else:
        print("❌ Failed to get sample URLs")
