#!/usr/bin/env python3
"""
Test the comprehensive data validation system
"""

from data_validation_system import DataValidationSystem
import json

def test_validation_system():
    """Test the validation system with various data scenarios"""
    validator = DataValidationSystem()
    
    print("🧪 Testing Comprehensive Data Validation System")
    print("="*60)
    
    # Test cases based on deep research findings
    test_cases = [
        {
            'name': 'Valid Property (Mumbai)',
            'data': {
                'title': '2 BHK Flat in Andheri West, Mumbai',
                'price': '₹1.5 Cr',
                'area': '850 sqft',
                'bedrooms': '2 BHK',
                'bathrooms': '2 Bath',
                'locality': 'Andheri West',
                'city': 'Mumbai',
                'property_type': 'Apartment',
                'transaction_type': 'Resale',
                'listing_type': 'Individual',
                'property_url': 'https://99acres.com/property-details-spid-123'
            }
        },
        {
            'name': 'Valid Project Property (Delhi)',
            'data': {
                'title': '3 BHK Apartment in Gurgaon',
                'price': '₹2.5 - 3.2 Cr',
                'area': '1200-1400 sqft',
                'bedrooms': '3 BHK',
                'locality': 'Sector 45',
                'city': 'Delhi',
                'property_type': 'apartment',
                'transaction_type': 'New Booking',
                'listing_type': 'Project',
                'property_url': 'https://99acres.com/project-name-npxid-456'
            }
        },
        {
            'name': 'Property with Cleaning Needed',
            'data': {
                'title': '  1 BHK FLAT in  mumbai  ',
                'price': '₹45 lac',  # Should be normalized to Lakh
                'area': '500 sq.ft',  # Should be normalized to sqft
                'bedrooms': '1 BHK',
                'locality': '  andheri  ',
                'city': 'mumbai',
                'property_type': 'flat',  # Should be normalized to Flat
                'property_url': 'https://99acres.com/property-details-spid-789'
            }
        },
        {
            'name': 'Invalid Property (Price too high)',
            'data': {
                'title': 'Luxury Villa',
                'price': '₹500 Cr',  # Too high
                'area': '100000 sqft',  # Too large
                'bedrooms': '15 BHK',  # Too many
                'locality': 'X',  # Too short
                'city': 'Mumbai',
                'property_url': 'invalid-url'
            }
        },
        {
            'name': 'Property with Missing Required Fields',
            'data': {
                'title': '',  # Missing
                'price': '',  # Missing
                'area': '',   # Missing
                'locality': 'Andheri',
                'city': 'Mumbai'
            }
        },
        {
            'name': 'Property with Invalid Formats',
            'data': {
                'title': 'Test Property',
                'price': 'Price on Request',  # Invalid format
                'area': 'Large',  # Invalid format
                'bedrooms': 'Many',  # Invalid format
                'pincode': '12345',  # Invalid pincode
                'property_type': 'Unknown Type',  # Invalid type
                'transaction_type': 'Invalid Transaction',  # Invalid type
                'property_url': 'https://99acres.com/property-details-spid-999'
            }
        }
    ]
    
    # Run validation tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print("-" * 50)
        
        is_valid, errors, cleaned_data = validator.validate_property_data(test_case['data'])
        
        print(f"✅ Valid: {is_valid}")
        print(f"📊 Quality Score: {cleaned_data.get('data_quality_score', 0)}%")
        print(f"🔧 Validation Status: {cleaned_data.get('validation_status', 'unknown')}")
        
        if errors:
            print(f"❌ Errors ({len(errors)}):")
            for error in errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
            if len(errors) > 3:
                print(f"   ... and {len(errors) - 3} more errors")
        else:
            print("✅ No validation errors")
        
        # Show cleaning actions
        cleaning_actions = [action for action in validator.get_validation_stats()['cleaning_actions'] 
                          if any(action['field'] in test_case['data'] for action in [action])]
        
        if cleaning_actions:
            print(f"🧹 Cleaning Actions:")
            for action in cleaning_actions[-3:]:  # Show last 3 cleaning actions
                print(f"   {action['field']}: '{action['original']}' → '{action['cleaned']}'")
        
        # Show key cleaned fields
        key_fields = ['title', 'price', 'area', 'property_type', 'transaction_type']
        cleaned_fields = {field: cleaned_data.get(field) for field in key_fields if cleaned_data.get(field)}
        
        if cleaned_fields:
            print(f"📋 Key Cleaned Fields:")
            for field, value in cleaned_fields.items():
                original_value = test_case['data'].get(field, '')
                if str(value) != str(original_value):
                    print(f"   {field}: {value} (was: {original_value})")
                else:
                    print(f"   {field}: {value}")
    
    # Show overall validation statistics
    print(f"\n📊 Overall Validation Statistics:")
    print("="*50)
    stats = validator.get_validation_stats()
    print(f"Total properties validated: {stats['total_validated']}")
    print(f"Total cleaning actions: {len(stats['cleaning_actions'])}")
    print(f"Total validation errors: {len(stats['validation_errors'])}")
    
    # Show common cleaning patterns
    if stats['cleaning_actions']:
        print(f"\n🧹 Common Cleaning Patterns:")
        cleaning_by_field = {}
        for action in stats['cleaning_actions']:
            field = action['field']
            if field not in cleaning_by_field:
                cleaning_by_field[field] = []
            cleaning_by_field[field].append(action)
        
        for field, actions in cleaning_by_field.items():
            print(f"   {field}: {len(actions)} cleaning actions")
    
    # Show common validation errors
    if stats['validation_errors']:
        print(f"\n❌ Common Validation Errors:")
        error_types = {}
        for error in stats['validation_errors']:
            error_type = error.split(':')[0] if ':' in error else error
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   {error_type}: {count} occurrences")
    
    print(f"\n✅ Validation System Testing Complete!")
    print(f"🎯 System is ready for production use with comprehensive validation")

def test_specific_patterns():
    """Test specific patterns discovered in deep research"""
    validator = DataValidationSystem()
    
    print(f"\n🔬 Testing Specific Patterns from Deep Research")
    print("="*60)
    
    # Price patterns from research
    price_patterns = [
        '₹1.36 Cr',
        '₹48 Lakh',
        '₹34.25 Lac',
        '₹3.37 - 4.45 Cr',
        '₹1.53 - 1.55 Cr',
        'Starting from ₹2.5 Cr',
        '₹1.5 Cr onwards'
    ]
    
    print(f"\n💰 Testing Price Patterns:")
    for price in price_patterns:
        is_valid, errors, cleaned = validator._validate_price(price)
        print(f"   {price}: {'✅' if is_valid else '❌'} {cleaned}")
    
    # Area patterns from research
    area_patterns = [
        '850 sqft',
        '1200-1400 sqft',
        '740 sq ft to 752 sq ft',
        '500 sq.ft',
        '1000'
    ]
    
    print(f"\n📐 Testing Area Patterns:")
    for area in area_patterns:
        is_valid, errors, cleaned = validator._validate_area(area)
        print(f"   {area}: {'✅' if is_valid else '❌'} {cleaned}")
    
    # Property type patterns from research
    property_types = [
        'Apartment',
        'apartment',
        'Flat',
        'flat',
        'Villa',
        'Independent House',
        'Builder Floor',
        'Unknown Type'
    ]
    
    print(f"\n🏢 Testing Property Type Patterns:")
    for prop_type in property_types:
        is_valid, errors, cleaned = validator._validate_property_type(prop_type)
        print(f"   {prop_type}: {'✅' if is_valid else '❌'} {cleaned}")

if __name__ == "__main__":
    test_validation_system()
    test_specific_patterns()
