#!/usr/bin/env python3
"""
Simple Field Analyzer for 99acres Individual Properties
Uses existing property URLs from database to analyze individual property pages
"""

import json
import requests
from bs4 import BeautifulSoup
import time
import re
from collections import defaultdict
from datetime import datetime

class SimpleFieldAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.all_fields = defaultdict(set)
        self.analyzed_properties = []
        
    def load_property_urls(self):
        """Load property URLs from the extracted JSON file"""
        try:
            with open('property_urls_for_analysis.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data['property_urls']
        except Exception as e:
            print(f"❌ Error loading URLs: {str(e)}")
            return []
    
    def extract_fields_from_property_page(self, soup, url):
        """Extract all possible fields from an individual property page"""
        fields = {}
        page_text = soup.get_text()
        
        # 1. Basic Information
        fields['url'] = url
        title_tag = soup.find('title')
        fields['page_title'] = title_tag.get_text(strip=True) if title_tag else ''
        
        # 2. Price Patterns
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/sqft', 'price_per_sqft'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 3. Property Specifications
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 4. Location Information
        location_patterns = [
            (r'Located in\s*([^,\n]+)', 'location_area'),
            (r'Near\s*([^,\n]+)', 'nearby_landmarks'),
            (r'(\d{6})', 'pincode'),
            (r'(Mumbai|Delhi|Bangalore|Pune|Chennai|Hyderabad)', 'city')
        ]
        
        for pattern, field_name in location_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 5. Builder Information
        builder_patterns = [
            (r'By\s*([A-Z][^,\n]+(?:Group|Developers?|Builders?|Construction|Realty|Properties))', 'builder_name'),
            (r'Developer\s*:\s*([^,\n]+)', 'developer_name'),
            (r'RERA\s*(?:ID|Number)\s*:\s*([A-Z0-9]+)', 'rera_number')
        ]
        
        for pattern, field_name in builder_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 6. Amenities
        amenities_keywords = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
            'Club House', 'Lift', 'Power Backup', 'Water Supply', 'Intercom',
            'Fire Safety', 'CCTV', 'Jogging Track', 'Tennis Court', 'Basketball'
        ]
        
        found_amenities = []
        for amenity in amenities_keywords:
            if amenity.lower() in page_text.lower():
                found_amenities.append(amenity)
        
        if found_amenities:
            fields['amenities'] = found_amenities
        
        # 7. Contact Information
        contact_patterns = [
            (r'(\+91[\s-]?\d{10})', 'phone_numbers'),
            (r'(\d{10})', 'mobile_numbers'),
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 'email_addresses')
        ]
        
        for pattern, field_name in contact_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                fields[field_name] = list(set(matches))
        
        # 8. Meta Information
        meta_description = soup.find('meta', attrs={'name': 'description'})
        if meta_description:
            fields['meta_description'] = meta_description.get('content', '')
        
        # 9. Images
        images = soup.find_all('img', src=True)
        property_images = [img.get('src') for img in images if img.get('src') and 'property' in img.get('src', '').lower()]
        if property_images:
            fields['total_images'] = len(property_images)
        
        # 10. JSON-LD Structured Data
        json_scripts = soup.find_all('script', type='application/ld+json')
        for i, script in enumerate(json_scripts):
            try:
                json_data = json.loads(script.string)
                fields[f'structured_data_{i}'] = json_data
            except:
                pass
        
        return fields
    
    def analyze_properties(self, max_properties=20):
        """Analyze individual property pages"""
        print(f"🚀 Starting Simple Field Analysis")
        print(f"🎯 Target: {max_properties} individual property pages")
        print("="*60)
        
        # Load property URLs
        property_urls = self.load_property_urls()
        if not property_urls:
            print("❌ No property URLs found")
            return None
        
        print(f"📋 Loaded {len(property_urls)} property URLs from database")
        
        # Analyze properties
        analyzed_count = 0
        for i, prop in enumerate(property_urls[:max_properties]):
            url = prop['url']
            
            print(f"\n🏠 Analyzing property {analyzed_count + 1}/{max_properties}")
            print(f"   Title: {prop['title'][:50]}...")
            print(f"   URL: {url}")
            
            try:
                # Get page content
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                # Parse and analyze
                soup = BeautifulSoup(response.text, 'html.parser')
                fields = self.extract_fields_from_property_page(soup, url)
                
                # Store results
                analysis_result = {
                    'property_index': analyzed_count + 1,
                    'database_info': prop,
                    'extracted_fields': fields,
                    'total_fields_extracted': len(fields),
                    'analysis_timestamp': datetime.now().isoformat()
                }
                
                self.analyzed_properties.append(analysis_result)
                
                # Update global field collection
                for field_name, field_value in fields.items():
                    self.all_fields[field_name].add(str(field_value)[:100])
                
                print(f"   ✅ Extracted {len(fields)} field types")
                analyzed_count += 1
                
                # Respectful delay
                time.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error analyzing property: {str(e)}")
                continue
        
        print(f"\n✅ Analysis complete! Analyzed {analyzed_count} properties")
        return self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive field analysis report"""
        report = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_properties_analyzed': len(self.analyzed_properties),
                'total_unique_fields': len(self.all_fields),
                'analyzer_version': 'Simple 1.0'
            },
            'field_frequency_analysis': {},
            'sample_properties': self.analyzed_properties[:3],
            'all_analyzed_properties': self.analyzed_properties
        }
        
        # Field frequency analysis
        for field_name, values in self.all_fields.items():
            report['field_frequency_analysis'][field_name] = {
                'frequency': len(values),
                'sample_values': list(values)[:5]
            }
        
        return report

def main():
    """Main analysis function"""
    print("🔍 99acres Simple Field Analyzer")
    print("=" * 50)
    
    analyzer = SimpleFieldAnalyzer()
    
    # Analyze 20 properties
    report = analyzer.analyze_properties(max_properties=20)
    
    if report:
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'simple_field_analysis_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🎉 SIMPLE FIELD ANALYSIS COMPLETE!")
        print("="*60)
        print(f"📊 Properties Analyzed: {report['analysis_metadata']['total_properties_analyzed']}")
        print(f"🔍 Unique Fields Found: {report['analysis_metadata']['total_unique_fields']}")
        print(f"📄 Report saved: {filename}")
        
        # Print top fields
        print(f"\n🏆 TOP 15 MOST COMMON FIELDS:")
        sorted_fields = sorted(report['field_frequency_analysis'].items(), 
                             key=lambda x: x[1]['frequency'], reverse=True)
        for i, (field_name, field_info) in enumerate(sorted_fields[:15], 1):
            print(f"   {i:2d}. {field_name}: {field_info['frequency']} occurrences")
        
        print(f"\n📋 Analysis complete! Check {filename} for detailed results.")
    else:
        print("❌ Analysis failed.")

if __name__ == "__main__":
    main()
