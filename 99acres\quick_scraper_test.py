#!/usr/bin/env python3
"""
Quick Scraper Test
Test the fixed scraper with a single property
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper
import sqlite3

def test_single_property():
    """Test scraping a single property"""
    print("🧪 Quick Scraper Test")
    print("=" * 40)
    
    try:
        # Get a test URL
        print("1. Loading test URL...")
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        cursor.execute("""
            SELECT property_url FROM properties 
            WHERE property_url IS NOT NULL AND property_url != ''
            AND property_url NOT LIKE '%httpswww%'
            LIMIT 1
        """)
        test_url = cursor.fetchone()[0]
        conn.close()
        
        print(f"   📋 Test URL: {test_url}")
        
        # Create scraper
        print("\n2. Creating scraper...")
        scraper = ComprehensiveIndividualListingScraper(headless=True)
        
        # Setup driver
        print("\n3. Setting up browser...")
        if scraper.setup_driver():
            print("   ✅ Browser setup successful")
        else:
            print("   ❌ Browser setup failed")
            return False
        
        # Extract data
        print("\n4. Extracting property data...")
        property_data = scraper.extract_comprehensive_property_data(test_url)
        
        if property_data:
            print(f"   ✅ Data extracted successfully")
            print(f"   📊 Fields extracted: {len([k for k, v in property_data.items() if v])}")
            print(f"   🏠 Title: {property_data.get('title', 'N/A')[:50]}...")
            print(f"   💰 Price: {property_data.get('price_display', 'N/A')}")
            print(f"   🛏️ BHK: {property_data.get('bhk_config', 'N/A')}")
            
            # Save to database
            print("\n5. Saving to database...")
            if scraper.save_to_database(property_data):
                print("   ✅ Saved to database successfully")
                result = True
            else:
                print("   ❌ Failed to save to database")
                result = False
        else:
            print("   ❌ No data extracted")
            result = False
        
        # Cleanup
        print("\n6. Cleaning up...")
        scraper.driver.quit()
        print("   ✅ Browser closed")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    success = test_single_property()
    
    print(f"\n{'='*40}")
    if success:
        print("🎉 QUICK TEST PASSED!")
        print("✅ Scraper is working correctly")
        print("🔧 Ready for dashboard testing")
    else:
        print("❌ QUICK TEST FAILED!")
        print("⚠️ Need to fix issues before proceeding")
    
    return success

if __name__ == "__main__":
    main()
