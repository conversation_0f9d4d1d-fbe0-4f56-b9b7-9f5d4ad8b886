#!/usr/bin/env python3
"""
Comprehensive 99acres Research Tool
Systematic analysis of all property types, data fields, and website mechanisms
"""

import time
import json
import csv
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import requests
import re
import random
from urllib.parse import urljoin, urlparse
import os


class Comprehensive99acresResearcher:
    """Comprehensive research tool for 99acres website analysis"""
    
    def __init__(self):
        self.driver = None
        self.research_data = {
            'timestamp': datetime.now().isoformat(),
            'property_types': {},
            'cities_analyzed': [],
            'data_fields': {},
            'url_patterns': {},
            'anti_scraping': {},
            'edge_cases': [],
            'sample_properties': [],
            'technical_analysis': {}
        }
        
        # Test cities for comprehensive analysis
        self.test_cities = [
            'mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'chennai',
            'ahmedabad', 'kolkata', 'surat', 'jaipur', 'gurgaon', 'noida'
        ]
        
        # Property type URLs to test
        self.property_types = {
            'residential_sale': 'property-for-sale-in-{city}-ffid',
            'residential_rent': 'property-for-rent-in-{city}-ffid',
            'commercial_sale': 'commercial-property-for-sale-in-{city}-ffid',
            'commercial_rent': 'commercial-property-for-rent-in-{city}-ffid'
        }
        
        self.base_url = "https://www.99acres.com"
        
    def setup_driver(self):
        """Setup Chrome WebDriver with comprehensive options"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Random user agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Chrome WebDriver initialized with anti-detection measures")
    
    def random_delay(self, min_seconds=2, max_seconds=5):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def analyze_url_patterns(self):
        """Analyze URL patterns for different property types and cities"""
        print("\n🔍 PHASE 1: Analyzing URL Patterns")
        print("="*60)
        
        url_analysis = {}
        
        for prop_type, url_pattern in self.property_types.items():
            print(f"\n📋 Analyzing {prop_type.replace('_', ' ').title()}")
            url_analysis[prop_type] = {}
            
            for city in self.test_cities[:6]:  # Test first 6 cities
                url = f"{self.base_url}/{url_pattern.format(city=city)}"
                
                try:
                    # Test URL accessibility
                    response = requests.head(url, timeout=10)
                    status = response.status_code
                    
                    url_analysis[prop_type][city] = {
                        'url': url,
                        'status_code': status,
                        'accessible': status == 200
                    }
                    
                    print(f"   {city}: {status} {'✅' if status == 200 else '❌'}")
                    
                except Exception as e:
                    url_analysis[prop_type][city] = {
                        'url': url,
                        'status_code': None,
                        'accessible': False,
                        'error': str(e)
                    }
                    print(f"   {city}: Error - {str(e)[:50]}...")
                
                time.sleep(1)  # Rate limiting
        
        self.research_data['url_patterns'] = url_analysis
        return url_analysis
    
    def analyze_property_listing_page(self, url, property_type, city):
        """Comprehensive analysis of property listing page"""
        print(f"\n🏠 Analyzing {property_type} in {city}")
        print(f"   URL: {url}")
        
        try:
            self.driver.get(url)
            self.random_delay(3, 6)
            
            # Scroll to load dynamic content
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.random_delay(2, 4)
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'url': url,
                'property_type': property_type,
                'city': city,
                'page_title': soup.title.string if soup.title else None,
                'total_results': self._extract_total_results(soup),
                'property_cards': self._analyze_property_cards(soup),
                'pagination': self._analyze_pagination(soup),
                'filters': self._analyze_filters(soup),
                'data_fields': self._extract_all_data_fields(soup),
                'anti_scraping_indicators': self._detect_anti_scraping(soup)
            }
            
            print(f"   ✅ Found {len(analysis['property_cards'])} property cards")
            print(f"   📊 Total results: {analysis['total_results']}")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing page: {str(e)}")
            return {'error': str(e), 'url': url}
    
    def _extract_total_results(self, soup):
        """Extract total number of results from page"""
        # Look for result count patterns
        patterns = [
            r'(\d+(?:,\d+)*)\s*results',
            r'(\d+(?:,\d+)*)\s*properties',
            r'showing\s*(\d+(?:,\d+)*)',
        ]
        
        page_text = soup.get_text()
        for pattern in patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(1).replace(',', '')
        
        return None
    
    def _analyze_property_cards(self, soup):
        """Analyze property cards structure"""
        # Find property links to identify cards
        property_links = soup.find_all('a', href=True)
        property_cards = []
        
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                # Find the container for this property
                current = link
                for _ in range(20):
                    if current is None:
                        break
                    
                    text = current.get_text().lower()
                    if ('₹' in text and 'sqft' in text and 'bhk' in text):
                        if current not in property_cards:
                            property_cards.append(current)
                        break
                    current = current.parent
        
        # Analyze structure of first few cards
        card_analysis = []
        for i, card in enumerate(property_cards[:5]):
            card_data = self._analyze_single_card(card, i)
            card_analysis.append(card_data)
        
        return card_analysis
    
    def _analyze_single_card(self, card, index):
        """Detailed analysis of a single property card"""
        card_text = card.get_text()
        
        analysis = {
            'index': index,
            'text_length': len(card_text),
            'html_structure': self._get_html_structure(card),
            'data_fields': self._extract_card_fields(card),
            'links': self._extract_card_links(card),
            'images': self._extract_card_images(card),
            'property_type': self._classify_property_type(card_text)
        }
        
        return analysis
    
    def _extract_card_fields(self, card):
        """Extract all possible data fields from a card"""
        card_text = card.get_text()
        fields = {}
        
        # Price patterns
        price_patterns = {
            'main_price_cr': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)',
            'main_price_lakh': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)',
            'price_range_cr': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr',
            'price_range_lakh': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh',
            'price_per_sqft': r'₹\s*([\d,]+)\s*/sqft'
        }
        
        for field, pattern in price_patterns.items():
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                fields[field] = matches
        
        # Area patterns
        area_patterns = {
            'carpet_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Carpet\s*Area',
            'buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Built-up\s*Area',
            'super_buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Super\s*Built-up\s*Area',
            'general_area': r'(\d+(?:,\d+)*)\s*sqft'
        }
        
        for field, pattern in area_patterns.items():
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                fields[field] = matches
        
        # Configuration patterns
        config_patterns = {
            'bhk': r'(\d+)\s*bhk',
            'bedrooms': r'(\d+)\s*bedroom',
            'bathrooms': r'(\d+)\s*bath',
            'balconies': r'(\d+)\s*balcon'
        }
        
        for field, pattern in config_patterns.items():
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                fields[field] = matches
        
        # Status patterns
        status_patterns = {
            'construction_status': r'(Ready To Move|Under Construction|New Launch|Partially Ready)',
            'property_age': r'(\d+)\s*years?\s*old',
            'possession': r'Possession\s*in\s*([^,\n]+)',
            'completion': r'Completion\s*in\s*([^,\n]+)'
        }
        
        for field, pattern in status_patterns.items():
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                fields[field] = matches
        
        return fields
    
    def _extract_card_links(self, card):
        """Extract all links from a card"""
        links = []
        for link in card.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text(strip=True)
            links.append({'href': href, 'text': text})
        return links
    
    def _extract_card_images(self, card):
        """Extract all images from a card"""
        images = []
        for img in card.find_all('img'):
            src = img.get('src') or img.get('data-src')
            alt = img.get('alt', '')
            if src:
                images.append({'src': src, 'alt': alt})
        return images
    
    def _classify_property_type(self, card_text):
        """Classify the type of property based on card content"""
        text_lower = card_text.lower()
        
        if 'new booking' in text_lower or 'new launch' in text_lower:
            return 'new_launch'
        elif 'resale' in text_lower:
            return 'resale'
        elif 'under construction' in text_lower:
            return 'under_construction'
        elif 'ready to move' in text_lower:
            return 'ready_to_move'
        else:
            return 'unknown'
    
    def _get_html_structure(self, element):
        """Get simplified HTML structure"""
        structure = {
            'tag': element.name,
            'classes': element.get('class', []),
            'id': element.get('id'),
            'children_count': len(element.find_all()),
            'direct_children': [child.name for child in element.children if hasattr(child, 'name') and child.name]
        }
        return structure
    
    def _analyze_pagination(self, soup):
        """Analyze pagination structure"""
        pagination_info = {}
        
        # Look for pagination elements
        pagination_selectors = [
            '.pagination', '.pager', '[class*="pagination"]', 
            '[class*="pager"]', 'nav', '.page-numbers'
        ]
        
        for selector in pagination_selectors:
            elements = soup.select(selector)
            if elements:
                pagination_info[selector] = len(elements)
        
        # Look for next/previous buttons
        next_prev_patterns = [
            'next', 'previous', 'prev', 'forward', 'back'
        ]
        
        for pattern in next_prev_patterns:
            elements = soup.find_all(text=re.compile(pattern, re.IGNORECASE))
            if elements:
                pagination_info[f'{pattern}_buttons'] = len(elements)
        
        return pagination_info
    
    def _analyze_filters(self, soup):
        """Analyze available filters on the page"""
        filters = {}
        
        # Look for filter sections
        filter_keywords = [
            'budget', 'type', 'bedrooms', 'area', 'localities', 
            'amenities', 'furnishing', 'posted by', 'construction status'
        ]
        
        page_text = soup.get_text().lower()
        for keyword in filter_keywords:
            if keyword in page_text:
                filters[keyword] = True
        
        # Count filter options
        filter_elements = soup.find_all(['select', 'input', 'button'])
        filters['total_filter_elements'] = len(filter_elements)
        
        return filters
    
    def _extract_all_data_fields(self, soup):
        """Extract all possible data fields from the page"""
        page_text = soup.get_text()
        
        # Look for structured data (JSON-LD)
        json_scripts = soup.find_all('script', type='application/ld+json')
        structured_data = []
        
        for script in json_scripts:
            try:
                data = json.loads(script.string)
                structured_data.append(data)
            except:
                pass
        
        # Extract unique field names from structured data
        field_names = set()
        for data in structured_data:
            if isinstance(data, dict):
                field_names.update(self._extract_keys_recursive(data))
        
        return {
            'structured_data_count': len(structured_data),
            'unique_fields': list(field_names),
            'field_count': len(field_names)
        }
    
    def _extract_keys_recursive(self, obj, prefix=''):
        """Recursively extract all keys from nested dictionary"""
        keys = set()
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                full_key = f"{prefix}.{key}" if prefix else key
                keys.add(full_key)
                keys.update(self._extract_keys_recursive(value, full_key))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                keys.update(self._extract_keys_recursive(item, f"{prefix}[{i}]"))
        
        return keys
    
    def _detect_anti_scraping(self, soup):
        """Detect anti-scraping mechanisms"""
        indicators = {}
        
        # Check for common anti-scraping patterns
        page_text = soup.get_text().lower()
        
        anti_scraping_keywords = [
            'captcha', 'robot', 'bot', 'automated', 'suspicious activity',
            'rate limit', 'too many requests', 'blocked'
        ]
        
        for keyword in anti_scraping_keywords:
            if keyword in page_text:
                indicators[keyword] = True
        
        # Check for JavaScript challenges
        scripts = soup.find_all('script')
        indicators['script_count'] = len(scripts)
        indicators['has_external_scripts'] = any('src' in script.attrs for script in scripts)
        
        return indicators
    
    def run_comprehensive_research(self):
        """Run complete comprehensive research"""
        print("🚀 STARTING COMPREHENSIVE 99ACRES RESEARCH")
        print("="*80)
        print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Objective: Deep analysis of all property types and mechanisms")
        
        try:
            self.setup_driver()
            
            # Phase 1: URL Pattern Analysis
            url_analysis = self.analyze_url_patterns()
            
            # Phase 2: Detailed Page Analysis
            print(f"\n🔍 PHASE 2: Detailed Page Analysis")
            print("="*60)
            
            detailed_analysis = []
            
            # Analyze key property types and cities
            priority_combinations = [
                ('residential_sale', 'mumbai'),
                ('residential_sale', 'delhi'),
                ('residential_sale', 'bangalore'),
                ('residential_rent', 'mumbai'),
                ('residential_rent', 'delhi'),
                ('commercial_sale', 'mumbai')
            ]
            
            for prop_type, city in priority_combinations:
                if prop_type in url_analysis and city in url_analysis[prop_type]:
                    if url_analysis[prop_type][city]['accessible']:
                        url = url_analysis[prop_type][city]['url']
                        analysis = self.analyze_property_listing_page(url, prop_type, city)
                        detailed_analysis.append(analysis)
                        
                        self.random_delay(5, 8)  # Longer delay between pages
            
            self.research_data['detailed_analysis'] = detailed_analysis
            
            # Save comprehensive research data
            self.save_research_data()
            
            print(f"\n✅ COMPREHENSIVE RESEARCH COMPLETED!")
            print(f"📊 Analyzed {len(detailed_analysis)} property listing pages")
            print(f"💾 Research data saved to: data/comprehensive_99acres_research.json")
            
        except Exception as e:
            print(f"❌ Research failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def save_research_data(self):
        """Save comprehensive research data"""
        os.makedirs('data', exist_ok=True)
        
        # Save main research data
        with open('data/comprehensive_99acres_research.json', 'w', encoding='utf-8') as f:
            json.dump(self.research_data, f, indent=2, ensure_ascii=False)
        
        # Save summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Generate human-readable summary report"""
        report = []
        report.append("# 99acres Comprehensive Research Summary")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # URL Analysis Summary
        if 'url_patterns' in self.research_data:
            report.append("## URL Pattern Analysis")
            for prop_type, cities in self.research_data['url_patterns'].items():
                accessible_count = sum(1 for city_data in cities.values() if city_data.get('accessible', False))
                total_count = len(cities)
                report.append(f"- {prop_type}: {accessible_count}/{total_count} cities accessible")
            report.append("")
        
        # Detailed Analysis Summary
        if 'detailed_analysis' in self.research_data:
            report.append("## Detailed Page Analysis")
            for analysis in self.research_data['detailed_analysis']:
                if 'error' not in analysis:
                    prop_type = analysis.get('property_type', 'Unknown')
                    city = analysis.get('city', 'Unknown')
                    cards = len(analysis.get('property_cards', []))
                    total_results = analysis.get('total_results', 'Unknown')
                    report.append(f"- {prop_type} in {city}: {cards} cards analyzed, {total_results} total results")
            report.append("")
        
        # Save report
        with open('data/comprehensive_research_summary.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))


if __name__ == "__main__":
    researcher = Comprehensive99acresResearcher()
    researcher.run_comprehensive_research()
