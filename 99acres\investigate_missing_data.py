#!/usr/bin/env python3
"""
Investigate Missing Data
Find out where the 2000+ properties went
"""

import sqlite3
import os

def investigate_databases():
    """Investigate all databases to find missing data"""
    print("🔍 INVESTIGATING MISSING 2000+ PROPERTIES")
    print("=" * 60)
    
    # Check all database files
    db_files = [
        'data/99acres_properties.db',
        'data/99acres_properties_backup.db', 
        'data/99acres_properties_backup_20250810_181646.db',
        'data/individual_properties_comprehensive.db',
        'data/optimized_individual_properties.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"\n📊 ANALYZING: {db_file}")
            print("-" * 40)
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get table names
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"Tables: {tables}")
                
                for table in tables:
                    if table != 'sqlite_sequence':
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"  {table}: {count} records")
                        
                        # For properties table, get more details
                        if table == 'properties':
                            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NOT NULL AND property_url != ''")
                            url_count = cursor.fetchone()[0]
                            print(f"    - With URLs: {url_count}")
                            
                            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE '%httpswww%'")
                            malformed = cursor.fetchone()[0]
                            print(f"    - Malformed URLs: {malformed}")
                            
                            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NOT NULL AND property_url != '' AND property_url NOT LIKE '%httpswww%'")
                            valid_urls = cursor.fetchone()[0]
                            print(f"    - Valid URLs: {valid_urls}")
                            
                            # Check cities
                            cursor.execute("SELECT city, COUNT(*) FROM properties GROUP BY city ORDER BY COUNT(*) DESC LIMIT 5")
                            cities = cursor.fetchall()
                            print(f"    - Top cities:")
                            for city, city_count in cities:
                                print(f"      {city}: {city_count}")
                
                conn.close()
                
            except Exception as e:
                print(f"Error analyzing {db_file}: {str(e)}")
        else:
            print(f"\n❌ NOT FOUND: {db_file}")
    
    # Check for other potential database files
    print(f"\n🔍 SEARCHING FOR OTHER DATABASE FILES")
    print("-" * 40)
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db') and 'data/' not in root:
                full_path = os.path.join(root, file)
                print(f"Found: {full_path}")
                
                try:
                    conn = sqlite3.connect(full_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    for table in tables:
                        if table != 'sqlite_sequence':
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            print(f"  {table}: {count} records")
                    
                    conn.close()
                except:
                    print(f"  Could not read {full_path}")

def check_scraping_sessions():
    """Check scraping sessions to understand data collection"""
    print(f"\n📈 CHECKING SCRAPING SESSIONS")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('data/99acres_properties.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM scraping_sessions ORDER BY start_time DESC LIMIT 10")
        sessions = cursor.fetchall()
        
        if sessions:
            print("Recent scraping sessions:")
            for session in sessions:
                print(f"  {session}")
        else:
            print("No scraping sessions found")
        
        conn.close()
    except Exception as e:
        print(f"Error checking sessions: {str(e)}")

def main():
    """Main investigation function"""
    investigate_databases()
    check_scraping_sessions()
    
    print(f"\n🎯 SUMMARY & RECOMMENDATIONS")
    print("=" * 60)
    print("1. Check if data was moved to backup databases")
    print("2. Verify if scraping sessions were properly recorded")
    print("3. Look for data in other database files")
    print("4. Check if filtering logic is too restrictive")

if __name__ == "__main__":
    main()
