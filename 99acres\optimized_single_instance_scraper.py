#!/usr/bin/env python3
"""
Optimized Single Instance Scraper for 99acres
Tests optimizations without parallel processing complexity
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import time
import sqlite3
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
import random
import tempfile

class OptimizedSingleScraper:
    """Single instance optimized scraper for testing"""
    
    def __init__(self):
        self.driver = None
        self.database_path = 'data/optimized_single_test.db'
        self.scraped_properties = []
        self.start_time = datetime.now()
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize test database"""
        os.makedirs('data', exist_ok=True)
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_properties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                property_url TEXT UNIQUE,
                title TEXT,
                price_crores REAL,
                bhk_config TEXT,
                area_sqft INTEGER,
                city TEXT,
                extraction_time REAL,
                scraped_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Test database initialized")
    
    def setup_optimized_driver(self):
        """Setup optimized Chrome driver"""
        options = Options()
        
        # Performance optimizations
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")
        options.add_argument("--disable-css")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        
        # Memory optimizations
        options.add_argument("--memory-pressure-off")
        options.add_argument("--max_old_space_size=2048")
        
        # Anti-detection
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        ]
        options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        # Unique temp directory
        temp_dir = tempfile.mkdtemp(prefix="scraper_test_")
        options.add_argument(f"--user-data-dir={temp_dir}")
        
        # Disable logging
        options.add_argument("--log-level=3")
        options.add_argument("--silent")
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.driver.set_page_load_timeout(10)
            self.driver.implicitly_wait(5)
            print("✅ Optimized driver initialized")
            return True
        except Exception as e:
            print(f"❌ Driver setup failed: {str(e)}")
            return False
    
    def extract_optimized_data(self, url):
        """Extract data with optimized patterns"""
        start_time = time.time()
        
        try:
            # Navigate to page
            self.driver.get(url)
            
            # Quick wait for essential content
            try:
                WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                pass
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            page_text = soup.get_text()
            
            # Extract essential fields only
            data = {'property_url': url}
            
            # Title
            title_tag = soup.find('title')
            if title_tag:
                data['title'] = title_tag.get_text(strip=True)
            
            # Price (crores only for speed)
            price_match = re.search(r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr(?:ore)?', page_text, re.IGNORECASE)
            if price_match:
                try:
                    data['price_crores'] = float(price_match.group(1).replace(',', ''))
                except:
                    pass
            
            # BHK
            bhk_match = re.search(r'(\d+)\s*BHK', page_text, re.IGNORECASE)
            if bhk_match:
                data['bhk_config'] = bhk_match.group(1) + ' BHK'
            
            # Area
            area_match = re.search(r'(\d+)\s*sqft', page_text, re.IGNORECASE)
            if area_match:
                try:
                    data['area_sqft'] = int(area_match.group(1))
                except:
                    pass
            
            # City from URL
            url_parts = url.split('-')
            for part in url_parts:
                if part.lower() in ['mumbai', 'delhi', 'bangalore', 'pune', 'chennai', 'hyderabad', 'kolkata']:
                    data['city'] = part.title()
                    break
            
            # Add extraction time
            data['extraction_time'] = time.time() - start_time
            data['scraped_timestamp'] = datetime.now().isoformat()
            
            return data
            
        except Exception as e:
            print(f"   ❌ Extraction failed: {str(e)}")
            return None
    
    def save_to_database(self, property_data):
        """Save property to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO test_properties 
                (property_url, title, price_crores, bhk_config, area_sqft, city, extraction_time, scraped_timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                property_data.get('property_url'),
                property_data.get('title'),
                property_data.get('price_crores'),
                property_data.get('bhk_config'),
                property_data.get('area_sqft'),
                property_data.get('city'),
                property_data.get('extraction_time'),
                property_data.get('scraped_timestamp')
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"   ❌ Database save failed: {str(e)}")
            return False
    
    def intelligent_delay(self):
        """Smart delay with randomization"""
        # Much shorter delays for testing
        delay = random.uniform(1.0, 2.0)
        time.sleep(delay)
    
    def test_optimization(self, max_properties=20):
        """Test optimized scraping on sample properties"""
        print("🚀 Testing Optimized Single Instance Scraper")
        print("=" * 50)
        
        try:
            # Load property URLs
            main_db_path = 'data/99acres_properties.db'
            conn = sqlite3.connect(main_db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT property_url 
                FROM properties 
                WHERE property_url IS NOT NULL AND property_url != ''
                AND property_url NOT LIKE '%httpswww%'
                ORDER BY RANDOM() LIMIT ?
            """, (max_properties,))
            
            urls = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            print(f"📋 Loaded {len(urls)} test URLs")
            
            if not urls:
                print("❌ No URLs found")
                return
            
            # Setup driver
            if not self.setup_optimized_driver():
                return
            
            # Test scraping
            successful = 0
            failed = 0
            total_extraction_time = 0
            
            for i, url in enumerate(urls, 1):
                print(f"\n📍 Testing {i}/{len(urls)}")
                
                # Fix URL format
                if url.startswith('http'):
                    fixed_url = url
                elif url.startswith('www.'):
                    fixed_url = f"https://{url}"
                else:
                    fixed_url = f"https://www.99acres.com/{url}"
                
                print(f"   URL: {fixed_url}")
                
                # Extract data
                property_data = self.extract_optimized_data(fixed_url)
                
                if property_data:
                    # Save to database
                    if self.save_to_database(property_data):
                        successful += 1
                        extraction_time = property_data.get('extraction_time', 0)
                        total_extraction_time += extraction_time
                        
                        print(f"   ✅ SUCCESS: {extraction_time:.1f}s")
                        print(f"      Title: {property_data.get('title', 'N/A')[:50]}...")
                        print(f"      Price: ₹{property_data.get('price_crores', 'N/A')} Cr")
                        print(f"      BHK: {property_data.get('bhk_config', 'N/A')}")
                        
                        self.scraped_properties.append(property_data)
                    else:
                        failed += 1
                        print(f"   ⚠️ Database save failed")
                else:
                    failed += 1
                    print(f"   ❌ FAILED: No data extracted")
                
                # Intelligent delay
                self.intelligent_delay()
            
            # Calculate performance metrics
            total_time = (datetime.now() - self.start_time).total_seconds()
            success_rate = (successful / len(urls)) * 100
            avg_extraction_time = total_extraction_time / successful if successful > 0 else 0
            avg_total_time = total_time / len(urls)
            
            # Results
            print(f"\n" + "=" * 50)
            print(f"🎉 OPTIMIZATION TEST COMPLETE!")
            print(f"=" * 50)
            print(f"📊 Properties Tested: {len(urls)}")
            print(f"✅ Successful: {successful}")
            print(f"❌ Failed: {failed}")
            print(f"📈 Success Rate: {success_rate:.1f}%")
            print(f"⚡ Avg Extraction Time: {avg_extraction_time:.1f}s")
            print(f"🕒 Avg Total Time: {avg_total_time:.1f}s")
            print(f"🔥 Speed vs Original: {9.5 / avg_total_time:.1f}x faster")
            
            # Save test report
            test_report = {
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'properties_tested': len(urls),
                    'successful_extractions': successful,
                    'failed_extractions': failed,
                    'success_rate_percentage': success_rate,
                    'avg_extraction_time': avg_extraction_time,
                    'avg_total_time': avg_total_time,
                    'speed_improvement': f"{9.5 / avg_total_time:.1f}x" if avg_total_time > 0 else "N/A"
                },
                'optimization_features': [
                    "Headless browser",
                    "Disabled images/CSS",
                    "Memory optimization",
                    "Faster timeouts",
                    "Essential fields only",
                    "Smart delays"
                ],
                'sample_properties': self.scraped_properties[:5]
            }
            
            with open('optimization_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(test_report, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Test report saved: optimization_test_report.json")
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """Main test function"""
    scraper = OptimizedSingleScraper()
    scraper.test_optimization(max_properties=20)

if __name__ == "__main__":
    main()
