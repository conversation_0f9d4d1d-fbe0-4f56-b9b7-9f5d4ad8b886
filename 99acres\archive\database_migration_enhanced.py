#!/usr/bin/env python3
"""
Enhanced Database Migration for 99acres Scraper
Migrates existing database to support all enhanced fields from deep research
"""

import sqlite3
import os
import shutil
from datetime import datetime

class DatabaseMigration:
    """Handle database schema migration to enhanced version"""
    
    def __init__(self, database_path='data/99acres_properties.db'):
        self.database_path = database_path
        self.backup_path = database_path.replace('.db', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
        
    def backup_database(self):
        """Create backup of existing database"""
        if os.path.exists(self.database_path):
            shutil.copy2(self.database_path, self.backup_path)
            print(f"✅ Database backed up to: {self.backup_path}")
            return True
        return False
    
    def get_existing_columns(self):
        """Get list of existing columns in properties table"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(properties)")
            columns = [row[1] for row in cursor.fetchall()]
            conn.close()
            return columns
        except:
            return []
    
    def migrate_schema(self):
        """Migrate database schema to enhanced version"""
        print("🔄 Starting database schema migration...")
        
        # Backup existing database
        if not self.backup_database():
            print("ℹ️ No existing database found, will create new schema")
        
        # Get existing columns
        existing_columns = self.get_existing_columns()
        print(f"📊 Found {len(existing_columns)} existing columns")
        
        # Define all enhanced fields from deep research
        enhanced_fields = {
            # Enhanced Price Information
            'min_price': 'TEXT',
            'max_price': 'TEXT', 
            'price_unit': 'TEXT',
            'price_negotiable': 'TEXT',
            'maintenance_charges': 'TEXT',
            
            # Enhanced Area Information
            'area_range': 'TEXT',
            'area_type': 'TEXT',
            'land_area': 'TEXT',
            'covered_area': 'TEXT',
            
            # Enhanced Location Information
            'sub_locality': 'TEXT',
            'area_code': 'TEXT',
            'address_line1': 'TEXT',
            'address_line2': 'TEXT',
            'landmark': 'TEXT',
            'distance_from_metro': 'TEXT',
            'connectivity_info': 'TEXT',
            
            # Enhanced Amenities (Categorized)
            'amenities_basic': 'TEXT',
            'amenities_recreational': 'TEXT',
            'amenities_sports': 'TEXT',
            'amenities_convenience': 'TEXT',
            'amenities_technology': 'TEXT',
            'amenities_safety': 'TEXT',
            'property_highlights': 'TEXT',
            'unique_selling_points': 'TEXT',
            
            # Enhanced Quality and Validation
            'field_completeness_score': 'INTEGER DEFAULT 0',
            'validation_status': 'TEXT',
            'extraction_method': 'TEXT',
            'structured_data_available': 'TEXT',
            
            # Research Insights
            'container_class': 'TEXT',
            'extraction_confidence': 'INTEGER DEFAULT 0',
            'city_validated': 'TEXT',
            'property_type_validated': 'TEXT',
            
            # Enhanced Metadata
            'last_updated': 'TEXT',
            'scraping_session_id': 'TEXT',
            'configuration_details': 'TEXT',
            'floor_plan_details': 'TEXT'
        }
        
        # Add missing columns
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        added_columns = 0
        for column_name, column_type in enhanced_fields.items():
            if column_name not in existing_columns:
                try:
                    alter_query = f"ALTER TABLE properties ADD COLUMN {column_name} {column_type}"
                    cursor.execute(alter_query)
                    added_columns += 1
                    print(f"   ✅ Added column: {column_name}")
                except Exception as e:
                    print(f"   ⚠️ Could not add column {column_name}: {str(e)}")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_city ON properties(city)",
            "CREATE INDEX IF NOT EXISTS idx_property_type ON properties(property_type)",
            "CREATE INDEX IF NOT EXISTS idx_price_range ON properties(min_price, max_price)",
            "CREATE INDEX IF NOT EXISTS idx_area ON properties(area)",
            "CREATE INDEX IF NOT EXISTS idx_locality ON properties(locality)",
            "CREATE INDEX IF NOT EXISTS idx_scraped_at ON properties(scraped_at)",
            "CREATE INDEX IF NOT EXISTS idx_data_quality ON properties(data_quality_score)",
            "CREATE INDEX IF NOT EXISTS idx_property_url ON properties(property_url)"
        ]
        
        for index_query in indexes:
            try:
                cursor.execute(index_query)
                print(f"   ✅ Created index")
            except Exception as e:
                print(f"   ⚠️ Index creation warning: {str(e)}")
        
        conn.commit()
        conn.close()
        
        print(f"✅ Migration completed! Added {added_columns} new columns")
        return True
    
    def validate_migration(self):
        """Validate the migration was successful"""
        print("🔍 Validating migration...")
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Check table structure
            cursor.execute("PRAGMA table_info(properties)")
            columns = cursor.fetchall()
            print(f"✅ Properties table has {len(columns)} columns")
            
            # Check if we can insert a test record
            test_data = {
                'title': 'Migration Test Property',
                'price': '₹1 Cr',
                'area': '1000 sqft',
                'city': 'Mumbai',
                'min_price': '₹1 Cr',
                'max_price': '₹1 Cr',
                'extraction_method': 'migration_test',
                'data_quality_score': 100,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Test insertion
            cursor.execute("""
                INSERT INTO properties (title, price, area, city, min_price, max_price, 
                                      extraction_method, data_quality_score, scraped_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_data['title'], test_data['price'], test_data['area'], 
                test_data['city'], test_data['min_price'], test_data['max_price'],
                test_data['extraction_method'], test_data['data_quality_score'], 
                test_data['scraped_at']
            ))
            
            # Remove test record
            cursor.execute("DELETE FROM properties WHERE title = ?", (test_data['title'],))
            
            conn.commit()
            conn.close()
            
            print("✅ Migration validation successful!")
            return True
            
        except Exception as e:
            print(f"❌ Migration validation failed: {str(e)}")
            return False
    
    def run_migration(self):
        """Run complete migration process"""
        print("🚀 Starting Enhanced Database Migration")
        print("="*60)
        
        try:
            # Step 1: Migrate schema
            if self.migrate_schema():
                print("✅ Schema migration completed")
            else:
                print("❌ Schema migration failed")
                return False
            
            # Step 2: Validate migration
            if self.validate_migration():
                print("✅ Migration validation passed")
            else:
                print("❌ Migration validation failed")
                return False
            
            print("\n🎉 Enhanced Database Migration Completed Successfully!")
            print(f"📦 Backup available at: {self.backup_path}")
            print("🔧 Database now supports all enhanced fields from deep research")
            
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            return False

def main():
    """Main migration function"""
    migration = DatabaseMigration()
    success = migration.run_migration()
    
    if success:
        print("\n✅ Ready to use enhanced scraper with all validated fields!")
    else:
        print("\n❌ Migration failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
