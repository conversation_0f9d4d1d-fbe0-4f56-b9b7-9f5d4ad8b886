# 99acres Website Structure Research Findings

## Overview
Comprehensive analysis of 99acres.com website structure for building a property scraper similar to the MagicBricks scraper.

**Research Date**: 2025-08-10  
**Target URL**: https://www.99acres.com/property-for-sale-in-mumbai-ffid  
**Total Properties Found**: 75,489 results for Mumbai

## URL Patterns

### Property Listing URLs
- **Sale Properties**: `https://www.99acres.com/property-for-sale-in-{city}-ffid`
- **Rent Properties**: `https://www.99acres.com/property-for-rent-in-{city}-ffid`
- **Pagination**: Likely uses page parameters (needs further investigation)

### Individual Property URLs
- **Pattern**: `https://www.99acres.com/{property-details}-spid-{property_id}`
- **Example**: `https://www.99acres.com/4-bhk-bedroom-apartment-flat-for-sale-in-palm-beach-navi-mumbai-4825-sq-ft-spid-F82432092`

### Project URLs (New Launches)
- **Pattern**: `https://www.99acres.com/{project-name}-{location}-npxid-{project_id}`
- **Example**: `https://www.99acres.com/godrej-horizon-wadala-south-mumbai-npxid-r389975`

## Property Card Structure

### Main Container
- **Property Cards**: Each property is contained in a `generic` element with nested structure
- **Two Types**: 
  1. Individual property listings (resale/rent)
  2. New project listings (builder projects)

### Key Data Fields Identified

#### 1. Property Title & Type
- **Location**: Nested in heading elements (h2)
- **Format**: "{BHK} {Type} in {Location}"
- **Example**: "4 BHK Flat in Palm Beach, Sanpada"

#### 2. Price Information
- **Main Price**: Format "₹{amount} Cr" or "₹{amount} Lakh"
- **Price per sqft**: Format "₹{amount} /sqft"
- **Price Range**: For projects "₹{min} - {max} Cr"

#### 3. Area Details
- **Built-up Area**: Format "{area} sqft ({sqm} sqm)"
- **Carpet Area**: For some properties
- **Area Type**: "Built-up Area" or "Carpet Area"

#### 4. Property Configuration
- **BHK**: "{number} BHK ({number} Baths)"
- **Bedrooms**: Extracted from BHK
- **Bathrooms**: Extracted from bath count

#### 5. Construction Status
- **Status Types**:
  - "Ready To Move"
  - "Under Construction"
  - "New Launch"
  - "Partially Ready To Move"
- **Completion Date**: "Completion in {month}, {year}"

#### 6. Location Information
- **Society/Project Name**: Main project title
- **Locality**: Extracted from title
- **City**: From URL and title
- **Nearby Amenities**: "Nearby:" section with facilities

#### 7. Builder/Agent Information
- **Builder**: For new projects
- **Agent Type**: "FEATURED DEALER", "Owner", "Builder", "Dealer"
- **Agent Name**: Company/person name
- **Posted Time**: "3w ago", "4d ago", etc.

#### 8. Additional Features
- **RERA Registration**: RERA numbers for approved projects
- **Amenities**: Various tags and features
- **Images**: Property images and videos
- **Highlights**: Special features like "East Facing"

#### 9. Contact Information
- **Contact Buttons**: "View Number", "Contact"
- **Brochure**: Download links for projects

## CSS Selectors Analysis

### Property Cards
- **Individual Properties**: Nested `generic` elements with specific structure
- **Project Properties**: Similar structure but with different content layout

### Key Selectors (Approximate)
```css
/* Property card containers */
generic[ref^="e"] > generic[ref^="e"] > generic[ref^="e"]

/* Property titles */
heading[level="2"]

/* Price information */
generic:contains("₹")

/* Area information */
generic:contains("sqft")

/* BHK information */
generic:contains("BHK")

/* Contact buttons */
button:contains("View Number")
button:contains("Contact")

/* Property links */
link[href*="spid-"]
link[href*="npxid-"]
```

## Data Extraction Strategy

### 1. Property Card Identification
- Look for nested generic elements containing property information
- Identify by presence of price, area, and BHK information
- Distinguish between individual properties and projects

### 2. Data Field Extraction
- **Title**: Extract from h2 heading elements
- **Price**: Parse from text containing "₹" and "Cr"/"Lakh"
- **Area**: Extract numbers followed by "sqft"
- **BHK**: Parse from text containing "BHK"
- **Location**: Extract from title and nearby sections
- **Status**: Look for construction status indicators

### 3. Link Extraction
- **Property URLs**: Extract href from links containing "spid-"
- **Project URLs**: Extract href from links containing "npxid-"
- **Images**: Extract from img elements

## Pagination Analysis

### Current Page Structure
- Shows "75489 results" for Mumbai
- Likely uses pagination for large result sets
- Need to investigate pagination controls and URL parameters

### Pagination Strategy
- Look for pagination controls at bottom of page
- Identify next/previous buttons
- Determine URL parameter structure for page navigation

## Anti-Detection Considerations

### Website Characteristics
- Modern React-based application
- Dynamic content loading
- Extensive JavaScript usage
- Tracking and analytics present

### Recommended Measures
- Random delays between requests
- Rotate user agents
- Use realistic browser headers
- Implement session management
- Respect rate limits

## Comparison with MagicBricks

### Similarities
- Similar property data fields
- Comparable URL structure
- Similar pagination approach
- Same data types (price, area, BHK, etc.)

### Differences
- Different CSS class names and structure
- Different URL patterns (ffid vs pppfs)
- More emphasis on new projects
- Different agent/builder information layout

## Implementation Recommendations

### 1. Scraper Architecture
- Use same modular approach as MagicBricks scraper
- Implement similar error handling and retry logic
- Maintain same database schema for compatibility

### 2. Data Extraction
- Adapt CSS selectors for 99acres structure
- Implement robust text parsing for price and area
- Handle both individual properties and projects

### 3. URL Generation
- Modify city URL patterns for 99acres format
- Implement pagination handling
- Support both sale and rent property types

### 4. Quality Assurance
- Implement data validation
- Add duplicate detection
- Ensure consistent data formatting

## Next Steps

1. **Task 1.2**: Implement CSS selector identification
2. **Task 1.3**: Create project structure and base scraper
3. **Task 2.1**: Develop core data extraction logic
4. **Task 2.2**: Add pagination support
5. **Task 2.3**: Implement anti-detection measures

---
**Research Status**: ✅ COMPLETE  
**Next Phase**: CSS Selector Implementation
