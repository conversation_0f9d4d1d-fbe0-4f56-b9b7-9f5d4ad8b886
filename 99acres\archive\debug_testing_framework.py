#!/usr/bin/env python3
"""
Debug the testing framework issue
"""

from multi_city_testing_framework import MultiCityTestingFramework

def debug_testing_framework():
    """Debug what's happening in the testing framework"""
    framework = MultiCityTestingFramework()
    
    city = 'bangalore'
    property_type = 'property-for-rent-in-{city}-ffid'
    url = f"https://www.99acres.com/{property_type.format(city=city)}"
    
    print(f"🔍 Debugging Testing Framework")
    print(f"URL: {url}")
    
    # Test the combination directly
    result = framework._test_city_property_combination(city, property_type, url, 1)
    
    print(f"\n📊 Test Result:")
    print(f"Success: {result['success']}")
    print(f"Properties extracted: {result['properties_extracted']}")
    print(f"Average quality score: {result['average_quality_score']}")
    print(f"Extraction rates: {result['extraction_rates']}")
    print(f"Errors: {result['errors']}")
    
    if result['properties_extracted'] > 0 and not result['success']:
        print(f"\n🔍 Validation Analysis:")
        print(f"Min properties required: {framework.success_criteria['min_properties_per_page']}")
        print(f"Max properties allowed: {framework.success_criteria['max_properties_per_page']}")
        print(f"Min quality score required: {framework.success_criteria['min_data_quality_score']}")
        print(f"Min price extraction required: {framework.success_criteria['min_price_extraction_rate']}")
        print(f"Min area extraction required: {framework.success_criteria['min_area_extraction_rate']}")
        print(f"Min location extraction required: {framework.success_criteria['min_location_extraction_rate']}")
        
        # Check each criteria
        print(f"\n✅ Criteria Check:")
        print(f"Properties count: {result['properties_extracted']} >= {framework.success_criteria['min_properties_per_page']} = {result['properties_extracted'] >= framework.success_criteria['min_properties_per_page']}")
        print(f"Quality score: {result['average_quality_score']} >= {framework.success_criteria['min_data_quality_score']} = {result['average_quality_score'] >= framework.success_criteria['min_data_quality_score']}")
        
        extraction_rates = result['extraction_rates']
        print(f"Price extraction: {extraction_rates.get('price', 0)} >= {framework.success_criteria['min_price_extraction_rate']} = {extraction_rates.get('price', 0) >= framework.success_criteria['min_price_extraction_rate']}")
        print(f"Area extraction: {extraction_rates.get('area', 0)} >= {framework.success_criteria['min_area_extraction_rate']} = {extraction_rates.get('area', 0) >= framework.success_criteria['min_area_extraction_rate']}")
        print(f"Location extraction: {extraction_rates.get('locality', 0)} >= {framework.success_criteria['min_location_extraction_rate']} = {extraction_rates.get('locality', 0) >= framework.success_criteria['min_location_extraction_rate']}")

if __name__ == "__main__":
    debug_testing_framework()
