2025-08-10 13:22:04,726 - INFO - Chrome WebDriver initialized successfully
2025-08-10 13:22:17,034 - INFO - WebDriver closed
2025-08-10 13:22:21,106 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002155D8A47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9acb1ced38b2ab6b1ea07df843f188d0
2025-08-10 13:22:25,167 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002155D8A4910>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9acb1ced38b2ab6b1ea07df843f188d0
2025-08-10 13:22:29,249 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002155D80D350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9acb1ced38b2ab6b1ea07df843f188d0
2025-08-10 13:22:37,413 - INFO - WebDriver closed
2025-08-10 13:24:18,772 - INFO - Chrome WebDriver initialized successfully
2025-08-10 13:24:22,207 - INFO - WebDriver closed
2025-08-10 13:24:26,295 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153EA4F1810>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51e1366356e103a0395633fd64216642
2025-08-10 13:24:30,371 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153EA4F1310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51e1366356e103a0395633fd64216642
2025-08-10 13:24:34,464 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153EA459350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51e1366356e103a0395633fd64216642
2025-08-10 13:24:42,642 - INFO - WebDriver closed
2025-08-10 13:55:16,846 - INFO - Chrome WebDriver initialized successfully
2025-08-10 13:55:17,133 - INFO - Chrome WebDriver initialized successfully
2025-08-10 13:55:31,071 - INFO - WebDriver closed
2025-08-10 13:55:35,150 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013297522C10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f708fab3fac6571d4db989a884a2f1a0
2025-08-10 13:55:39,223 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013297519940>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f708fab3fac6571d4db989a884a2f1a0
2025-08-10 13:55:41,582 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974FB620>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d/element
2025-08-10 13:55:43,300 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013297519BA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f708fab3fac6571d4db989a884a2f1a0
2025-08-10 13:55:45,675 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132982CD490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d/element
2025-08-10 13:55:49,759 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F4C00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d/element
2025-08-10 13:55:51,449 - INFO - WebDriver closed
2025-08-10 13:55:57,899 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F5480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:01,972 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F56A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:06,041 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F58C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:14,202 - INFO - WebDriver closed
2025-08-10 13:56:18,280 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F5F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:22,363 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F6030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:26,456 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000132974F6250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c174b403821e64ecde770fb558e1c49d
2025-08-10 13:56:34,585 - INFO - WebDriver closed
2025-08-10 14:21:42,780 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:21:54,932 - INFO - WebDriver closed
2025-08-10 14:22:00,820 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:22:04,859 - INFO - WebDriver closed
2025-08-10 14:22:12,915 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:22:16,663 - INFO - WebDriver closed
2025-08-10 14:22:40,753 - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:22:40,754 - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:22:40,754 - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,754 - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:40,755 - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:22:40,755 - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:22:40,755 - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,756 - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:40,756 - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:22:40,756 - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:22:40,756 - DEBUG - Context: {'test': True}
2025-08-10 14:22:40,756 - DEBUG - Traceback: NoneType: None

2025-08-10 14:22:42,863 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:23:00,707 - INFO - WebDriver closed
2025-08-10 14:23:04,794 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62A93E650>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/294bf9d4c9ada3d57bb6da159519089a
2025-08-10 14:23:08,864 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62C14CDD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/294bf9d4c9ada3d57bb6da159519089a
2025-08-10 14:23:12,941 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62BD09150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/294bf9d4c9ada3d57bb6da159519089a
2025-08-10 14:23:21,085 - INFO - WebDriver closed
2025-08-10 14:23:23,728 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:23:39,120 - INFO - WebDriver closed
2025-08-10 14:23:43,203 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62A942360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b2c41040c07a34f0cfb171679c65b5d9
2025-08-10 14:23:47,277 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62BD099D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b2c41040c07a34f0cfb171679c65b5d9
2025-08-10 14:23:51,349 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62BD08D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b2c41040c07a34f0cfb171679c65b5d9
2025-08-10 14:23:59,513 - INFO - WebDriver closed
2025-08-10 14:24:01,230 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:24:23,711 - INFO - WebDriver closed
2025-08-10 14:24:27,789 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62A942360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8e1e9af43f4631d18799b88561fed139
2025-08-10 14:24:31,878 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62A86B460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8e1e9af43f4631d18799b88561fed139
2025-08-10 14:24:35,958 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C62BD0A250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8e1e9af43f4631d18799b88561fed139
2025-08-10 14:24:44,102 - INFO - WebDriver closed
2025-08-10 14:37:35,852 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:37:43,050 - INFO - WebDriver closed
2025-08-10 14:37:47,128 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001996FCDD450>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3a061d299d37256c82815b582173ef72
2025-08-10 14:37:51,219 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001996FCDD6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3a061d299d37256c82815b582173ef72
2025-08-10 14:37:55,314 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001996FC550F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3a061d299d37256c82815b582173ef72
2025-08-10 14:38:03,492 - INFO - WebDriver closed
2025-08-10 14:41:14,044 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:41:23,794 - INFO - WebDriver closed
2025-08-10 14:41:27,860 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B80DD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b06d2e229d33115f2af3ddef3e150cf4
2025-08-10 14:41:31,930 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B80E0D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b06d2e229d33115f2af3ddef3e150cf4
2025-08-10 14:41:35,994 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B7ADE00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b06d2e229d33115f2af3ddef3e150cf4
2025-08-10 14:41:44,091 - INFO - WebDriver closed
2025-08-10 14:41:46,207 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:41:50,625 - INFO - WebDriver closed
2025-08-10 14:41:58,477 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:42:06,543 - INFO - WebDriver closed
2025-08-10 14:42:10,610 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B7B1590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b4e33e1a4d86393cae8140409f7a23b5
2025-08-10 14:42:14,694 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B7B16A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b4e33e1a4d86393cae8140409f7a23b5
2025-08-10 14:42:18,766 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B7B1370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b4e33e1a4d86393cae8140409f7a23b5
2025-08-10 14:42:26,917 - INFO - WebDriver closed
2025-08-10 14:42:46,920 - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:42:46,921 - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:42:46,922 - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,922 - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:46,922 - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:42:46,922 - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:42:46,922 - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,922 - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:46,922 - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:42:46,923 - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:42:46,923 - DEBUG - Context: {'test': True}
2025-08-10 14:42:46,923 - DEBUG - Traceback: NoneType: None

2025-08-10 14:42:52,991 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:43:10,821 - INFO - WebDriver closed
2025-08-10 14:43:14,892 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7D040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3dea350c4f7de11fdd2011c1c97a6e06
2025-08-10 14:43:18,975 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7CC00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3dea350c4f7de11fdd2011c1c97a6e06
2025-08-10 14:43:23,033 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7D150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3dea350c4f7de11fdd2011c1c97a6e06
2025-08-10 14:43:31,171 - INFO - WebDriver closed
2025-08-10 14:43:33,912 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:43:45,222 - INFO - WebDriver closed
2025-08-10 14:43:49,296 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B6DB460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51cf04b3354a5c0ffb96f13ff74d7650
2025-08-10 14:43:53,377 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7DD00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51cf04b3354a5c0ffb96f13ff74d7650
2025-08-10 14:43:57,450 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7E030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51cf04b3354a5c0ffb96f13ff74d7650
2025-08-10 14:44:05,602 - INFO - WebDriver closed
2025-08-10 14:44:07,293 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:44:28,430 - INFO - WebDriver closed
2025-08-10 14:44:32,512 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4B6DB460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/813392ae43f1bdbb34add96cb52d35b1
2025-08-10 14:44:36,590 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7D150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/813392ae43f1bdbb34add96cb52d35b1
2025-08-10 14:44:40,665 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DC4CC7D9D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/813392ae43f1bdbb34add96cb52d35b1
2025-08-10 14:44:48,817 - INFO - WebDriver closed
2025-08-10 14:46:35,238 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:46:50,854 - INFO - WebDriver closed
2025-08-10 14:46:54,931 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE8347DD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c7968c8b452366a8dcb7638a39a2e1ed
2025-08-10 14:46:59,002 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE8347E0D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c7968c8b452366a8dcb7638a39a2e1ed
2025-08-10 14:47:03,065 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE8341DE00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c7968c8b452366a8dcb7638a39a2e1ed
2025-08-10 14:47:11,205 - INFO - WebDriver closed
2025-08-10 14:47:12,901 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:47:20,635 - INFO - WebDriver closed
2025-08-10 14:47:28,378 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:47:32,412 - INFO - WebDriver closed
2025-08-10 14:47:36,472 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE834256A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dc25fb86ca417e1d7991ba2705088ee6
2025-08-10 14:47:40,540 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dc25fb86ca417e1d7991ba2705088ee6
2025-08-10 14:47:44,633 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dc25fb86ca417e1d7991ba2705088ee6
2025-08-10 14:47:52,787 - INFO - WebDriver closed
2025-08-10 14:47:54,446 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:47:57,775 - INFO - WebDriver closed
2025-08-10 14:47:58,960 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:48:01,850 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fdd7145d4bab53c29d71b0e37e9b6893
2025-08-10 14:48:05,920 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE834258C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fdd7145d4bab53c29d71b0e37e9b6893
2025-08-10 14:48:09,990 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fdd7145d4bab53c29d71b0e37e9b6893
2025-08-10 14:48:12,791 - ERROR - [NETWORK] ConnectionError: Test network error
2025-08-10 14:48:12,791 - DEBUG - Details: Error Type: ConnectionError
Error Message: Test network error
Error Args: ('Test network error',)
2025-08-10 14:48:12,791 - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,792 - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:12,792 - ERROR - [VALIDATION] ValueError: Test validation error
2025-08-10 14:48:12,792 - DEBUG - Details: Error Type: ValueError
Error Message: Test validation error
Error Args: ('Test validation error',)
2025-08-10 14:48:12,792 - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,792 - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:12,792 - ERROR - [SYSTEM] Exception: Test general error
2025-08-10 14:48:12,793 - DEBUG - Details: Error Type: Exception
Error Message: Test general error
Error Args: ('Test general error',)
2025-08-10 14:48:12,793 - DEBUG - Context: {'test': True}
2025-08-10 14:48:12,793 - DEBUG - Traceback: NoneType: None

2025-08-10 14:48:14,679 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:48:18,132 - INFO - WebDriver closed
2025-08-10 14:48:29,776 - INFO - WebDriver closed
2025-08-10 14:48:33,851 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fcaf16f2d1259a50e1bb3e7a79a3e182
2025-08-10 14:48:37,929 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE834259D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fcaf16f2d1259a50e1bb3e7a79a3e182
2025-08-10 14:48:42,002 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fcaf16f2d1259a50e1bb3e7a79a3e182
2025-08-10 14:48:50,153 - INFO - WebDriver closed
2025-08-10 14:48:52,942 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:49:02,827 - INFO - WebDriver closed
2025-08-10 14:49:06,905 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83425D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/def24df7f5f6e61eba2e6e133953468e
2025-08-10 14:49:10,951 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCABE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/def24df7f5f6e61eba2e6e133953468e
2025-08-10 14:49:15,017 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCAE00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/def24df7f5f6e61eba2e6e133953468e
2025-08-10 14:49:23,124 - INFO - WebDriver closed
2025-08-10 14:49:26,761 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:49:52,242 - INFO - WebDriver closed
2025-08-10 14:49:55,753 - INFO - WebDriver closed
2025-08-10 14:49:56,305 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE834258C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b6c7c4c400f446d8f3d4779386af5f91
2025-08-10 14:49:59,885 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE83426AD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/92bdf1aa2b9ec7dd3b1efb07cff4efd0
2025-08-10 14:50:00,363 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCB240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b6c7c4c400f446d8f3d4779386af5f91
2025-08-10 14:50:03,964 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCA580>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/92bdf1aa2b9ec7dd3b1efb07cff4efd0
2025-08-10 14:50:04,422 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCB130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b6c7c4c400f446d8f3d4779386af5f91
2025-08-10 14:50:08,021 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AE84BCAF10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/92bdf1aa2b9ec7dd3b1efb07cff4efd0
2025-08-10 14:50:12,522 - INFO - WebDriver closed
2025-08-10 14:50:16,134 - INFO - WebDriver closed
2025-08-10 14:53:42,983 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:54:34,388 - INFO - WebDriver closed
2025-08-10 14:54:38,430 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD73250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c100d6d90e5444e468237b3c411f57f
2025-08-10 14:54:42,477 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD72D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c100d6d90e5444e468237b3c411f57f
2025-08-10 14:54:46,517 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFE3C050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c100d6d90e5444e468237b3c411f57f
2025-08-10 14:54:54,623 - INFO - WebDriver closed
2025-08-10 14:54:56,623 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:55:27,081 - INFO - WebDriver closed
2025-08-10 14:55:35,939 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:55:39,688 - INFO - WebDriver closed
2025-08-10 14:55:43,744 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD27570>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51dfdd6d1ec45a459923d706218a3fbf
2025-08-10 14:55:47,794 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD27F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51dfdd6d1ec45a459923d706218a3fbf
2025-08-10 14:55:51,848 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD25370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/51dfdd6d1ec45a459923d706218a3fbf
2025-08-10 14:55:59,982 - INFO - WebDriver closed
2025-08-10 14:56:01,704 - INFO - Chrome WebDriver initialized successfully
2025-08-10 14:57:05,594 - INFO - WebDriver closed
2025-08-10 14:57:09,657 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD26F10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a0bac1f95a56501b8ce73f2a2b51046e
2025-08-10 14:57:13,736 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD26E00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a0bac1f95a56501b8ce73f2a2b51046e
2025-08-10 14:57:17,807 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EBFD27570>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a0bac1f95a56501b8ce73f2a2b51046e
2025-08-10 14:57:25,960 - INFO - WebDriver closed
2025-08-10 15:03:53,355 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:04:00,240 - INFO - WebDriver closed
2025-08-10 15:04:04,300 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000247E261D450>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ed058c43f5afe31328b28f9a278ff110
2025-08-10 15:04:08,345 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000247E261D6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ed058c43f5afe31328b28f9a278ff110
2025-08-10 15:04:12,408 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000247E25950F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ed058c43f5afe31328b28f9a278ff110
2025-08-10 15:04:20,533 - INFO - WebDriver closed
2025-08-10 15:11:24,156 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:11:32,070 - INFO - WebDriver closed
2025-08-10 15:11:36,138 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000205B786DBD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6d57f8c86d2afe26851872aba06475db
2025-08-10 15:11:40,202 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000205B786D6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6d57f8c86d2afe26851872aba06475db
2025-08-10 15:11:44,263 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000205B77E9480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6d57f8c86d2afe26851872aba06475db
2025-08-10 15:11:52,390 - INFO - WebDriver closed
2025-08-10 15:12:27,558 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:13:18,804 - INFO - WebDriver closed
2025-08-10 15:13:22,868 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED3C3250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9e02b8ec97f5ee26713a4977c88affb9
2025-08-10 15:13:26,940 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CEDDF4690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9e02b8ec97f5ee26713a4977c88affb9
2025-08-10 15:13:31,015 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED488050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9e02b8ec97f5ee26713a4977c88affb9
2025-08-10 15:13:39,160 - INFO - WebDriver closed
2025-08-10 15:13:41,099 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:13:41,476 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:13:42,076 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:13:48,984 - INFO - WebDriver closed
2025-08-10 15:13:53,039 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED373570>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1dc0675977d9e3c8fa174292986e3b5f
2025-08-10 15:13:53,512 - INFO - WebDriver closed
2025-08-10 15:13:57,105 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED4256A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1dc0675977d9e3c8fa174292986e3b5f
2025-08-10 15:13:57,596 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED4258C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5784170561662998d6a1187d31bf063f
2025-08-10 15:14:01,177 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED425BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1dc0675977d9e3c8fa174292986e3b5f
2025-08-10 15:14:01,673 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED425F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5784170561662998d6a1187d31bf063f
2025-08-10 15:14:05,752 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED426360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5784170561662998d6a1187d31bf063f
2025-08-10 15:14:09,335 - INFO - WebDriver closed
2025-08-10 15:14:13,899 - INFO - WebDriver closed
2025-08-10 15:14:40,954 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:15:05,765 - INFO - WebDriver closed
2025-08-10 15:15:14,679 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:15:18,582 - INFO - WebDriver closed
2025-08-10 15:15:22,653 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED4267A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3700203f2233693cd27448c952052c53
2025-08-10 15:15:26,722 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED425E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3700203f2233693cd27448c952052c53
2025-08-10 15:15:30,788 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED426690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3700203f2233693cd27448c952052c53
2025-08-10 15:15:38,914 - INFO - WebDriver closed
2025-08-10 15:15:42,681 - INFO - WebDriver closed
2025-08-10 15:15:44,852 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:15:46,743 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED372F10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/04cdba3114eebb1b1d76fced6777397f
2025-08-10 15:15:50,823 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED426690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/04cdba3114eebb1b1d76fced6777397f
2025-08-10 15:15:54,883 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED425590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/04cdba3114eebb1b1d76fced6777397f
2025-08-10 15:16:02,994 - INFO - WebDriver closed
2025-08-10 15:16:25,746 - INFO - WebDriver closed
2025-08-10 15:16:29,825 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED4268B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dd1470e5724c4617c29c53dcb5566756
2025-08-10 15:16:33,897 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED426140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dd1470e5724c4617c29c53dcb5566756
2025-08-10 15:16:37,972 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018CED425BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dd1470e5724c4617c29c53dcb5566756
2025-08-10 15:16:46,112 - INFO - WebDriver closed
2025-08-10 15:25:02,769 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:25:02,769 - ERROR - Scraping failed: 'str' object has no attribute 'value'
2025-08-10 15:25:03,091 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:25:03,092 - ERROR - Scraping failed: 'str' object has no attribute 'value'
2025-08-10 15:25:04,962 - INFO - WebDriver closed
2025-08-10 15:25:05,838 - INFO - WebDriver closed
2025-08-10 15:25:09,027 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CED19950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3e425a06d76ebc248bf03e5c33effa59
2025-08-10 15:25:09,900 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CED0CC30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/07dfad45645105d68e814a1b3b507494
2025-08-10 15:25:13,085 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CED0CB00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3e425a06d76ebc248bf03e5c33effa59
2025-08-10 15:25:13,963 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CEC9B0B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/07dfad45645105d68e814a1b3b507494
2025-08-10 15:25:17,146 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CEBE7BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3e425a06d76ebc248bf03e5c33effa59
2025-08-10 15:25:18,024 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000145CECB4490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/07dfad45645105d68e814a1b3b507494
2025-08-10 15:25:25,272 - INFO - WebDriver closed
2025-08-10 15:25:26,150 - INFO - WebDriver closed
2025-08-10 15:26:48,143 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:26:48,143 - ERROR - Scraping failed: 'str' object has no attribute 'value'
2025-08-10 15:26:50,216 - INFO - WebDriver closed
2025-08-10 15:26:54,277 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000282441FBED0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f7723cb7b3cc63f1758c0f1ca52b8512
2025-08-10 15:26:58,332 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000282442C0190>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f7723cb7b3cc63f1758c0f1ca52b8512
2025-08-10 15:27:02,387 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000282442AC510>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f7723cb7b3cc63f1758c0f1ca52b8512
2025-08-10 15:27:10,504 - INFO - WebDriver closed
2025-08-10 15:28:43,376 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:28:43,376 - ERROR - Scraping failed: 'str' object has no attribute 'value'
2025-08-10 15:28:45,446 - INFO - WebDriver closed
2025-08-10 15:28:49,520 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C9706A7B10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5401ecf782a7d3b175d6f30876c545c0
2025-08-10 15:28:53,592 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C9706A7C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5401ecf782a7d3b175d6f30876c545c0
2025-08-10 15:28:57,670 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C97074C770>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5401ecf782a7d3b175d6f30876c545c0
2025-08-10 15:31:58,669 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:32:01,021 - INFO - WebDriver closed
2025-08-10 15:32:05,100 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001377FE2BB10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bbff7151cf51a6f21020e9b041dc983c
2025-08-10 15:32:09,175 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001377FEE8050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bbff7151cf51a6f21020e9b041dc983c
2025-08-10 15:32:13,237 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001377FECC510>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bbff7151cf51a6f21020e9b041dc983c
2025-08-10 15:32:21,382 - INFO - WebDriver closed
2025-08-10 15:32:58,827 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:33:00,896 - INFO - WebDriver closed
2025-08-10 15:33:04,983 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002933FA27B10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cd440888f50020ba5dd46937e2f74342
2025-08-10 15:33:09,058 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002933FA27C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cd440888f50020ba5dd46937e2f74342
2025-08-10 15:33:13,145 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002933FAD83E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cd440888f50020ba5dd46937e2f74342
2025-08-10 15:33:21,310 - INFO - WebDriver closed
2025-08-10 15:34:48,807 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:37:38,295 - INFO - WebDriver closed
2025-08-10 15:37:42,365 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E86665AD50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cddba5e28bda6d8ab3e5e23aa3e12cdd
2025-08-10 15:37:46,441 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E86665AFD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cddba5e28bda6d8ab3e5e23aa3e12cdd
2025-08-10 15:37:50,518 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E8665D35C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cddba5e28bda6d8ab3e5e23aa3e12cdd
2025-08-10 15:37:58,688 - INFO - WebDriver closed
2025-08-10 15:38:02,459 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:40:56,727 - INFO - WebDriver closed
2025-08-10 15:41:00,814 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E866605F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32898a7bb3111e4b6e2d71507722e09c
2025-08-10 15:41:04,900 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E866605150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32898a7bb3111e4b6e2d71507722e09c
2025-08-10 15:41:08,991 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E8666056A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32898a7bb3111e4b6e2d71507722e09c
2025-08-10 15:41:17,161 - INFO - WebDriver closed
2025-08-10 15:41:20,973 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:44:20,264 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:44:24,215 - INFO - WebDriver closed
2025-08-10 15:44:28,287 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D448AB8A50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f95719ed0f86da4445119ed9422eda9d
2025-08-10 15:44:32,363 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D448AB9090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f95719ed0f86da4445119ed9422eda9d
2025-08-10 15:44:36,448 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D448A9D480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f95719ed0f86da4445119ed9422eda9d
2025-08-10 15:44:44,608 - INFO - WebDriver closed
2025-08-10 15:45:12,681 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:46:46,857 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:46:58,345 - INFO - WebDriver closed
2025-08-10 15:47:02,429 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC6B16D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bc823ff6d953aa851f3bddb6a510bed7
2025-08-10 15:47:06,488 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC6B11D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bc823ff6d953aa851f3bddb6a510bed7
2025-08-10 15:47:10,547 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC6150F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bc823ff6d953aa851f3bddb6a510bed7
2025-08-10 15:47:18,674 - INFO - WebDriver closed
2025-08-10 15:47:21,445 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:49:16,205 - INFO - WebDriver closed
2025-08-10 15:49:20,279 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/77ac28c1f0d6342468096d2e9604e9bc
2025-08-10 15:49:24,365 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC666250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/77ac28c1f0d6342468096d2e9604e9bc
2025-08-10 15:49:28,427 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC666580>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/77ac28c1f0d6342468096d2e9604e9bc
2025-08-10 15:49:36,573 - INFO - WebDriver closed
2025-08-10 15:49:39,335 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:49:56,674 - INFO - WebDriver closed
2025-08-10 15:50:00,754 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6591fc4818ab027507a523a4c7d93778
2025-08-10 15:50:04,842 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC666030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6591fc4818ab027507a523a4c7d93778
2025-08-10 15:50:08,921 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6591fc4818ab027507a523a4c7d93778
2025-08-10 15:50:17,073 - INFO - WebDriver closed
2025-08-10 15:50:19,891 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:50:38,594 - INFO - WebDriver closed
2025-08-10 15:50:42,677 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cbdc6eea691a0214b34c38a3c4b659cf
2025-08-10 15:50:46,761 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cbdc6eea691a0214b34c38a3c4b659cf
2025-08-10 15:50:50,837 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC666030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cbdc6eea691a0214b34c38a3c4b659cf
2025-08-10 15:50:59,016 - INFO - WebDriver closed
2025-08-10 15:51:05,139 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:51:16,090 - INFO - WebDriver closed
2025-08-10 15:51:20,160 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC6656A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/90f61061355275952f6a35689465d995
2025-08-10 15:51:24,235 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC665BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/90f61061355275952f6a35689465d995
2025-08-10 15:51:28,319 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000154DC6657B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/90f61061355275952f6a35689465d995
2025-08-10 15:51:36,403 - INFO - WebDriver closed
2025-08-10 15:55:08,860 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:55:10,419 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,420 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,421 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,421 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,423 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,423 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,424 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,426 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,428 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,429 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,430 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,430 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,433 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,434 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,435 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,435 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,436 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:10,438 - ERROR - Error extracting property data: 'IntegratedMagicBricksScraper' object has no attribute 'date_parser'
2025-08-10 15:55:16,861 - INFO - WebDriver closed
2025-08-10 15:55:20,940 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000224F6BD0E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f80edf64e7b3acaf508015f189ad05f6
2025-08-10 15:55:25,018 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000224F6BD1090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f80edf64e7b3acaf508015f189ad05f6
2025-08-10 15:55:29,085 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000224F6B49350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f80edf64e7b3acaf508015f189ad05f6
2025-08-10 15:55:37,249 - INFO - WebDriver closed
2025-08-10 15:56:47,669 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:56:56,422 - INFO - WebDriver closed
2025-08-10 15:57:00,491 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002008F93D6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8978ed3920d0f72cff770f2e6304b61f
2025-08-10 15:57:04,560 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002008F93D1D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8978ed3920d0f72cff770f2e6304b61f
2025-08-10 15:57:08,619 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002008F899480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8978ed3920d0f72cff770f2e6304b61f
2025-08-10 15:57:16,728 - INFO - WebDriver closed
2025-08-10 15:57:54,792 - INFO - Chrome WebDriver initialized successfully
2025-08-10 15:58:08,432 - INFO - WebDriver closed
2025-08-10 15:58:12,516 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028A320D5BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a7b63ac0ca73d50761d64dfe1c27ceeb
2025-08-10 15:58:16,592 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028A320D56D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a7b63ac0ca73d50761d64dfe1c27ceeb
2025-08-10 15:58:20,672 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028A320390F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a7b63ac0ca73d50761d64dfe1c27ceeb
2025-08-10 15:58:28,843 - INFO - WebDriver closed
2025-08-10 15:58:53,409 - INFO - Chrome WebDriver initialized successfully
2025-08-10 16:00:46,902 - INFO - WebDriver closed
2025-08-10 16:00:50,991 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023F61595BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5ab0ac628cee1fc9e58607c2793bd1e7
2025-08-10 16:00:55,064 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023F615956D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5ab0ac628cee1fc9e58607c2793bd1e7
2025-08-10 16:00:59,138 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023F614FCFC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/5ab0ac628cee1fc9e58607c2793bd1e7
2025-08-10 16:01:07,261 - INFO - WebDriver closed
2025-08-10 17:45:16,739 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:47:13,191 - INFO - WebDriver closed
2025-08-10 17:47:17,260 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027D6B1E5BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4bab040918b9271f1e9013229d7f157a
2025-08-10 17:47:21,347 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027D6B1E56D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4bab040918b9271f1e9013229d7f157a
2025-08-10 17:47:25,431 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027D6B141480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4bab040918b9271f1e9013229d7f157a
2025-08-10 17:47:33,556 - INFO - WebDriver closed
2025-08-10 17:48:06,398 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:48:20,032 - INFO - WebDriver closed
2025-08-10 17:48:24,107 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023D84665A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c20e4df8bc4a29537533ac6b356ebdd
2025-08-10 17:48:28,176 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023D84665590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c20e4df8bc4a29537533ac6b356ebdd
2025-08-10 17:48:32,244 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023D845C8FC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0c20e4df8bc4a29537533ac6b356ebdd
2025-08-10 17:48:40,396 - INFO - WebDriver closed
2025-08-10 17:49:30,074 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:49:43,946 - INFO - WebDriver closed
2025-08-10 17:49:48,015 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E281A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/af41556703d8c24c1f8b0d37dda646be
2025-08-10 17:49:52,096 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E281590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/af41556703d8c24c1f8b0d37dda646be
2025-08-10 17:49:56,173 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E1E50F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/af41556703d8c24c1f8b0d37dda646be
2025-08-10 17:50:04,305 - INFO - WebDriver closed
2025-08-10 17:50:07,220 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:52:04,390 - INFO - WebDriver closed
2025-08-10 17:52:08,464 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1d3011c7306d7e959713c7b7d9c10653
2025-08-10 17:52:12,547 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E236140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1d3011c7306d7e959713c7b7d9c10653
2025-08-10 17:52:16,616 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E236470>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/1d3011c7306d7e959713c7b7d9c10653
2025-08-10 17:52:24,760 - INFO - WebDriver closed
2025-08-10 17:52:27,508 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:52:44,288 - INFO - WebDriver closed
2025-08-10 17:52:48,352 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E236030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/49b37683b997e2bf259e7239fec9c064
2025-08-10 17:52:52,414 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E2357B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/49b37683b997e2bf259e7239fec9c064
2025-08-10 17:52:56,489 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/49b37683b997e2bf259e7239fec9c064
2025-08-10 17:53:04,637 - INFO - WebDriver closed
2025-08-10 17:53:07,361 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:53:23,705 - INFO - WebDriver closed
2025-08-10 17:53:27,785 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/023863d8ed2ad3919b10c9f5c40017d6
2025-08-10 17:53:31,855 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/023863d8ed2ad3919b10c9f5c40017d6
2025-08-10 17:53:35,930 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E2357B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/023863d8ed2ad3919b10c9f5c40017d6
2025-08-10 17:53:44,072 - INFO - WebDriver closed
2025-08-10 17:53:46,847 - INFO - Chrome WebDriver initialized successfully
2025-08-10 17:54:04,631 - INFO - WebDriver closed
2025-08-10 17:54:08,704 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E236690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/984ab75fda5369a43920b298ff7a5dce
2025-08-10 17:54:12,789 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/984ab75fda5369a43920b298ff7a5dce
2025-08-10 17:54:16,865 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C48E235260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/984ab75fda5369a43920b298ff7a5dce
2025-08-10 17:54:25,038 - INFO - WebDriver closed
2025-08-10 18:00:54,867 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:01:08,548 - INFO - WebDriver closed
2025-08-10 18:01:12,614 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C50F955810>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/96f04172b483a24cdea2c7917de8ded6
2025-08-10 18:01:16,687 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C50F955310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/96f04172b483a24cdea2c7917de8ded6
2025-08-10 18:01:20,765 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C50F8C1350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/96f04172b483a24cdea2c7917de8ded6
2025-08-10 18:01:28,908 - INFO - WebDriver closed
2025-08-10 18:02:44,722 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:02:59,031 - INFO - WebDriver closed
2025-08-10 18:03:03,109 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90E9A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9c5a4402709d9b4188aa04f8fe072187
2025-08-10 18:03:07,191 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90E9590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9c5a4402709d9b4188aa04f8fe072187
2025-08-10 18:03:11,269 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90490F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9c5a4402709d9b4188aa04f8fe072187
2025-08-10 18:03:19,415 - INFO - WebDriver closed
2025-08-10 18:03:22,145 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:03:37,920 - INFO - WebDriver closed
2025-08-10 18:03:41,989 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90957B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6bdaa4694876fc53258c993bb97f01be
2025-08-10 18:03:46,064 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90956A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6bdaa4694876fc53258c993bb97f01be
2025-08-10 18:03:50,153 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6bdaa4694876fc53258c993bb97f01be
2025-08-10 18:03:58,307 - INFO - WebDriver closed
2025-08-10 18:04:01,221 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:04:17,603 - INFO - WebDriver closed
2025-08-10 18:04:21,682 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90959D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4b95436cdfb673f9c72ed4124e76cab2
2025-08-10 18:04:25,750 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4b95436cdfb673f9c72ed4124e76cab2
2025-08-10 18:04:29,844 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4b95436cdfb673f9c72ed4124e76cab2
2025-08-10 18:04:38,005 - INFO - WebDriver closed
2025-08-10 18:04:40,744 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:04:57,625 - INFO - WebDriver closed
2025-08-10 18:05:01,687 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/2efae81d01a50ee2de6f1891ece03bce
2025-08-10 18:05:05,771 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/2efae81d01a50ee2de6f1891ece03bce
2025-08-10 18:05:09,838 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E90945A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/2efae81d01a50ee2de6f1891ece03bce
2025-08-10 18:05:17,989 - INFO - WebDriver closed
2025-08-10 18:05:20,672 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:05:34,671 - INFO - WebDriver closed
2025-08-10 18:05:38,733 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e83ce1abf7119815572c368ac420a3c5
2025-08-10 18:05:42,809 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e83ce1abf7119815572c368ac420a3c5
2025-08-10 18:05:46,882 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000159E9095D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e83ce1abf7119815572c368ac420a3c5
2025-08-10 18:05:55,046 - INFO - WebDriver closed
2025-08-10 18:13:16,436 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:13:18,023 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,023 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,024 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,025 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,026 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,027 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,028 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,029 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,031 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,032 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,033 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,034 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,036 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,037 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,038 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,039 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,040 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:18,041 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,812 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,816 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,817 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,819 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,820 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,821 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,822 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,825 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,826 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,827 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,828 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,830 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,831 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,833 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,834 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,835 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:22,836 - ERROR - Error extracting property data: 'DateParsingSystem' object has no attribute 'parse_date_to_datetime'
2025-08-10 18:13:27,396 - INFO - WebDriver closed
2025-08-10 18:13:31,475 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017F8447D090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c2b574c4fbc0fe113eecca4bcbb505b4
2025-08-10 18:13:35,559 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017F8447D450>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c2b574c4fbc0fe113eecca4bcbb505b4
2025-08-10 18:13:39,634 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017F843F5350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c2b574c4fbc0fe113eecca4bcbb505b4
2025-08-10 18:13:47,779 - INFO - WebDriver closed
2025-08-10 18:15:28,811 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:15:41,474 - INFO - WebDriver closed
2025-08-10 18:15:45,569 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BC0486DA90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0145222d81582115dd1c6f061ec0c6f7
2025-08-10 18:15:49,641 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BC0486D590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0145222d81582115dd1c6f061ec0c6f7
2025-08-10 18:15:53,712 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BC047E5350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0145222d81582115dd1c6f061ec0c6f7
2025-08-10 18:16:01,878 - INFO - WebDriver closed
2025-08-10 18:16:37,355 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:16:50,967 - INFO - WebDriver closed
2025-08-10 18:16:55,051 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002634FBAD6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0d18cee5a51a5ff956965116c51cab2f
2025-08-10 18:16:59,137 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002634FBAD1D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0d18cee5a51a5ff956965116c51cab2f
2025-08-10 18:17:03,189 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002634FB04FC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/0d18cee5a51a5ff956965116c51cab2f
2025-08-10 18:17:11,288 - INFO - WebDriver closed
2025-08-10 18:17:55,061 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:18:26,478 - INFO - WebDriver closed
2025-08-10 18:18:30,544 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAFDA90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c9a002eed4c8251a2390c1216404532b
2025-08-10 18:18:34,631 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAFD590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c9a002eed4c8251a2390c1216404532b
2025-08-10 18:18:38,691 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BA58FC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/c9a002eed4c8251a2390c1216404532b
2025-08-10 18:18:46,846 - INFO - WebDriver closed
2025-08-10 18:18:49,524 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:19:07,196 - INFO - WebDriver closed
2025-08-10 18:19:11,262 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA57B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/db6ac8baf2a3601696d9b8fb3281ab5f
2025-08-10 18:19:15,329 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA56A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/db6ac8baf2a3601696d9b8fb3281ab5f
2025-08-10 18:19:19,386 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/db6ac8baf2a3601696d9b8fb3281ab5f
2025-08-10 18:19:27,485 - INFO - WebDriver closed
2025-08-10 18:19:30,452 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:19:47,690 - INFO - WebDriver closed
2025-08-10 18:19:51,755 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA59D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/047f5384a1e0d3d045f61a653970559c
2025-08-10 18:19:55,822 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/047f5384a1e0d3d045f61a653970559c
2025-08-10 18:19:59,895 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/047f5384a1e0d3d045f61a653970559c
2025-08-10 18:20:08,018 - INFO - WebDriver closed
2025-08-10 18:20:10,817 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:20:29,043 - INFO - WebDriver closed
2025-08-10 18:20:33,101 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/411bbf9c20e7e4c547492860a03190da
2025-08-10 18:20:37,163 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/411bbf9c20e7e4c547492860a03190da
2025-08-10 18:20:41,231 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA45A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/411bbf9c20e7e4c547492860a03190da
2025-08-10 18:20:49,366 - INFO - WebDriver closed
2025-08-10 18:20:55,552 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:21:09,804 - INFO - WebDriver closed
2025-08-10 18:21:13,874 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/99591f68839dfb424844225a62230aa3
2025-08-10 18:21:17,938 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA5480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/99591f68839dfb424844225a62230aa3
2025-08-10 18:21:22,002 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001E36BAA6140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/99591f68839dfb424844225a62230aa3
2025-08-10 18:21:30,144 - INFO - WebDriver closed
2025-08-10 18:29:21,749 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:29:36,563 - INFO - WebDriver closed
2025-08-10 18:29:42,365 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:29:57,666 - INFO - WebDriver closed
2025-08-10 18:30:03,527 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:30:20,590 - INFO - WebDriver closed
2025-08-10 18:30:26,426 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:30:42,399 - INFO - WebDriver closed
2025-08-10 18:47:48,950 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:47:55,897 - INFO - WebDriver closed
2025-08-10 18:50:40,997 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:50:47,344 - INFO - WebDriver closed
2025-08-10 18:51:40,036 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:51:56,123 - INFO - WebDriver closed
2025-08-10 18:52:02,035 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:52:17,081 - INFO - WebDriver closed
2025-08-10 18:52:23,224 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:52:40,726 - INFO - WebDriver closed
2025-08-10 18:52:46,757 - INFO - Chrome WebDriver initialized successfully
2025-08-10 18:53:03,079 - INFO - WebDriver closed
2025-08-10 19:06:22,558 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:06:26,295 - INFO - WebDriver closed
2025-08-10 19:20:06,092 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:20:26,966 - INFO - WebDriver closed
2025-08-10 19:20:32,675 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:20:49,469 - INFO - WebDriver closed
2025-08-10 19:20:55,394 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:21:13,806 - INFO - WebDriver closed
2025-08-10 19:21:19,939 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:21:38,230 - INFO - WebDriver closed
2025-08-10 19:21:44,018 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:22:01,634 - INFO - WebDriver closed
2025-08-10 19:23:53,078 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:24:07,736 - INFO - WebDriver closed
2025-08-10 19:24:13,424 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:24:22,959 - INFO - WebDriver closed
2025-08-10 19:24:28,807 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:24:39,485 - INFO - WebDriver closed
2025-08-10 19:24:45,397 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:24:55,530 - INFO - WebDriver closed
2025-08-10 19:25:01,328 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:25:13,058 - INFO - WebDriver closed
2025-08-10 19:35:43,321 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:35:50,967 - INFO - WebDriver closed
2025-08-10 19:35:56,678 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:36:03,043 - INFO - WebDriver closed
2025-08-10 19:36:08,857 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:36:17,459 - INFO - WebDriver closed
2025-08-10 19:36:23,327 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:36:30,231 - INFO - WebDriver closed
2025-08-10 19:37:47,777 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:38:15,460 - INFO - WebDriver closed
2025-08-10 19:38:55,079 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:38:58,873 - INFO - WebDriver closed
2025-08-10 19:39:04,641 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:39:08,483 - INFO - WebDriver closed
2025-08-10 19:39:14,294 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:39:26,814 - INFO - WebDriver closed
2025-08-10 19:50:17,172 - INFO - Chrome WebDriver initialized successfully
2025-08-10 19:50:22,206 - INFO - ============================================================
2025-08-10 19:53:13,316 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA91BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:53:17,414 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14D338050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:53:21,502 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA09F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:53:37,761 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14DC3A8D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:53:41,843 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA52030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:53:45,931 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA52140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:02,590 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA527A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:06,670 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA528B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:10,772 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA52BE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:26,584 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA53130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:30,679 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA52690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:34,778 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA52470>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:54:59,515 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA51E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:55:03,604 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA518C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:55:07,692 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA519D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310/url
2025-08-10 19:55:28,504 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA53460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310
2025-08-10 19:55:32,587 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA53570>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310
2025-08-10 19:55:36,669 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B14CA538A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/8bd1a5f57f3df4d8396db05942af3310
2025-08-10 19:55:44,794 - INFO - WebDriver closed
2025-08-11 10:24:52,461 - INFO - Chrome WebDriver initialized successfully
2025-08-11 10:25:00,048 - INFO - WebDriver closed
2025-08-11 10:25:02,070 - INFO - Chrome WebDriver initialized successfully
2025-08-11 10:25:07,770 - INFO - ============================================================
2025-08-11 10:27:55,955 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/e942029b74b3e696371681e87e56b4fd
2025-08-11 10:27:59,999 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CC2C318F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e942029b74b3e696371681e87e56b4fd
2025-08-11 10:28:04,043 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001CC2C6FCB00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e942029b74b3e696371681e87e56b4fd
2025-08-11 10:28:12,110 - INFO - WebDriver closed
2025-08-11 10:45:24,443 - INFO - Chrome WebDriver initialized successfully
2025-08-11 10:45:26,411 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-11 10:45:34,140 - INFO - WebDriver closed
2025-08-11 11:02:38,242 - INFO - Chrome WebDriver initialized successfully
2025-08-11 11:02:40,163 - INFO - [TIMER] Waiting 3.7 seconds before next page...
2025-08-11 11:02:43,830 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-11 11:02:43,831 - INFO -    [LIST] Total properties: 0
2025-08-11 11:02:43,831 - INFO -    [SUCCESS] Valid properties: 0
2025-08-11 11:02:43,831 - ERROR - Scraping failed: 'validation_success_rate'
2025-08-11 11:02:46,555 - INFO - WebDriver closed
2025-08-11 12:26:07,003 - INFO - Chrome WebDriver initialized successfully
2025-08-11 12:26:09,052 - INFO - [TIMER] Waiting 3.6 seconds before next page...
2025-08-11 12:26:14,296 - INFO - [TIMER] Waiting 2.3 seconds before next page...
2025-08-11 12:26:18,416 - INFO - [TIMER] Waiting 2.5 seconds before next page...
2025-08-11 12:26:22,246 - INFO - [TIMER] Waiting 4.8 seconds before next page...
2025-08-11 12:26:28,216 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-11 12:26:28,216 - INFO -    [LIST] Total properties: 84
2025-08-11 12:26:28,217 - INFO -    [SUCCESS] Valid properties: 84
2025-08-11 12:26:28,217 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-11 12:26:28,217 - INFO -    [COMPLETE] Average data quality: 94.4%
2025-08-11 12:26:28,235 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-11 12:26:28,235 - INFO - ============================================================
2025-08-11 12:26:28,235 - INFO -    [LIST] Found 84 property URLs for detailed scraping
2025-08-11 12:26:28,235 - INFO - [HOUSE] Starting individual property page scraping for 84 properties
2025-08-11 12:26:28,235 - INFO -    [BATCH] Batch size: 10
2025-08-11 12:26:28,235 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-11 12:26:28,235 - INFO - \n[BATCH] Processing batch 1: Properties 1-10
2025-08-11 12:26:28,236 - INFO -    🔍 Scraping property 1 (attempt 1/3)
2025-08-11 12:26:32,309 - INFO -    [SUCCESS] Property 1 scraped successfully
2025-08-11 12:26:32,309 - INFO -    [SUCCESS] Property 1/84: Success
2025-08-11 12:26:32,309 - INFO -    [TIMER] Waiting 6.0s before next property...
2025-08-11 12:26:38,339 - INFO -    🔍 Scraping property 2 (attempt 1/3)
2025-08-11 12:26:42,750 - INFO -    [SUCCESS] Property 2 scraped successfully
2025-08-11 12:26:42,750 - INFO -    [SUCCESS] Property 2/84: Success
2025-08-11 12:26:42,751 - INFO -    [TIMER] Waiting 7.9s before next property...
2025-08-11 12:26:50,693 - INFO -    🔍 Scraping property 3 (attempt 1/3)
2025-08-11 12:26:55,574 - INFO -    [SUCCESS] Property 3 scraped successfully
2025-08-11 12:26:55,575 - INFO -    [SUCCESS] Property 3/84: Success
2025-08-11 12:26:55,575 - INFO -    [TIMER] Waiting 3.1s before next property...
2025-08-11 12:26:58,700 - INFO -    🔍 Scraping property 4 (attempt 1/3)
2025-08-11 12:27:02,358 - INFO -    [SUCCESS] Property 4 scraped successfully
2025-08-11 12:27:02,359 - INFO -    [SUCCESS] Property 4/84: Success
2025-08-11 12:27:02,359 - INFO -    [TIMER] Waiting 7.8s before next property...
2025-08-11 12:27:10,198 - INFO -    🔍 Scraping property 5 (attempt 1/3)
2025-08-11 12:27:15,246 - INFO -    [SUCCESS] Property 5 scraped successfully
2025-08-11 12:27:15,247 - INFO -    [SUCCESS] Property 5/84: Success
2025-08-11 12:27:15,247 - INFO -    [TIMER] Waiting 8.0s before next property...
2025-08-11 12:27:23,231 - INFO -    🔍 Scraping property 6 (attempt 1/3)
2025-08-11 12:27:28,409 - INFO -    [SUCCESS] Property 6 scraped successfully
2025-08-11 12:27:28,409 - INFO -    [SUCCESS] Property 6/84: Success
2025-08-11 12:27:28,409 - INFO -    [TIMER] Waiting 6.4s before next property...
2025-08-11 12:27:34,784 - INFO -    🔍 Scraping property 7 (attempt 1/3)
2025-08-11 12:27:39,713 - INFO -    [SUCCESS] Property 7 scraped successfully
2025-08-11 12:27:39,714 - INFO -    [SUCCESS] Property 7/84: Success
2025-08-11 12:27:39,714 - INFO -    [TIMER] Waiting 6.2s before next property...
2025-08-11 12:27:45,933 - INFO -    🔍 Scraping property 8 (attempt 1/3)
2025-08-11 12:27:50,408 - INFO -    [SUCCESS] Property 8 scraped successfully
2025-08-11 12:27:50,408 - INFO -    [SUCCESS] Property 8/84: Success
2025-08-11 12:27:50,409 - INFO -    [TIMER] Waiting 5.5s before next property...
2025-08-11 12:27:55,880 - INFO -    🔍 Scraping property 9 (attempt 1/3)
2025-08-11 12:28:00,436 - INFO -    [SUCCESS] Property 9 scraped successfully
2025-08-11 12:28:00,437 - INFO -    [SUCCESS] Property 9/84: Success
2025-08-11 12:28:00,438 - INFO -    [TIMER] Waiting 7.1s before next property...
2025-08-11 12:28:07,546 - INFO -    🔍 Scraping property 10 (attempt 1/3)
2025-08-11 12:28:11,553 - INFO -    [SUCCESS] Property 10 scraped successfully
2025-08-11 12:28:11,553 - INFO -    [SUCCESS] Property 10/84: Success
2025-08-11 12:28:11,554 - INFO -    [BREAK] Batch break: 15s
2025-08-11 12:28:26,555 - INFO - \n[BATCH] Processing batch 2: Properties 11-20
2025-08-11 12:28:26,555 - INFO -    🔍 Scraping property 11 (attempt 1/3)
2025-08-11 12:28:31,299 - INFO -    [SUCCESS] Property 11 scraped successfully
2025-08-11 12:28:31,300 - INFO -    [SUCCESS] Property 11/84: Success
2025-08-11 12:28:31,300 - INFO -    [TIMER] Waiting 3.7s before next property...
2025-08-11 12:28:34,992 - INFO -    🔍 Scraping property 12 (attempt 1/3)
2025-08-11 12:28:39,336 - INFO -    [SUCCESS] Property 12 scraped successfully
2025-08-11 12:28:39,337 - INFO -    [SUCCESS] Property 12/84: Success
2025-08-11 12:28:39,337 - INFO -    [TIMER] Waiting 3.6s before next property...
2025-08-11 12:28:42,971 - INFO -    🔍 Scraping property 13 (attempt 1/3)
2025-08-11 12:28:47,414 - INFO -    [SUCCESS] Property 13 scraped successfully
2025-08-11 12:28:47,415 - INFO -    [SUCCESS] Property 13/84: Success
2025-08-11 12:28:47,415 - INFO -    [TIMER] Waiting 4.1s before next property...
2025-08-11 12:28:51,565 - INFO -    🔍 Scraping property 14 (attempt 1/3)
2025-08-11 12:28:56,782 - INFO -    [SUCCESS] Property 14 scraped successfully
2025-08-11 12:28:56,783 - INFO -    [SUCCESS] Property 14/84: Success
2025-08-11 12:28:56,784 - INFO -    [TIMER] Waiting 6.1s before next property...
2025-08-11 12:29:02,890 - INFO -    🔍 Scraping property 15 (attempt 1/3)
2025-08-11 12:29:08,689 - INFO -    [SUCCESS] Property 15 scraped successfully
2025-08-11 12:29:08,690 - INFO -    [SUCCESS] Property 15/84: Success
2025-08-11 12:29:08,690 - INFO -    [TIMER] Waiting 6.2s before next property...
2025-08-11 12:29:14,921 - INFO -    🔍 Scraping property 16 (attempt 1/3)
2025-08-11 12:29:19,560 - INFO -    [SUCCESS] Property 16 scraped successfully
2025-08-11 12:29:19,561 - INFO -    [SUCCESS] Property 16/84: Success
2025-08-11 12:29:19,561 - INFO -    [TIMER] Waiting 7.5s before next property...
2025-08-11 12:29:27,031 - INFO -    🔍 Scraping property 17 (attempt 1/3)
2025-08-11 12:29:31,171 - INFO -    [SUCCESS] Property 17 scraped successfully
2025-08-11 12:29:31,172 - INFO -    [SUCCESS] Property 17/84: Success
2025-08-11 12:29:31,172 - INFO -    [TIMER] Waiting 4.3s before next property...
2025-08-11 12:29:35,459 - INFO -    🔍 Scraping property 18 (attempt 1/3)
2025-08-11 12:29:39,339 - INFO -    [SUCCESS] Property 18 scraped successfully
2025-08-11 12:29:39,339 - INFO -    [SUCCESS] Property 18/84: Success
2025-08-11 12:29:39,340 - INFO -    [TIMER] Waiting 6.8s before next property...
2025-08-11 12:29:46,162 - INFO -    🔍 Scraping property 19 (attempt 1/3)
2025-08-11 12:29:51,378 - INFO -    [SUCCESS] Property 19 scraped successfully
2025-08-11 12:29:51,379 - INFO -    [SUCCESS] Property 19/84: Success
2025-08-11 12:29:51,379 - INFO -    [TIMER] Waiting 7.5s before next property...
2025-08-11 12:29:58,862 - INFO -    🔍 Scraping property 20 (attempt 1/3)
2025-08-11 12:30:02,215 - INFO -    [SUCCESS] Property 20 scraped successfully
2025-08-11 12:30:02,216 - INFO -    [SUCCESS] Property 20/84: Success
2025-08-11 12:30:02,216 - INFO -    [BREAK] Batch break: 20s
2025-08-11 12:30:22,217 - INFO - \n[BATCH] Processing batch 3: Properties 21-30
2025-08-11 12:30:22,217 - INFO -    🔍 Scraping property 21 (attempt 1/3)
2025-08-11 12:30:26,292 - INFO -    [SUCCESS] Property 21 scraped successfully
2025-08-11 12:30:26,293 - INFO -    [SUCCESS] Property 21/84: Success
2025-08-11 12:30:26,293 - INFO -    [TIMER] Waiting 7.2s before next property...
2025-08-11 12:30:33,486 - INFO -    🔍 Scraping property 22 (attempt 1/3)
2025-08-11 12:30:36,959 - INFO -    [SUCCESS] Property 22 scraped successfully
2025-08-11 12:30:36,959 - INFO -    [SUCCESS] Property 22/84: Success
2025-08-11 12:30:36,959 - INFO -    [TIMER] Waiting 7.0s before next property...
2025-08-11 12:30:43,988 - INFO -    🔍 Scraping property 23 (attempt 1/3)
2025-08-11 12:30:47,587 - INFO -    [SUCCESS] Property 23 scraped successfully
2025-08-11 12:30:47,588 - INFO -    [SUCCESS] Property 23/84: Success
2025-08-11 12:30:47,588 - INFO -    [TIMER] Waiting 3.1s before next property...
2025-08-11 12:30:50,688 - INFO -    🔍 Scraping property 24 (attempt 1/3)
2025-08-11 12:30:53,976 - WARNING -    ⚠️ Poor data quality on attempt 1
2025-08-11 12:30:53,977 - INFO -    🔍 Scraping property 24 (attempt 2/3)
2025-08-11 12:30:58,073 - WARNING -    ⚠️ Poor data quality on attempt 2
2025-08-11 12:30:58,073 - INFO -    🔍 Scraping property 24 (attempt 3/3)
2025-08-11 12:31:03,056 - WARNING -    ⚠️ Poor data quality on attempt 3
2025-08-11 12:31:03,056 - INFO -    📝 Returning partial data for property 24
2025-08-11 12:31:03,057 - INFO -    [SUCCESS] Property 24/84: Success
2025-08-11 12:31:03,057 - INFO -    [TIMER] Waiting 5.8s before next property...
2025-08-11 12:31:08,818 - INFO -    🔍 Scraping property 25 (attempt 1/3)
2025-08-11 12:31:13,977 - INFO -    [SUCCESS] Property 25 scraped successfully
2025-08-11 12:31:13,978 - INFO -    [SUCCESS] Property 25/84: Success
2025-08-11 12:31:13,978 - INFO -    [TIMER] Waiting 6.2s before next property...
2025-08-11 12:31:20,219 - INFO -    🔍 Scraping property 26 (attempt 1/3)
2025-08-11 12:31:23,525 - INFO -    [SUCCESS] Property 26 scraped successfully
2025-08-11 12:31:23,525 - INFO -    [SUCCESS] Property 26/84: Success
2025-08-11 12:31:23,526 - INFO -    [TIMER] Waiting 8.8s before next property...
2025-08-11 12:31:32,355 - INFO -    🔍 Scraping property 27 (attempt 1/3)
2025-08-11 12:31:36,608 - INFO -    [SUCCESS] Property 27 scraped successfully
2025-08-11 12:31:36,608 - INFO -    [SUCCESS] Property 27/84: Success
2025-08-11 12:31:36,608 - INFO -    [TIMER] Waiting 5.0s before next property...
2025-08-11 12:31:41,617 - INFO -    🔍 Scraping property 28 (attempt 1/3)
2025-08-11 12:31:45,605 - INFO -    [SUCCESS] Property 28 scraped successfully
2025-08-11 12:31:45,605 - INFO -    [SUCCESS] Property 28/84: Success
2025-08-11 12:31:45,606 - INFO -    [TIMER] Waiting 3.7s before next property...
2025-08-11 12:31:49,297 - INFO -    🔍 Scraping property 29 (attempt 1/3)
2025-08-11 12:31:52,648 - INFO -    [SUCCESS] Property 29 scraped successfully
2025-08-11 12:31:52,649 - INFO -    [SUCCESS] Property 29/84: Success
2025-08-11 12:31:52,649 - INFO -    [TIMER] Waiting 5.4s before next property...
2025-08-11 12:31:58,067 - INFO -    🔍 Scraping property 30 (attempt 1/3)
2025-08-11 12:32:01,960 - INFO -    [SUCCESS] Property 30 scraped successfully
2025-08-11 12:32:01,961 - INFO -    [SUCCESS] Property 30/84: Success
2025-08-11 12:32:01,961 - INFO -    [BREAK] Batch break: 25s
2025-08-11 12:32:26,962 - INFO - \n[BATCH] Processing batch 4: Properties 31-40
2025-08-11 12:32:26,963 - INFO -    🔍 Scraping property 31 (attempt 1/3)
2025-08-11 12:32:31,541 - INFO -    [SUCCESS] Property 31 scraped successfully
2025-08-11 12:32:31,541 - INFO -    [SUCCESS] Property 31/84: Success
2025-08-11 12:32:31,542 - INFO -    [TIMER] Waiting 7.4s before next property...
2025-08-11 12:32:38,944 - INFO -    🔍 Scraping property 32 (attempt 1/3)
2025-08-11 12:32:42,952 - INFO -    [SUCCESS] Property 32 scraped successfully
2025-08-11 12:32:42,952 - INFO -    [SUCCESS] Property 32/84: Success
2025-08-11 12:32:42,953 - INFO -    [TIMER] Waiting 4.5s before next property...
2025-08-11 12:32:47,424 - INFO -    🔍 Scraping property 33 (attempt 1/3)
2025-08-11 12:32:50,940 - INFO -    [SUCCESS] Property 33 scraped successfully
2025-08-11 12:32:50,941 - INFO -    [SUCCESS] Property 33/84: Success
2025-08-11 12:32:50,941 - INFO -    [TIMER] Waiting 6.6s before next property...
2025-08-11 12:32:57,504 - INFO -    🔍 Scraping property 34 (attempt 1/3)
2025-08-11 12:33:04,051 - INFO -    [SUCCESS] Property 34 scraped successfully
2025-08-11 12:33:04,052 - INFO -    [SUCCESS] Property 34/84: Success
2025-08-11 12:33:04,052 - INFO -    [TIMER] Waiting 6.6s before next property...
2025-08-11 12:33:10,645 - INFO -    🔍 Scraping property 35 (attempt 1/3)
2025-08-11 12:33:15,068 - INFO -    [SUCCESS] Property 35 scraped successfully
2025-08-11 12:33:15,069 - INFO -    [SUCCESS] Property 35/84: Success
2025-08-11 12:33:15,069 - INFO -    [TIMER] Waiting 6.9s before next property...
2025-08-11 12:33:21,982 - INFO -    🔍 Scraping property 36 (attempt 1/3)
2025-08-11 12:33:25,746 - INFO -    [SUCCESS] Property 36 scraped successfully
2025-08-11 12:33:25,746 - INFO -    [SUCCESS] Property 36/84: Success
2025-08-11 12:33:25,746 - INFO -    [TIMER] Waiting 4.6s before next property...
2025-08-11 12:33:30,330 - INFO -    🔍 Scraping property 37 (attempt 1/3)
2025-08-11 12:33:35,756 - INFO -    [SUCCESS] Property 37 scraped successfully
2025-08-11 12:33:35,757 - INFO -    [SUCCESS] Property 37/84: Success
2025-08-11 12:33:35,757 - INFO -    [TIMER] Waiting 5.4s before next property...
2025-08-11 12:33:41,160 - INFO -    🔍 Scraping property 38 (attempt 1/3)
2025-08-11 12:33:44,976 - INFO -    [SUCCESS] Property 38 scraped successfully
2025-08-11 12:33:44,976 - INFO -    [SUCCESS] Property 38/84: Success
2025-08-11 12:33:44,977 - INFO -    [TIMER] Waiting 5.3s before next property...
2025-08-11 12:33:50,240 - INFO -    🔍 Scraping property 39 (attempt 1/3)
2025-08-11 12:33:54,338 - INFO -    [SUCCESS] Property 39 scraped successfully
2025-08-11 12:33:54,338 - INFO -    [SUCCESS] Property 39/84: Success
2025-08-11 12:33:54,338 - INFO -    [TIMER] Waiting 5.0s before next property...
2025-08-11 12:33:59,349 - INFO -    🔍 Scraping property 40 (attempt 1/3)
2025-08-11 12:34:02,782 - INFO -    [SUCCESS] Property 40 scraped successfully
2025-08-11 12:34:02,782 - INFO -    [SUCCESS] Property 40/84: Success
2025-08-11 12:34:02,782 - INFO -    [BREAK] Batch break: 30s
2025-08-11 12:34:32,783 - INFO - \n[BATCH] Processing batch 5: Properties 41-50
2025-08-11 12:34:32,784 - INFO -    🔍 Scraping property 41 (attempt 1/3)
2025-08-11 12:34:37,165 - INFO -    [SUCCESS] Property 41 scraped successfully
2025-08-11 12:34:37,165 - INFO -    [SUCCESS] Property 41/84: Success
2025-08-11 12:34:37,166 - INFO -    [TIMER] Waiting 7.2s before next property...
2025-08-11 12:34:44,386 - INFO -    🔍 Scraping property 42 (attempt 1/3)
2025-08-11 12:34:48,096 - INFO -    [SUCCESS] Property 42 scraped successfully
2025-08-11 12:34:48,097 - INFO -    [SUCCESS] Property 42/84: Success
2025-08-11 12:34:48,097 - INFO -    [TIMER] Waiting 4.4s before next property...
2025-08-11 12:34:52,479 - INFO -    🔍 Scraping property 43 (attempt 1/3)
2025-08-11 12:34:56,312 - INFO -    [SUCCESS] Property 43 scraped successfully
2025-08-11 12:34:56,312 - INFO -    [SUCCESS] Property 43/84: Success
2025-08-11 12:34:56,312 - INFO -    [TIMER] Waiting 4.1s before next property...
2025-08-11 12:35:00,458 - INFO -    🔍 Scraping property 44 (attempt 1/3)
2025-08-11 12:35:04,615 - INFO -    [SUCCESS] Property 44 scraped successfully
2025-08-11 12:35:04,616 - INFO -    [SUCCESS] Property 44/84: Success
2025-08-11 12:35:04,617 - INFO -    [TIMER] Waiting 4.6s before next property...
2025-08-11 12:35:09,174 - INFO -    🔍 Scraping property 45 (attempt 1/3)
2025-08-11 12:35:13,616 - INFO -    [SUCCESS] Property 45 scraped successfully
2025-08-11 12:35:13,617 - INFO -    [SUCCESS] Property 45/84: Success
2025-08-11 12:35:13,617 - INFO -    [TIMER] Waiting 9.0s before next property...
2025-08-11 12:35:22,581 - INFO -    🔍 Scraping property 46 (attempt 1/3)
2025-08-11 12:35:27,966 - INFO -    [SUCCESS] Property 46 scraped successfully
2025-08-11 12:35:27,967 - INFO -    [SUCCESS] Property 46/84: Success
2025-08-11 12:35:27,967 - INFO -    [TIMER] Waiting 4.5s before next property...
2025-08-11 12:35:32,484 - INFO -    🔍 Scraping property 47 (attempt 1/3)
2025-08-11 12:35:37,067 - INFO -    [SUCCESS] Property 47 scraped successfully
2025-08-11 12:35:37,067 - INFO -    [SUCCESS] Property 47/84: Success
2025-08-11 12:35:37,067 - INFO -    [TIMER] Waiting 4.1s before next property...
2025-08-11 12:35:41,202 - INFO -    🔍 Scraping property 48 (attempt 1/3)
2025-08-11 12:35:45,950 - INFO -    [SUCCESS] Property 48 scraped successfully
2025-08-11 12:35:45,950 - INFO -    [SUCCESS] Property 48/84: Success
2025-08-11 12:35:45,950 - INFO -    [TIMER] Waiting 6.3s before next property...
2025-08-11 12:35:52,270 - INFO -    🔍 Scraping property 49 (attempt 1/3)
2025-08-11 12:35:56,575 - INFO -    [SUCCESS] Property 49 scraped successfully
2025-08-11 12:35:56,576 - INFO -    [SUCCESS] Property 49/84: Success
2025-08-11 12:35:56,576 - INFO -    [TIMER] Waiting 5.4s before next property...
2025-08-11 12:36:01,977 - INFO -    🔍 Scraping property 50 (attempt 1/3)
2025-08-11 12:36:05,693 - INFO -    [SUCCESS] Property 50 scraped successfully
2025-08-11 12:36:05,693 - INFO -    [SUCCESS] Property 50/84: Success
2025-08-11 12:36:05,694 - INFO -    [BREAK] Batch break: 35s
2025-08-11 12:36:40,694 - INFO - \n[BATCH] Processing batch 6: Properties 51-60
2025-08-11 12:36:40,695 - INFO -    🔍 Scraping property 51 (attempt 1/3)
2025-08-11 12:36:45,223 - INFO -    [SUCCESS] Property 51 scraped successfully
2025-08-11 12:36:45,224 - INFO -    [SUCCESS] Property 51/84: Success
2025-08-11 12:36:45,225 - INFO -    [TIMER] Waiting 4.2s before next property...
2025-08-11 12:36:49,471 - INFO -    🔍 Scraping property 52 (attempt 1/3)
2025-08-11 12:36:57,932 - INFO -    [SUCCESS] Property 52 scraped successfully
2025-08-11 12:36:57,932 - INFO -    [SUCCESS] Property 52/84: Success
2025-08-11 12:36:57,933 - INFO -    [TIMER] Waiting 3.4s before next property...
2025-08-11 12:37:01,371 - INFO -    🔍 Scraping property 53 (attempt 1/3)
2025-08-11 12:37:06,719 - INFO -    [SUCCESS] Property 53 scraped successfully
2025-08-11 12:37:06,719 - INFO -    [SUCCESS] Property 53/84: Success
2025-08-11 12:37:06,719 - INFO -    [TIMER] Waiting 3.0s before next property...
2025-08-11 12:37:09,748 - INFO -    🔍 Scraping property 54 (attempt 1/3)
2025-08-11 12:37:13,442 - INFO -    [SUCCESS] Property 54 scraped successfully
2025-08-11 12:37:13,442 - INFO -    [SUCCESS] Property 54/84: Success
2025-08-11 12:37:13,443 - INFO -    [TIMER] Waiting 5.4s before next property...
2025-08-11 12:37:18,821 - INFO -    🔍 Scraping property 55 (attempt 1/3)
2025-08-11 12:37:22,869 - INFO -    [SUCCESS] Property 55 scraped successfully
2025-08-11 12:37:22,870 - INFO -    [SUCCESS] Property 55/84: Success
2025-08-11 12:37:22,870 - INFO -    [TIMER] Waiting 6.3s before next property...
2025-08-11 12:37:29,165 - INFO -    🔍 Scraping property 56 (attempt 1/3)
2025-08-11 12:37:32,872 - INFO -    [SUCCESS] Property 56 scraped successfully
2025-08-11 12:37:32,872 - INFO -    [SUCCESS] Property 56/84: Success
2025-08-11 12:37:32,873 - INFO -    [TIMER] Waiting 9.0s before next property...
2025-08-11 12:37:41,872 - INFO -    🔍 Scraping property 57 (attempt 1/3)
2025-08-11 12:37:45,284 - INFO -    [SUCCESS] Property 57 scraped successfully
2025-08-11 12:37:45,285 - INFO -    [SUCCESS] Property 57/84: Success
2025-08-11 12:37:45,285 - INFO -    [TIMER] Waiting 3.8s before next property...
2025-08-11 12:37:49,052 - INFO -    🔍 Scraping property 58 (attempt 1/3)
2025-08-11 12:37:53,286 - INFO -    [SUCCESS] Property 58 scraped successfully
2025-08-11 12:37:53,286 - INFO -    [SUCCESS] Property 58/84: Success
2025-08-11 12:37:53,287 - INFO -    [TIMER] Waiting 5.0s before next property...
2025-08-11 12:37:58,333 - INFO -    🔍 Scraping property 59 (attempt 1/3)
2025-08-11 12:38:02,180 - INFO -    [SUCCESS] Property 59 scraped successfully
2025-08-11 12:38:02,181 - INFO -    [SUCCESS] Property 59/84: Success
2025-08-11 12:38:02,181 - INFO -    [TIMER] Waiting 4.3s before next property...
2025-08-11 12:38:06,527 - INFO -    🔍 Scraping property 60 (attempt 1/3)
2025-08-11 12:38:10,489 - INFO -    [SUCCESS] Property 60 scraped successfully
2025-08-11 12:38:10,489 - INFO -    [SUCCESS] Property 60/84: Success
2025-08-11 12:38:10,489 - INFO -    [BREAK] Batch break: 40s
2025-08-11 12:38:50,490 - INFO - \n[BATCH] Processing batch 7: Properties 61-70
2025-08-11 12:38:50,490 - INFO -    🔍 Scraping property 61 (attempt 1/3)
2025-08-11 12:38:54,937 - INFO -    [SUCCESS] Property 61 scraped successfully
2025-08-11 12:38:54,937 - INFO -    [SUCCESS] Property 61/84: Success
2025-08-11 12:38:54,937 - INFO -    [TIMER] Waiting 7.3s before next property...
2025-08-11 12:39:02,210 - INFO -    🔍 Scraping property 62 (attempt 1/3)
2025-08-11 12:39:06,390 - INFO -    [SUCCESS] Property 62 scraped successfully
2025-08-11 12:39:06,391 - INFO -    [SUCCESS] Property 62/84: Success
2025-08-11 12:39:06,391 - INFO -    [TIMER] Waiting 5.8s before next property...
2025-08-11 12:39:12,161 - INFO -    🔍 Scraping property 63 (attempt 1/3)
2025-08-11 12:39:15,325 - INFO -    [SUCCESS] Property 63 scraped successfully
2025-08-11 12:39:15,325 - INFO -    [SUCCESS] Property 63/84: Success
2025-08-11 12:39:15,326 - INFO -    [TIMER] Waiting 3.1s before next property...
2025-08-11 12:39:18,454 - INFO -    🔍 Scraping property 64 (attempt 1/3)
2025-08-11 12:39:22,538 - WARNING -    ⚠️ Poor data quality on attempt 1
2025-08-11 12:39:22,538 - INFO -    🔍 Scraping property 64 (attempt 2/3)
2025-08-11 12:39:26,321 - WARNING -    ⚠️ Poor data quality on attempt 2
2025-08-11 12:39:26,321 - INFO -    🔍 Scraping property 64 (attempt 3/3)
2025-08-11 12:39:31,115 - WARNING -    ⚠️ Poor data quality on attempt 3
2025-08-11 12:39:31,116 - INFO -    📝 Returning partial data for property 64
2025-08-11 12:39:31,116 - INFO -    [SUCCESS] Property 64/84: Success
2025-08-11 12:39:31,116 - INFO -    [TIMER] Waiting 3.6s before next property...
2025-08-11 12:39:34,679 - INFO -    🔍 Scraping property 65 (attempt 1/3)
2025-08-11 12:39:37,743 - INFO -    [SUCCESS] Property 65 scraped successfully
2025-08-11 12:39:37,744 - INFO -    [SUCCESS] Property 65/84: Success
2025-08-11 12:39:37,744 - INFO -    [TIMER] Waiting 8.9s before next property...
2025-08-11 12:39:46,679 - INFO -    🔍 Scraping property 66 (attempt 1/3)
2025-08-11 12:39:49,427 - INFO -    [SUCCESS] Property 66 scraped successfully
2025-08-11 12:39:49,427 - INFO -    [SUCCESS] Property 66/84: Success
2025-08-11 12:39:49,428 - INFO -    [TIMER] Waiting 8.8s before next property...
2025-08-11 12:39:58,247 - INFO -    🔍 Scraping property 67 (attempt 1/3)
2025-08-11 12:40:00,967 - INFO -    [SUCCESS] Property 67 scraped successfully
2025-08-11 12:40:00,968 - INFO -    [SUCCESS] Property 67/84: Success
2025-08-11 12:40:00,968 - INFO -    [TIMER] Waiting 7.7s before next property...
2025-08-11 12:40:08,670 - INFO -    🔍 Scraping property 68 (attempt 1/3)
2025-08-11 12:40:11,799 - INFO -    [SUCCESS] Property 68 scraped successfully
2025-08-11 12:40:11,799 - INFO -    [SUCCESS] Property 68/84: Success
2025-08-11 12:40:11,799 - INFO -    [TIMER] Waiting 8.2s before next property...
2025-08-11 12:40:19,986 - INFO -    🔍 Scraping property 69 (attempt 1/3)
2025-08-11 12:40:23,029 - INFO -    [SUCCESS] Property 69 scraped successfully
2025-08-11 12:40:23,030 - INFO -    [SUCCESS] Property 69/84: Success
2025-08-11 12:40:23,030 - INFO -    [TIMER] Waiting 9.3s before next property...
2025-08-11 12:40:32,290 - INFO -    🔍 Scraping property 70 (attempt 1/3)
2025-08-11 12:40:36,144 - INFO -    [SUCCESS] Property 70 scraped successfully
2025-08-11 12:40:36,144 - INFO -    [SUCCESS] Property 70/84: Success
2025-08-11 12:40:36,145 - INFO -    [BREAK] Batch break: 45s
2025-08-11 12:41:21,146 - INFO - \n[BATCH] Processing batch 8: Properties 71-80
2025-08-11 12:41:21,147 - INFO -    🔍 Scraping property 71 (attempt 1/3)
2025-08-11 12:41:26,008 - INFO -    [SUCCESS] Property 71 scraped successfully
2025-08-11 12:41:26,008 - INFO -    [SUCCESS] Property 71/84: Success
2025-08-11 12:41:26,008 - INFO -    [TIMER] Waiting 7.5s before next property...
2025-08-11 12:41:33,556 - INFO -    🔍 Scraping property 72 (attempt 1/3)
2025-08-11 12:41:37,988 - INFO -    [SUCCESS] Property 72 scraped successfully
2025-08-11 12:41:37,988 - INFO -    [SUCCESS] Property 72/84: Success
2025-08-11 12:41:37,989 - INFO -    [TIMER] Waiting 3.9s before next property...
2025-08-11 12:41:41,846 - INFO -    🔍 Scraping property 73 (attempt 1/3)
2025-08-11 12:41:46,517 - INFO -    [SUCCESS] Property 73 scraped successfully
2025-08-11 12:41:46,518 - INFO -    [SUCCESS] Property 73/84: Success
2025-08-11 12:41:46,518 - INFO -    [TIMER] Waiting 7.5s before next property...
2025-08-11 12:41:54,000 - INFO -    🔍 Scraping property 74 (attempt 1/3)
2025-08-11 12:41:58,023 - INFO -    [SUCCESS] Property 74 scraped successfully
2025-08-11 12:41:58,023 - INFO -    [SUCCESS] Property 74/84: Success
2025-08-11 12:41:58,024 - INFO -    [TIMER] Waiting 6.4s before next property...
2025-08-11 12:42:04,388 - INFO -    🔍 Scraping property 75 (attempt 1/3)
2025-08-11 12:42:08,362 - INFO -    [SUCCESS] Property 75 scraped successfully
2025-08-11 12:42:08,363 - INFO -    [SUCCESS] Property 75/84: Success
2025-08-11 12:42:08,363 - INFO -    [TIMER] Waiting 5.7s before next property...
2025-08-11 12:42:14,030 - INFO -    🔍 Scraping property 76 (attempt 1/3)
2025-08-11 12:42:17,805 - INFO -    [SUCCESS] Property 76 scraped successfully
2025-08-11 12:42:17,806 - INFO -    [SUCCESS] Property 76/84: Success
2025-08-11 12:42:17,806 - INFO -    [TIMER] Waiting 6.8s before next property...
2025-08-11 12:42:24,624 - INFO -    🔍 Scraping property 77 (attempt 1/3)
2025-08-11 12:42:28,402 - INFO -    [SUCCESS] Property 77 scraped successfully
2025-08-11 12:42:28,402 - INFO -    [SUCCESS] Property 77/84: Success
2025-08-11 12:42:28,403 - INFO -    [TIMER] Waiting 8.6s before next property...
2025-08-11 12:42:36,984 - INFO -    🔍 Scraping property 78 (attempt 1/3)
2025-08-11 12:42:41,126 - WARNING -    ⚠️ Poor data quality on attempt 1
2025-08-11 12:42:41,127 - INFO -    🔍 Scraping property 78 (attempt 2/3)
2025-08-11 12:42:44,943 - WARNING -    ⚠️ Poor data quality on attempt 2
2025-08-11 12:42:44,944 - INFO -    🔍 Scraping property 78 (attempt 3/3)
2025-08-11 12:42:49,834 - WARNING -    ⚠️ Poor data quality on attempt 3
2025-08-11 12:42:49,834 - INFO -    📝 Returning partial data for property 78
2025-08-11 12:42:49,835 - INFO -    [SUCCESS] Property 78/84: Success
2025-08-11 12:42:49,835 - INFO -    [TIMER] Waiting 9.4s before next property...
2025-08-11 12:42:59,191 - INFO -    🔍 Scraping property 79 (attempt 1/3)
2025-08-11 12:43:02,304 - INFO -    [SUCCESS] Property 79 scraped successfully
2025-08-11 12:43:02,304 - INFO -    [SUCCESS] Property 79/84: Success
2025-08-11 12:43:02,304 - INFO -    [TIMER] Waiting 6.8s before next property...
2025-08-11 12:43:09,065 - INFO -    🔍 Scraping property 80 (attempt 1/3)
2025-08-11 12:43:12,370 - INFO -    [SUCCESS] Property 80 scraped successfully
2025-08-11 12:43:12,371 - INFO -    [SUCCESS] Property 80/84: Success
2025-08-11 12:43:12,371 - INFO -    [BREAK] Batch break: 50s
2025-08-11 12:44:02,372 - INFO - \n[BATCH] Processing batch 9: Properties 81-84
2025-08-11 12:44:02,373 - INFO -    🔍 Scraping property 81 (attempt 1/3)
2025-08-11 12:44:05,858 - INFO -    [SUCCESS] Property 81 scraped successfully
2025-08-11 12:44:05,859 - INFO -    [SUCCESS] Property 81/84: Success
2025-08-11 12:44:05,859 - INFO -    [TIMER] Waiting 3.3s before next property...
2025-08-11 12:44:09,197 - INFO -    🔍 Scraping property 82 (attempt 1/3)
2025-08-11 12:44:12,810 - INFO -    [SUCCESS] Property 82 scraped successfully
2025-08-11 12:44:12,811 - INFO -    [SUCCESS] Property 82/84: Success
2025-08-11 12:44:12,811 - INFO -    [TIMER] Waiting 4.6s before next property...
2025-08-11 12:44:17,448 - INFO -    🔍 Scraping property 83 (attempt 1/3)
2025-08-11 12:44:21,929 - INFO -    [SUCCESS] Property 83 scraped successfully
2025-08-11 12:44:21,929 - INFO -    [SUCCESS] Property 83/84: Success
2025-08-11 12:44:21,930 - INFO -    [TIMER] Waiting 3.1s before next property...
2025-08-11 12:44:25,001 - INFO -    🔍 Scraping property 84 (attempt 1/3)
2025-08-11 12:44:28,186 - INFO -    [SUCCESS] Property 84 scraped successfully
2025-08-11 12:44:28,187 - INFO -    [SUCCESS] Property 84/84: Success
2025-08-11 12:44:28,187 - INFO - \n[COMPLETE] Individual property scraping complete: 84/84 successful
2025-08-11 12:44:28,253 - INFO -    [SAVE] CSV updated with detailed property information
2025-08-11 12:44:28,254 - INFO -    [SUCCESS] Updated CSV with 84 detailed properties
2025-08-11 12:44:30,525 - INFO - WebDriver closed
2025-08-12 11:02:16,986 - INFO - Chrome WebDriver initialized successfully
2025-08-12 11:02:18,678 - INFO - [TIMER] Waiting 2.5 seconds before next page...
2025-08-12 11:02:22,607 - INFO - [TIMER] Waiting 2.6 seconds before next page...
2025-08-12 11:02:27,360 - INFO - [TIMER] Waiting 4.6 seconds before next page...
2025-08-12 11:02:33,272 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:02:33,272 - INFO -    [LIST] Total properties: 82
2025-08-12 11:02:33,272 - INFO -    [SUCCESS] Valid properties: 82
2025-08-12 11:02:33,272 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:02:33,272 - INFO -    [COMPLETE] Average data quality: 98.7%
2025-08-12 11:02:35,389 - INFO - WebDriver closed
2025-08-12 11:02:55,781 - INFO - WebDriver closed
2025-08-12 11:06:19,532 - INFO - Chrome WebDriver initialized successfully
2025-08-12 11:06:21,163 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:06:21,164 - INFO -    [LIST] Total properties: 22
2025-08-12 11:06:21,164 - INFO -    [SUCCESS] Valid properties: 22
2025-08-12 11:06:21,164 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:06:21,164 - INFO -    [COMPLETE] Average data quality: 97.6%
2025-08-12 11:06:23,243 - INFO - WebDriver closed
2025-08-12 11:06:43,634 - INFO - WebDriver closed
2025-08-12 11:06:57,419 - INFO - Chrome WebDriver initialized successfully
2025-08-12 11:06:58,953 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:06:58,953 - INFO -    [LIST] Total properties: 22
2025-08-12 11:06:58,953 - INFO -    [SUCCESS] Valid properties: 22
2025-08-12 11:06:58,953 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:06:58,953 - INFO -    [COMPLETE] Average data quality: 97.6%
2025-08-12 11:07:01,037 - INFO - WebDriver closed
2025-08-12 11:07:21,451 - INFO - WebDriver closed
2025-08-12 11:08:45,653 - INFO - Chrome WebDriver initialized successfully
2025-08-12 11:08:47,177 - INFO - [TIMER] Waiting 4.4 seconds before next page...
2025-08-12 11:08:53,044 - INFO - [TIMER] Waiting 2.5 seconds before next page...
2025-08-12 11:08:56,904 - INFO - [TIMER] Waiting 3.3 seconds before next page...
2025-08-12 11:09:01,834 - INFO - [TIMER] Waiting 4.1 seconds before next page...
2025-08-12 11:09:07,297 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 11:09:12,261 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:09:12,261 - INFO -    [LIST] Total properties: 115
2025-08-12 11:09:12,261 - INFO -    [SUCCESS] Valid properties: 115
2025-08-12 11:09:12,261 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:09:12,261 - INFO -    [COMPLETE] Average data quality: 88.3%
2025-08-12 11:09:12,268 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:09:12,268 - INFO - ============================================================
2025-08-12 11:09:12,268 - INFO -    [LIST] Found 115 property URLs for detailed scraping
2025-08-12 11:09:12,268 - INFO - [HOUSE] Starting individual property page scraping for 115 properties
2025-08-12 11:09:12,268 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:09:12,268 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:09:12,269 - INFO - \n[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:09:12,269 - INFO -    🔍 Scraping property 1 (attempt 1/3)
2025-08-12 11:09:15,866 - INFO -    [SUCCESS] Property 1 scraped successfully
2025-08-12 11:09:15,866 - INFO -    [SUCCESS] Property 1/115: Success
2025-08-12 11:09:15,867 - INFO -    [TIMER] Waiting 16.7s before next property...
2025-08-12 11:09:32,571 - INFO -    🔍 Scraping property 2 (attempt 1/3)
2025-08-12 11:09:36,036 - INFO -    [SUCCESS] Property 2 scraped successfully
2025-08-12 11:09:36,037 - INFO -    [SUCCESS] Property 2/115: Success
2025-08-12 11:09:36,037 - INFO -    [TIMER] Waiting 17.2s before next property...
2025-08-12 11:09:53,225 - INFO -    🔍 Scraping property 3 (attempt 1/3)
2025-08-12 11:09:56,453 - INFO -    [SUCCESS] Property 3 scraped successfully
2025-08-12 11:09:56,453 - INFO -    [SUCCESS] Property 3/115: Success
2025-08-12 11:09:56,454 - INFO -    [TIMER] Waiting 8.0s before next property...
2025-08-12 11:10:04,501 - INFO -    🔍 Scraping property 4 (attempt 1/3)
2025-08-12 11:10:10,488 - INFO -    [SUCCESS] Property 4 scraped successfully
2025-08-12 11:10:10,488 - INFO -    [SUCCESS] Property 4/115: Success
2025-08-12 11:10:10,488 - INFO -    [TIMER] Waiting 10.0s before next property...
2025-08-12 11:10:20,503 - INFO -    🔍 Scraping property 5 (attempt 1/3)
2025-08-12 11:10:25,982 - INFO -    [SUCCESS] Property 5 scraped successfully
2025-08-12 11:10:25,982 - INFO -    [SUCCESS] Property 5/115: Success
2025-08-12 11:10:25,982 - INFO -    [TIMER] Waiting 16.2s before next property...
2025-08-12 11:10:42,142 - INFO -    🔍 Scraping property 6 (attempt 1/3)
2025-08-12 11:10:46,235 - INFO -    [SUCCESS] Property 6 scraped successfully
2025-08-12 11:10:46,235 - INFO -    [SUCCESS] Property 6/115: Success
2025-08-12 11:10:46,235 - INFO -    [TIMER] Waiting 15.4s before next property...
2025-08-12 11:11:01,658 - INFO -    🔍 Scraping property 7 (attempt 1/3)
2025-08-12 11:11:05,988 - INFO -    [SUCCESS] Property 7 scraped successfully
2025-08-12 11:11:05,988 - INFO -    [SUCCESS] Property 7/115: Success
2025-08-12 11:11:05,989 - INFO -    [TIMER] Waiting 8.9s before next property...
2025-08-12 11:11:14,864 - INFO -    🔍 Scraping property 8 (attempt 1/3)
2025-08-12 11:11:18,806 - INFO -    [SUCCESS] Property 8 scraped successfully
2025-08-12 11:11:18,806 - INFO -    [SUCCESS] Property 8/115: Success
2025-08-12 11:11:18,806 - INFO -    [TIMER] Waiting 6.2s before next property...
2025-08-12 11:11:24,963 - INFO -    🔍 Scraping property 9 (attempt 1/3)
2025-08-12 11:11:28,452 - INFO -    [SUCCESS] Property 9 scraped successfully
2025-08-12 11:11:28,452 - INFO -    [SUCCESS] Property 9/115: Success
2025-08-12 11:11:28,453 - INFO -    [TIMER] Waiting 8.0s before next property...
2025-08-12 11:11:36,419 - INFO -    🔍 Scraping property 10 (attempt 1/3)
2025-08-12 11:11:40,710 - INFO -    [SUCCESS] Property 10 scraped successfully
2025-08-12 11:11:40,710 - INFO -    [SUCCESS] Property 10/115: Success
2025-08-12 11:11:40,710 - INFO -    [BREAK] Batch break: 15s
2025-08-12 11:11:55,711 - INFO - \n[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:11:55,711 - INFO -    🔍 Scraping property 11 (attempt 1/3)
2025-08-12 11:11:59,755 - INFO -    [SUCCESS] Property 11 scraped successfully
2025-08-12 11:11:59,755 - INFO -    [SUCCESS] Property 11/115: Success
2025-08-12 11:11:59,756 - INFO -    [TIMER] Waiting 18.1s before next property...
2025-08-12 11:12:17,894 - INFO -    🔍 Scraping property 12 (attempt 1/3)
2025-08-12 11:12:20,558 - INFO -    [SUCCESS] Property 12 scraped successfully
2025-08-12 11:12:20,558 - INFO -    [SUCCESS] Property 12/115: Success
2025-08-12 11:12:20,559 - INFO -    [TIMER] Waiting 19.6s before next property...
2025-08-12 11:12:40,175 - INFO -    🔍 Scraping property 13 (attempt 1/3)
2025-08-12 11:12:43,798 - INFO -    [SUCCESS] Property 13 scraped successfully
2025-08-12 11:12:43,798 - INFO -    [SUCCESS] Property 13/115: Success
2025-08-12 11:12:43,798 - INFO -    [TIMER] Waiting 4.2s before next property...
2025-08-12 11:12:48,015 - INFO -    🔍 Scraping property 14 (attempt 1/3)
2025-08-12 11:12:52,267 - INFO -    [SUCCESS] Property 14 scraped successfully
2025-08-12 11:12:52,267 - INFO -    [SUCCESS] Property 14/115: Success
2025-08-12 11:12:52,267 - INFO -    [TIMER] Waiting 7.8s before next property...
2025-08-12 11:13:00,117 - INFO -    🔍 Scraping property 15 (attempt 1/3)
2025-08-12 11:13:03,533 - INFO -    [SUCCESS] Property 15 scraped successfully
2025-08-12 11:13:03,533 - INFO -    [SUCCESS] Property 15/115: Success
2025-08-12 11:13:03,534 - INFO -    [TIMER] Waiting 7.2s before next property...
2025-08-12 11:13:10,773 - INFO -    🔍 Scraping property 16 (attempt 1/3)
2025-08-12 11:13:13,494 - INFO -    [SUCCESS] Property 16 scraped successfully
2025-08-12 11:13:13,494 - INFO -    [SUCCESS] Property 16/115: Success
2025-08-12 11:13:13,495 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:13:33,495 - INFO -    🔍 Scraping property 17 (attempt 1/3)
2025-08-12 11:13:38,798 - INFO -    [SUCCESS] Property 17 scraped successfully
2025-08-12 11:13:38,799 - INFO -    [SUCCESS] Property 17/115: Success
2025-08-12 11:13:38,799 - INFO -    [TIMER] Waiting 5.2s before next property...
2025-08-12 11:13:43,988 - INFO -    🔍 Scraping property 18 (attempt 1/3)
2025-08-12 11:13:46,771 - INFO -    [SUCCESS] Property 18 scraped successfully
2025-08-12 11:13:46,772 - INFO -    [SUCCESS] Property 18/115: Success
2025-08-12 11:13:46,772 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:14:06,773 - INFO -    🔍 Scraping property 19 (attempt 1/3)
2025-08-12 11:14:10,444 - INFO -    [SUCCESS] Property 19 scraped successfully
2025-08-12 11:14:10,445 - INFO -    [SUCCESS] Property 19/115: Success
2025-08-12 11:14:10,445 - INFO -    [TIMER] Waiting 8.8s before next property...
2025-08-12 11:14:19,249 - INFO -    🔍 Scraping property 20 (attempt 1/3)
2025-08-12 11:14:23,846 - INFO -    [SUCCESS] Property 20 scraped successfully
2025-08-12 11:14:23,847 - INFO -    [SUCCESS] Property 20/115: Success
2025-08-12 11:14:23,847 - INFO -    [BREAK] Batch break: 20s
2025-08-12 11:14:43,847 - INFO - \n[BATCH] Processing batch 3: Properties 21-30
2025-08-12 11:14:43,847 - INFO -    🔍 Scraping property 21 (attempt 1/3)
2025-08-12 11:14:45,301 - WARNING -    [ALERT] Bot detection on property 21 (attempt 1)
2025-08-12 11:14:45,301 - WARNING - [ALERT] Bot detection #1 - Implementing recovery strategy
2025-08-12 11:14:45,302 - INFO -    [RETRY] Strategy 1: Extended delay (45s) + User agent rotation
2025-08-12 11:15:34,445 - ERROR -    [ERROR] Failed to restart browser session: 'IntegratedMagicBricksScraper' object has no attribute '_setup_webdriver'
2025-08-12 11:15:34,445 - INFO -    🔍 Scraping property 21 (attempt 2/3)
2025-08-12 11:15:50,737 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E312442B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:16:00,738 - INFO -    🔍 Scraping property 21 (attempt 3/3)
2025-08-12 11:16:17,061 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A08D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:16:17,062 - ERROR -    [ERROR] Error scraping property 21 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A08D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:16:17,062 - ERROR -    [ERROR] Failed to scrape property 21 after 3 attempts
2025-08-12 11:16:17,062 - WARNING -    [ERROR] Property 21/115: Failed
2025-08-12 11:16:17,062 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:16:37,063 - INFO -    🔍 Scraping property 22 (attempt 1/3)
2025-08-12 11:16:53,394 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A1260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:16:58,395 - INFO -    🔍 Scraping property 22 (attempt 2/3)
2025-08-12 11:17:14,740 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E2DAA79B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:17:24,740 - INFO -    🔍 Scraping property 22 (attempt 3/3)
2025-08-12 11:17:41,067 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:17:41,067 - ERROR -    [ERROR] Error scraping property 22 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:17:41,068 - ERROR -    [ERROR] Failed to scrape property 22 after 3 attempts
2025-08-12 11:17:41,068 - WARNING -    [ERROR] Property 22/115: Failed
2025-08-12 11:17:41,068 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:18:01,068 - INFO -    🔍 Scraping property 23 (attempt 1/3)
2025-08-12 11:18:17,385 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0380>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:18:22,385 - INFO -    🔍 Scraping property 23 (attempt 2/3)
2025-08-12 11:18:38,700 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A1F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:18:48,701 - INFO -    🔍 Scraping property 23 (attempt 3/3)
2025-08-12 11:19:05,016 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A28B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:19:05,016 - ERROR -    [ERROR] Error scraping property 23 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A28B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:19:05,016 - ERROR -    [ERROR] Failed to scrape property 23 after 3 attempts
2025-08-12 11:19:05,016 - WARNING -    [ERROR] Property 23/115: Failed
2025-08-12 11:19:05,016 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:19:25,018 - INFO -    🔍 Scraping property 24 (attempt 1/3)
2025-08-12 11:19:41,344 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:19:46,345 - INFO -    🔍 Scraping property 24 (attempt 2/3)
2025-08-12 11:20:02,698 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:20:12,698 - INFO -    🔍 Scraping property 24 (attempt 3/3)
2025-08-12 11:20:29,016 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E85A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:20:29,016 - ERROR -    [ERROR] Error scraping property 24 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E85A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:20:29,017 - ERROR -    [ERROR] Failed to scrape property 24 after 3 attempts
2025-08-12 11:20:29,017 - WARNING -    [ERROR] Property 24/115: Failed
2025-08-12 11:20:29,017 - INFO -    [TIMER] Waiting 9.6s before next property...
2025-08-12 11:20:38,647 - INFO -    🔍 Scraping property 25 (attempt 1/3)
2025-08-12 11:20:54,983 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E8F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:20:59,984 - INFO -    🔍 Scraping property 25 (attempt 2/3)
2025-08-12 11:21:16,318 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E98C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:21:26,319 - INFO -    🔍 Scraping property 25 (attempt 3/3)
2025-08-12 11:21:42,661 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A39B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:21:42,661 - ERROR -    [ERROR] Error scraping property 25 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A39B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:21:42,661 - ERROR -    [ERROR] Failed to scrape property 25 after 3 attempts
2025-08-12 11:21:42,662 - WARNING -    [ERROR] Property 25/115: Failed
2025-08-12 11:21:42,662 - INFO -    [TIMER] Waiting 12.3s before next property...
2025-08-12 11:21:54,962 - INFO -    🔍 Scraping property 26 (attempt 1/3)
2025-08-12 11:22:11,300 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:22:16,300 - INFO -    🔍 Scraping property 26 (attempt 2/3)
2025-08-12 11:22:32,633 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2690>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:22:42,634 - INFO -    🔍 Scraping property 26 (attempt 3/3)
2025-08-12 11:22:58,970 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A1D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:22:58,971 - ERROR -    [ERROR] Error scraping property 26 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A1D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:22:58,971 - ERROR -    [ERROR] Failed to scrape property 26 after 3 attempts
2025-08-12 11:22:58,971 - WARNING -    [ERROR] Property 26/115: Failed
2025-08-12 11:22:58,971 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:23:18,972 - INFO -    🔍 Scraping property 27 (attempt 1/3)
2025-08-12 11:23:35,302 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A06B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:23:40,302 - INFO -    🔍 Scraping property 27 (attempt 2/3)
2025-08-12 11:23:56,589 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:24:06,590 - INFO -    🔍 Scraping property 27 (attempt 3/3)
2025-08-12 11:24:22,909 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E2DAA7CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:24:22,910 - ERROR -    [ERROR] Error scraping property 27 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E2DAA7CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:24:22,910 - ERROR -    [ERROR] Failed to scrape property 27 after 3 attempts
2025-08-12 11:24:22,910 - WARNING -    [ERROR] Property 27/115: Failed
2025-08-12 11:24:22,910 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:24:42,911 - INFO -    🔍 Scraping property 28 (attempt 1/3)
2025-08-12 11:24:59,245 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E9590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:25:04,246 - INFO -    🔍 Scraping property 28 (attempt 2/3)
2025-08-12 11:25:20,575 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E8C00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:25:30,576 - INFO -    🔍 Scraping property 28 (attempt 3/3)
2025-08-12 11:25:46,878 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E8270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:25:46,879 - ERROR -    [ERROR] Error scraping property 28 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E8270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:25:46,879 - ERROR -    [ERROR] Failed to scrape property 28 after 3 attempts
2025-08-12 11:25:46,879 - WARNING -    [ERROR] Property 28/115: Failed
2025-08-12 11:25:46,880 - INFO -    [TIMER] Waiting 8.8s before next property...
2025-08-12 11:25:55,719 - INFO -    🔍 Scraping property 29 (attempt 1/3)
2025-08-12 11:26:12,055 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310EA580>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:26:17,055 - INFO -    🔍 Scraping property 29 (attempt 2/3)
2025-08-12 11:26:33,396 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A1480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:26:43,396 - INFO -    🔍 Scraping property 29 (attempt 3/3)
2025-08-12 11:26:59,714 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:26:59,714 - ERROR -    [ERROR] Error scraping property 29 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:26:59,714 - ERROR -    [ERROR] Failed to scrape property 29 after 3 attempts
2025-08-12 11:26:59,715 - WARNING -    [ERROR] Property 29/115: Failed
2025-08-12 11:26:59,715 - INFO -    [TIMER] Waiting 20.0s before next property...
2025-08-12 11:27:19,715 - INFO -    🔍 Scraping property 30 (attempt 1/3)
2025-08-12 11:27:36,040 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:27:41,041 - INFO -    🔍 Scraping property 30 (attempt 2/3)
2025-08-12 11:27:57,365 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:28:07,366 - INFO -    🔍 Scraping property 30 (attempt 3/3)
2025-08-12 11:28:23,740 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2AD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:28:23,740 - ERROR -    [ERROR] Error scraping property 30 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2AD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:28:23,740 - ERROR -    [ERROR] Failed to scrape property 30 after 3 attempts
2025-08-12 11:28:23,740 - WARNING -    [ERROR] Property 30/115: Failed
2025-08-12 11:28:23,740 - INFO -    [BREAK] Batch break: 25s
2025-08-12 11:28:48,741 - INFO - \n[BATCH] Processing batch 4: Properties 31-40
2025-08-12 11:28:48,741 - INFO -    🔍 Scraping property 31 (attempt 1/3)
2025-08-12 11:29:05,069 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:29:10,069 - INFO -    🔍 Scraping property 31 (attempt 2/3)
2025-08-12 11:29:26,361 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E9E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:29:36,361 - INFO -    🔍 Scraping property 31 (attempt 3/3)
2025-08-12 11:29:52,674 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E89E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:29:52,674 - ERROR -    [ERROR] Error scraping property 31 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E89E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:29:52,674 - ERROR -    [ERROR] Failed to scrape property 31 after 3 attempts
2025-08-12 11:29:52,674 - WARNING -    [ERROR] Property 31/115: Failed
2025-08-12 11:29:52,675 - INFO -    [TIMER] Waiting 19.6s before next property...
2025-08-12 11:30:12,270 - INFO -    🔍 Scraping property 32 (attempt 1/3)
2025-08-12 11:30:28,590 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310E9370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:30:33,591 - INFO -    🔍 Scraping property 32 (attempt 2/3)
2025-08-12 11:30:49,907 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E310EA8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:30:59,908 - INFO -    🔍 Scraping property 32 (attempt 3/3)
2025-08-12 11:31:16,240 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:31:16,241 - ERROR -    [ERROR] Error scraping property 32 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:31:16,241 - ERROR -    [ERROR] Failed to scrape property 32 after 3 attempts
2025-08-12 11:31:16,241 - WARNING -    [ERROR] Property 32/115: Failed
2025-08-12 11:31:16,241 - INFO -    [TIMER] Waiting 17.4s before next property...
2025-08-12 11:31:33,612 - INFO -    🔍 Scraping property 33 (attempt 1/3)
2025-08-12 11:31:49,949 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A3350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:31:54,950 - INFO -    🔍 Scraping property 33 (attempt 2/3)
2025-08-12 11:32:11,301 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A29C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:32:21,301 - INFO -    🔍 Scraping property 33 (attempt 3/3)
2025-08-12 11:32:37,623 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:32:37,624 - ERROR -    [ERROR] Error scraping property 33 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A2030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:32:37,624 - ERROR -    [ERROR] Failed to scrape property 33 after 3 attempts
2025-08-12 11:32:37,624 - WARNING -    [ERROR] Property 33/115: Failed
2025-08-12 11:32:37,624 - INFO -    [TIMER] Waiting 13.9s before next property...
2025-08-12 11:32:51,561 - INFO -    🔍 Scraping property 34 (attempt 1/3)
2025-08-12 11:33:07,887 - WARNING -    ⚠️ Navigation error on attempt 1: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:33:12,888 - INFO -    🔍 Scraping property 34 (attempt 2/3)
2025-08-12 11:33:29,211 - WARNING -    ⚠️ Navigation error on attempt 2: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A0AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:33:39,211 - INFO -    🔍 Scraping property 34 (attempt 3/3)
2025-08-12 11:33:55,535 - WARNING -    ⚠️ Navigation error on attempt 3: HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A17B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:33:55,535 - ERROR -    [ERROR] Error scraping property 34 (attempt 3): HTTPConnectionPool(host='localhost', port=58481): Max retries exceeded with url: /session/7f0b943593bb67529a135397bb861325/timeouts (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025E313A17B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-12 11:33:55,536 - ERROR -    [ERROR] Failed to scrape property 34 after 3 attempts
2025-08-12 11:33:55,536 - WARNING -    [ERROR] Property 34/115: Failed
2025-08-12 11:33:55,536 - INFO -    [TIMER] Waiting 14.7s before next property...
2025-08-12 11:40:48,703 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:40:50,397 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 11:40:57,001 - INFO - [TIMER] Waiting 4.0 seconds before next page...
2025-08-12 11:41:02,631 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 11:41:05,755 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:41:05,755 - INFO -    [LIST] Total properties: 76
2025-08-12 11:41:05,755 - INFO -    [SUCCESS] Valid properties: 76
2025-08-12 11:41:05,755 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:41:05,755 - INFO -    [COMPLETE] Average data quality: 85.5%
2025-08-12 11:41:05,761 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:41:05,761 - INFO - ============================================================
2025-08-12 11:41:05,761 - INFO -    [LIST] Found 76 property URLs for detailed scraping
2025-08-12 11:41:05,762 - INFO - [HOUSE] Starting individual property page scraping for 76 properties
2025-08-12 11:41:05,762 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:41:05,762 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:41:05,762 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:41:05,762 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:41:05,762 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:41:11,018 - ERROR - Error scraping property 6: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:11,018 - INFO -    [SUCCESS] Property 1/76: Failed
2025-08-12 11:41:11,208 - ERROR - Error scraping property 4: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:11,208 - INFO -    [SUCCESS] Property 2/76: Failed
2025-08-12 11:41:15,215 - ERROR - Error scraping property 7: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:15,215 - INFO -    [SUCCESS] Property 3/76: Failed
2025-08-12 11:41:15,412 - ERROR - Error scraping property 2: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:15,412 - INFO -    [SUCCESS] Property 4/76: Failed
2025-08-12 11:41:15,414 - ERROR - Error scraping property 5: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:15,415 - INFO -    [SUCCESS] Property 5/76: Failed
2025-08-12 11:41:15,515 - ERROR - Error scraping property 0: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:15,515 - INFO -    [SUCCESS] Property 6/76: Failed
2025-08-12 11:41:18,882 - ERROR - Error scraping property 1: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:18,882 - INFO -    [SUCCESS] Property 7/76: Failed
2025-08-12 11:41:20,107 - ERROR - Error scraping property 3: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:20,107 - INFO -    [SUCCESS] Property 8/76: Failed
2025-08-12 11:41:24,949 - ERROR - Error scraping property 8: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:24,949 - INFO -    [SUCCESS] Property 9/76: Failed
2025-08-12 11:41:26,430 - ERROR - Error scraping property 9: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:26,430 - INFO -    [SUCCESS] Property 10/76: Failed
2025-08-12 11:41:32,599 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:41:37,600 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:41:43,868 - ERROR - Error scraping property 12: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:43,869 - INFO -    [SUCCESS] Property 11/76: Failed
2025-08-12 11:41:44,340 - ERROR - Error scraping property 10: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:44,341 - INFO -    [SUCCESS] Property 12/76: Failed
2025-08-12 11:41:44,883 - ERROR - Error scraping property 14: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:44,884 - INFO -    [SUCCESS] Property 13/76: Failed
2025-08-12 11:41:46,288 - ERROR - Error scraping property 16: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:46,289 - INFO -    [SUCCESS] Property 14/76: Failed
2025-08-12 11:41:47,585 - ERROR - Error scraping property 15: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:47,585 - INFO -    [SUCCESS] Property 15/76: Failed
2025-08-12 11:41:47,748 - ERROR - Error scraping property 11: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:47,748 - INFO -    [SUCCESS] Property 16/76: Failed
2025-08-12 11:41:48,558 - ERROR - Error scraping property 13: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:48,559 - INFO -    [SUCCESS] Property 17/76: Failed
2025-08-12 11:41:51,376 - ERROR - Error scraping property 17: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:51,377 - INFO -    [SUCCESS] Property 18/76: Failed
2025-08-12 11:41:59,983 - ERROR - Error scraping property 18: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:41:59,984 - INFO -    [SUCCESS] Property 19/76: Failed
2025-08-12 11:42:02,613 - ERROR - Error scraping property 19: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:42:02,614 - INFO -    [SUCCESS] Property 20/76: Failed
2025-08-12 11:42:08,737 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:42:13,738 - INFO - 
[BATCH] Processing batch 3: Properties 21-30
2025-08-12 11:42:17,516 - ERROR - Error scraping property 22: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:42:17,517 - INFO -    [SUCCESS] Property 21/76: Failed
2025-08-12 11:42:20,147 - ERROR - Error scraping property 20: 'IntegratedMagicBricksScraper' object has no attribute '_detect_bot_protection'
2025-08-12 11:42:20,148 - INFO -    [SUCCESS] Property 22/76: Failed
2025-08-12 11:42:37,511 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:42:39,781 - INFO - [TIMER] Waiting 3.6 seconds before next page...
2025-08-12 11:42:44,672 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 11:42:48,995 - INFO - [TIMER] Waiting 2.2 seconds before next page...
2025-08-12 11:42:51,206 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:42:51,206 - INFO -    [LIST] Total properties: 76
2025-08-12 11:42:51,206 - INFO -    [SUCCESS] Valid properties: 76
2025-08-12 11:42:51,206 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:42:51,206 - INFO -    [COMPLETE] Average data quality: 85.5%
2025-08-12 11:42:51,212 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:42:51,212 - INFO - ============================================================
2025-08-12 11:42:51,212 - INFO -    [LIST] Found 76 property URLs for detailed scraping
2025-08-12 11:42:51,213 - INFO - [HOUSE] Starting individual property page scraping for 76 properties
2025-08-12 11:42:51,213 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:42:51,213 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:42:51,213 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:42:51,213 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:42:51,214 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:42:57,080 - WARNING - Bot detection on property 0, applying recovery
2025-08-12 11:43:00,472 - WARNING - Bot detection on property 4, applying recovery
2025-08-12 11:43:01,477 - WARNING - Bot detection on property 6, applying recovery
2025-08-12 11:43:01,640 - WARNING - Bot detection on property 2, applying recovery
2025-08-12 11:43:04,252 - INFO -    [SUCCESS] Property 1/76: Failed
2025-08-12 11:43:05,800 - INFO -    [SUCCESS] Property 2/76: Failed
2025-08-12 11:43:06,582 - WARNING - Bot detection on property 1, applying recovery
2025-08-12 11:43:07,864 - WARNING - Bot detection on property 5, applying recovery
2025-08-12 11:43:08,843 - INFO -    [SUCCESS] Property 3/76: Failed
2025-08-12 11:43:10,286 - INFO -    [SUCCESS] Property 4/76: Failed
2025-08-12 11:43:12,327 - WARNING - Bot detection on property 7, applying recovery
2025-08-12 11:43:13,576 - WARNING - Bot detection on property 3, applying recovery
2025-08-12 11:43:14,440 - INFO -    [SUCCESS] Property 5/76: Failed
2025-08-12 11:43:15,613 - INFO -    [SUCCESS] Property 6/76: Failed
2025-08-12 11:43:21,394 - INFO -    [SUCCESS] Property 7/76: Failed
2025-08-12 11:43:22,347 - INFO -    [SUCCESS] Property 8/76: Failed
2025-08-12 11:43:23,609 - WARNING - Bot detection on property 8, applying recovery
2025-08-12 11:43:30,922 - INFO -    [SUCCESS] Property 9/76: Failed
2025-08-12 11:43:33,708 - WARNING - Bot detection on property 9, applying recovery
2025-08-12 11:43:39,488 - INFO -    [SUCCESS] Property 10/76: Failed
2025-08-12 11:43:45,643 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:43:50,643 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:43:54,312 - WARNING - Bot detection on property 12, applying recovery
2025-08-12 11:43:55,082 - WARNING - Bot detection on property 10, applying recovery
2025-08-12 11:44:00,202 - WARNING - Bot detection on property 16, applying recovery
2025-08-12 11:44:00,325 - WARNING - Bot detection on property 14, applying recovery
2025-08-12 11:44:00,852 - INFO -    [SUCCESS] Property 11/76: Failed
2025-08-12 11:44:02,622 - INFO -    [SUCCESS] Property 12/76: Failed
2025-08-12 11:44:04,051 - WARNING - Bot detection on property 13, applying recovery
2025-08-12 11:44:05,057 - WARNING - Bot detection on property 11, applying recovery
2025-08-12 11:44:07,989 - INFO -    [SUCCESS] Property 13/76: Failed
2025-08-12 11:44:09,553 - INFO -    [SUCCESS] Property 14/76: Failed
2025-08-12 11:44:11,557 - WARNING - Bot detection on property 17, applying recovery
2025-08-12 11:44:12,030 - INFO -    [SUCCESS] Property 15/76: Failed
2025-08-12 11:44:13,828 - INFO -    [SUCCESS] Property 16/76: Failed
2025-08-12 11:44:14,036 - WARNING - Bot detection on property 15, applying recovery
2025-08-12 11:44:20,615 - INFO -    [SUCCESS] Property 17/76: Failed
2025-08-12 11:44:21,104 - INFO -    [SUCCESS] Property 18/76: Failed
2025-08-12 11:44:24,491 - WARNING - Bot detection on property 18, applying recovery
2025-08-12 11:44:33,975 - INFO -    [SUCCESS] Property 19/76: Failed
2025-08-12 11:44:37,563 - WARNING - Bot detection on property 19, applying recovery
2025-08-12 11:44:42,658 - INFO -    [SUCCESS] Property 20/76: Failed
2025-08-12 11:44:48,798 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:44:53,799 - INFO - 
[BATCH] Processing batch 3: Properties 21-30
2025-08-12 11:44:59,425 - WARNING - Bot detection on property 22, applying recovery
2025-08-12 11:44:59,477 - WARNING - Bot detection on property 26, applying recovery
2025-08-12 11:45:03,193 - WARNING - Bot detection on property 20, applying recovery
2025-08-12 11:45:04,007 - WARNING - Bot detection on property 24, applying recovery
2025-08-12 11:45:07,871 - INFO -    [SUCCESS] Property 21/76: Failed
2025-08-12 11:45:09,326 - INFO -    [SUCCESS] Property 22/76: Failed
2025-08-12 11:45:10,330 - INFO -    [SUCCESS] Property 23/76: Failed
2025-08-12 11:45:12,959 - WARNING - Bot detection on property 23, applying recovery
2025-08-12 11:45:13,609 - INFO -    [SUCCESS] Property 24/76: Failed
2025-08-12 11:45:13,842 - WARNING - Bot detection on property 21, applying recovery
2025-08-12 11:45:14,482 - WARNING - Bot detection on property 27, applying recovery
2025-08-12 11:45:16,954 - WARNING - Bot detection on property 25, applying recovery
2025-08-12 11:45:19,019 - INFO -    [SUCCESS] Property 25/76: Failed
2025-08-12 11:45:20,651 - INFO -    [SUCCESS] Property 26/76: Failed
2025-08-12 11:45:23,788 - INFO -    [SUCCESS] Property 27/76: Failed
2025-08-12 11:45:26,472 - INFO -    [SUCCESS] Property 28/76: Failed
2025-08-12 11:45:31,279 - WARNING - Bot detection on property 28, applying recovery
2025-08-12 11:45:38,532 - INFO -    [SUCCESS] Property 29/76: Failed
2025-08-12 11:45:41,053 - WARNING - Bot detection on property 29, applying recovery
2025-08-12 11:45:49,088 - INFO -    [SUCCESS] Property 30/76: Failed
2025-08-12 11:45:55,234 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:46:00,235 - INFO - 
[BATCH] Processing batch 4: Properties 31-40
2025-08-12 11:46:04,306 - WARNING - Bot detection on property 34, applying recovery
2025-08-12 11:46:04,911 - WARNING - Bot detection on property 36, applying recovery
2025-08-12 11:46:05,728 - WARNING - Bot detection on property 30, applying recovery
2025-08-12 11:46:09,701 - WARNING - Bot detection on property 32, applying recovery
2025-08-12 11:46:11,387 - INFO -    [SUCCESS] Property 31/76: Failed
2025-08-12 11:46:12,966 - INFO -    [SUCCESS] Property 32/76: Failed
2025-08-12 11:46:14,600 - INFO -    [SUCCESS] Property 33/76: Failed
2025-08-12 11:46:15,002 - INFO -    [SUCCESS] Property 34/76: Failed
2025-08-12 11:46:15,701 - WARNING - Bot detection on property 35, applying recovery
2025-08-12 11:46:16,444 - WARNING - Bot detection on property 37, applying recovery
2025-08-12 11:46:16,521 - WARNING - Bot detection on property 31, applying recovery
2025-08-12 11:46:19,426 - WARNING - Bot detection on property 33, applying recovery
2025-08-12 11:46:20,723 - INFO -    [SUCCESS] Property 35/76: Failed
2025-08-12 11:46:25,249 - INFO -    [SUCCESS] Property 36/76: Failed
2025-08-12 11:46:26,493 - INFO -    [SUCCESS] Property 37/76: Failed
2025-08-12 11:46:29,034 - INFO -    [SUCCESS] Property 38/76: Failed
2025-08-12 11:46:31,227 - WARNING - Bot detection on property 38, applying recovery
2025-08-12 11:46:46,097 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:46:48,134 - INFO - [TIMER] Waiting 4.2 seconds before next page...
2025-08-12 11:46:52,311 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:46:52,312 - INFO -    [LIST] Total properties: 20
2025-08-12 11:46:52,312 - INFO -    [SUCCESS] Valid properties: 20
2025-08-12 11:46:52,312 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:46:52,312 - INFO -    [COMPLETE] Average data quality: 89.7%
2025-08-12 11:46:52,321 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:46:52,321 - INFO - ============================================================
2025-08-12 11:46:52,321 - INFO -    [LIST] Found 20 property URLs for detailed scraping
2025-08-12 11:46:52,322 - INFO - [HOUSE] Starting individual property page scraping for 20 properties
2025-08-12 11:46:52,322 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:46:52,322 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:46:52,322 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:46:52,323 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:46:52,323 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:46:58,945 - ERROR - Error scraping property 4: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:46:58,946 - INFO -    [SUCCESS] Property 1/20: Failed
2025-08-12 11:46:59,469 - ERROR - Error scraping property 6: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:46:59,469 - INFO -    [SUCCESS] Property 2/20: Failed
2025-08-12 11:47:04,796 - ERROR - Error scraping property 2: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:04,797 - INFO -    [SUCCESS] Property 3/20: Failed
2025-08-12 11:47:05,343 - ERROR - Error scraping property 7: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:05,344 - INFO -    [SUCCESS] Property 4/20: Failed
2025-08-12 11:47:06,300 - ERROR - Error scraping property 5: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:06,301 - INFO -    [SUCCESS] Property 5/20: Failed
2025-08-12 11:47:06,590 - ERROR - Error scraping property 0: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:06,591 - INFO -    [SUCCESS] Property 6/20: Failed
2025-08-12 11:47:10,218 - ERROR - Error scraping property 1: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:10,219 - INFO -    [SUCCESS] Property 7/20: Failed
2025-08-12 11:47:10,388 - ERROR - Error scraping property 3: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:10,389 - INFO -    [SUCCESS] Property 8/20: Failed
2025-08-12 11:47:19,374 - ERROR - Error scraping property 8: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:19,375 - INFO -    [SUCCESS] Property 9/20: Failed
2025-08-12 11:47:21,280 - ERROR - Error scraping property 9: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:21,281 - INFO -    [SUCCESS] Property 10/20: Failed
2025-08-12 11:47:27,478 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:47:32,478 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:47:39,874 - ERROR - Error scraping property 14: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:39,876 - INFO -    [SUCCESS] Property 11/20: Failed
2025-08-12 11:47:40,307 - ERROR - Error scraping property 16: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:40,308 - INFO -    [SUCCESS] Property 12/20: Failed
2025-08-12 11:47:44,442 - ERROR - Error scraping property 15: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:44,443 - INFO -    [SUCCESS] Property 13/20: Failed
2025-08-12 11:47:44,503 - ERROR - Error scraping property 17: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:44,504 - INFO -    [SUCCESS] Property 14/20: Failed
2025-08-12 11:47:45,949 - ERROR - Error scraping property 12: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:45,951 - INFO -    [SUCCESS] Property 15/20: Failed
2025-08-12 11:47:49,275 - ERROR - Error scraping property 10: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:49,276 - INFO -    [SUCCESS] Property 16/20: Failed
2025-08-12 11:47:52,035 - ERROR - Error scraping property 13: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:52,035 - INFO -    [SUCCESS] Property 17/20: Failed
2025-08-12 11:47:52,329 - ERROR - Error scraping property 11: 'WebDriver' object has no attribute 'lower'
2025-08-12 11:47:52,329 - INFO -    [SUCCESS] Property 18/20: Failed
2025-08-12 11:48:05,973 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:48:07,714 - INFO - [TIMER] Waiting 2.2 seconds before next page...
2025-08-12 11:48:09,894 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:48:09,894 - INFO -    [LIST] Total properties: 20
2025-08-12 11:48:09,894 - INFO -    [SUCCESS] Valid properties: 20
2025-08-12 11:48:09,894 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:48:09,894 - INFO -    [COMPLETE] Average data quality: 89.7%
2025-08-12 11:48:09,900 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:48:09,901 - INFO - ============================================================
2025-08-12 11:48:09,901 - INFO -    [LIST] Found 20 property URLs for detailed scraping
2025-08-12 11:48:09,901 - INFO - [HOUSE] Starting individual property page scraping for 20 properties
2025-08-12 11:48:09,901 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:48:09,901 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:48:09,901 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:48:09,901 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:48:09,901 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:48:18,063 - ERROR - Error scraping property 2: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:18,098 - INFO -    [SUCCESS] Property 1/20: Failed
2025-08-12 11:48:18,149 - ERROR - Error scraping property 6: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:18,152 - INFO -    [SUCCESS] Property 2/20: Failed
2025-08-12 11:48:19,055 - ERROR - Error scraping property 4: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:19,080 - INFO -    [SUCCESS] Property 3/20: Failed
2025-08-12 11:48:19,383 - ERROR - Error scraping property 0: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:19,384 - INFO -    [SUCCESS] Property 4/20: Failed
2025-08-12 11:48:22,395 - ERROR - Error scraping property 7: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:22,396 - INFO -    [SUCCESS] Property 5/20: Failed
2025-08-12 11:48:23,872 - ERROR - Error scraping property 1: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:23,907 - INFO -    [SUCCESS] Property 6/20: Failed
2025-08-12 11:48:24,258 - ERROR - Error scraping property 5: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:24,259 - INFO -    [SUCCESS] Property 7/20: Failed
2025-08-12 11:48:24,835 - ERROR - Error scraping property 3: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:24,836 - INFO -    [SUCCESS] Property 8/20: Failed
2025-08-12 11:48:35,859 - ERROR - Error scraping property 8: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:35,860 - INFO -    [SUCCESS] Property 9/20: Failed
2025-08-12 11:48:39,330 - ERROR - Error scraping property 9: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:39,330 - INFO -    [SUCCESS] Property 10/20: Failed
2025-08-12 11:48:45,549 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:48:50,550 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:48:56,565 - ERROR - Error scraping property 14: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:56,565 - INFO -    [SUCCESS] Property 11/20: Failed
2025-08-12 11:48:57,629 - ERROR - Error scraping property 16: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:57,631 - INFO -    [SUCCESS] Property 12/20: Failed
2025-08-12 11:48:59,144 - ERROR - Error scraping property 15: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:48:59,145 - INFO -    [SUCCESS] Property 13/20: Failed
2025-08-12 11:49:01,644 - ERROR - Error scraping property 17: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:01,645 - INFO -    [SUCCESS] Property 14/20: Failed
2025-08-12 11:49:04,217 - ERROR - Error scraping property 10: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:04,218 - INFO -    [SUCCESS] Property 15/20: Failed
2025-08-12 11:49:08,129 - ERROR - Error scraping property 12: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:08,129 - INFO -    [SUCCESS] Property 16/20: Failed
2025-08-12 11:49:09,579 - ERROR - Error scraping property 11: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:09,579 - INFO -    [SUCCESS] Property 17/20: Failed
2025-08-12 11:49:13,229 - ERROR - Error scraping property 18: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:13,229 - INFO -    [SUCCESS] Property 18/20: Failed
2025-08-12 11:49:13,744 - ERROR - Error scraping property 13: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:13,745 - INFO -    [SUCCESS] Property 19/20: Failed
2025-08-12 11:49:17,908 - ERROR - Error scraping property 19: 'IntegratedMagicBricksScraper' object has no attribute '_safe_extract_title'
2025-08-12 11:49:17,909 - INFO -    [SUCCESS] Property 20/20: Failed
2025-08-12 11:49:24,263 - INFO - 
[COMPLETE] Concurrent individual property scraping complete: 0/20 successful
2025-08-12 11:49:26,396 - INFO - WebDriver closed
2025-08-12 11:49:46,819 - INFO - WebDriver closed
2025-08-12 11:50:07,173 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:50:09,272 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 11:50:12,341 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:50:12,341 - INFO -    [LIST] Total properties: 20
2025-08-12 11:50:12,341 - INFO -    [SUCCESS] Valid properties: 20
2025-08-12 11:50:12,342 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:50:12,342 - INFO -    [COMPLETE] Average data quality: 89.7%
2025-08-12 11:50:12,347 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:50:12,347 - INFO - ============================================================
2025-08-12 11:50:12,347 - INFO -    [LIST] Found 20 property URLs for detailed scraping
2025-08-12 11:50:12,347 - INFO - [HOUSE] Starting individual property page scraping for 20 properties
2025-08-12 11:50:12,348 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:50:12,348 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:50:12,348 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:50:12,348 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:50:12,348 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:50:19,437 - ERROR - Error scraping property 0: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:19,438 - INFO -    [SUCCESS] Property 1/20: Failed
2025-08-12 11:50:26,850 - ERROR - Error scraping property 2: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:26,893 - INFO -    [SUCCESS] Property 2/20: Failed
2025-08-12 11:50:28,955 - ERROR - Error scraping property 1: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:28,993 - INFO -    [SUCCESS] Property 3/20: Failed
2025-08-12 11:50:32,581 - ERROR - Error scraping property 6: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:32,606 - INFO -    [SUCCESS] Property 4/20: Failed
2025-08-12 11:50:33,549 - ERROR - Error scraping property 4: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:33,575 - INFO -    [SUCCESS] Property 5/20: Failed
2025-08-12 11:50:35,019 - ERROR - Error scraping property 3: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:35,020 - INFO -    [SUCCESS] Property 6/20: Failed
2025-08-12 11:50:37,956 - ERROR - Error scraping property 7: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:37,999 - INFO -    [SUCCESS] Property 7/20: Failed
2025-08-12 11:50:38,304 - ERROR - Error scraping property 5: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:38,305 - INFO -    [SUCCESS] Property 8/20: Failed
2025-08-12 11:50:43,883 - ERROR - Error scraping property 8: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:43,884 - INFO -    [SUCCESS] Property 9/20: Failed
2025-08-12 11:50:48,379 - ERROR - Error scraping property 9: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:50:48,380 - INFO -    [SUCCESS] Property 10/20: Failed
2025-08-12 11:50:54,600 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:50:59,601 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:51:06,937 - ERROR - Error scraping property 10: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:06,975 - INFO -    [SUCCESS] Property 11/20: Failed
2025-08-12 11:51:07,551 - ERROR - Error scraping property 16: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:07,552 - INFO -    [SUCCESS] Property 12/20: Failed
2025-08-12 11:51:10,642 - ERROR - Error scraping property 14: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:10,642 - INFO -    [SUCCESS] Property 13/20: Failed
2025-08-12 11:51:13,120 - ERROR - Error scraping property 11: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:13,124 - INFO -    [SUCCESS] Property 14/20: Failed
2025-08-12 11:51:15,488 - ERROR - Error scraping property 17: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:15,535 - INFO -    [SUCCESS] Property 15/20: Failed
2025-08-12 11:51:16,315 - ERROR - Error scraping property 15: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:16,351 - INFO -    [SUCCESS] Property 16/20: Failed
2025-08-12 11:51:16,598 - ERROR - Error scraping property 12: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:16,599 - INFO -    [SUCCESS] Property 17/20: Failed
2025-08-12 11:51:21,267 - ERROR - Error scraping property 13: 'IntegratedMagicBricksScraper' object has no attribute '_extract_property_id'
2025-08-12 11:51:21,268 - INFO -    [SUCCESS] Property 18/20: Failed
2025-08-12 11:52:39,032 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:52:41,065 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 11:52:44,170 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:52:44,170 - INFO -    [LIST] Total properties: 20
2025-08-12 11:52:44,170 - INFO -    [SUCCESS] Valid properties: 20
2025-08-12 11:52:44,170 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:52:44,170 - INFO -    [COMPLETE] Average data quality: 89.7%
2025-08-12 11:52:44,177 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:52:44,177 - INFO - ============================================================
2025-08-12 11:52:44,178 - INFO -    [LIST] Found 20 property URLs for detailed scraping
2025-08-12 11:52:44,178 - INFO - [HOUSE] Starting individual property page scraping for 20 properties
2025-08-12 11:52:44,178 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:52:44,178 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:52:44,178 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:52:44,178 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:52:44,178 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:52:53,309 - INFO -    [SUCCESS] Property 1/20: Success
2025-08-12 11:52:54,284 - INFO -    [SUCCESS] Property 2/20: Success
2025-08-12 11:52:58,636 - INFO -    [SUCCESS] Property 3/20: Success
2025-08-12 11:52:58,802 - INFO -    [SUCCESS] Property 4/20: Success
2025-08-12 11:53:01,076 - INFO -    [SUCCESS] Property 5/20: Success
2025-08-12 11:53:02,857 - INFO -    [SUCCESS] Property 6/20: Success
2025-08-12 11:53:04,838 - INFO -    [SUCCESS] Property 7/20: Success
2025-08-12 11:53:07,861 - INFO -    [SUCCESS] Property 8/20: Success
2025-08-12 11:53:14,380 - INFO -    [SUCCESS] Property 9/20: Success
2025-08-12 11:53:18,194 - INFO -    [SUCCESS] Property 10/20: Success
2025-08-12 11:53:24,404 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:53:29,405 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:53:38,154 - INFO -    [SUCCESS] Property 11/20: Success
2025-08-12 11:53:39,083 - INFO -    [SUCCESS] Property 12/20: Success
2025-08-12 11:53:41,265 - INFO -    [SUCCESS] Property 13/20: Success
2025-08-12 11:53:44,425 - INFO -    [SUCCESS] Property 14/20: Success
2025-08-12 11:53:44,696 - INFO -    [SUCCESS] Property 15/20: Success
2025-08-12 11:53:45,486 - INFO -    [SUCCESS] Property 16/20: Success
2025-08-12 11:53:45,666 - INFO -    [SUCCESS] Property 17/20: Success
2025-08-12 11:53:48,627 - INFO -    [SUCCESS] Property 18/20: Success
2025-08-12 11:53:58,696 - INFO -    [SUCCESS] Property 19/20: Success
2025-08-12 11:54:02,123 - INFO -    [SUCCESS] Property 20/20: Success
2025-08-12 11:54:08,387 - INFO - 
[COMPLETE] Concurrent individual property scraping complete: 20/20 successful
2025-08-12 11:54:08,399 - INFO -    [SAVE] CSV updated with detailed property information
2025-08-12 11:54:08,399 - INFO -    [SUCCESS] Updated CSV with 20 detailed properties
2025-08-12 11:54:10,512 - INFO - WebDriver closed
2025-08-12 11:54:30,893 - INFO - WebDriver closed
2025-08-12 11:57:50,146 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 11:57:52,454 - INFO - [TIMER] Waiting 2.2 seconds before next page...
2025-08-12 11:57:54,690 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 11:57:54,690 - INFO -    [LIST] Total properties: 20
2025-08-12 11:57:54,691 - INFO -    [SUCCESS] Valid properties: 20
2025-08-12 11:57:54,691 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 11:57:54,691 - INFO -    [COMPLETE] Average data quality: 89.7%
2025-08-12 11:57:54,702 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 11:57:54,702 - INFO - ============================================================
2025-08-12 11:57:54,702 - INFO -    [LIST] Found 20 property URLs for detailed scraping
2025-08-12 11:57:54,702 - INFO - [HOUSE] Starting individual property page scraping for 20 properties
2025-08-12 11:57:54,703 - INFO -    [BATCH] Batch size: 10
2025-08-12 11:57:54,703 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 11:57:54,703 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 11:57:54,703 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 11:57:54,703 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 11:58:04,669 - INFO -    [SUCCESS] Property 1/20: Success
2025-08-12 11:58:06,746 - INFO -    [SUCCESS] Property 2/20: Success
2025-08-12 11:58:10,484 - INFO -    [SUCCESS] Property 3/20: Success
2025-08-12 11:58:10,799 - INFO -    [SUCCESS] Property 4/20: Success
2025-08-12 11:58:13,848 - INFO -    [SUCCESS] Property 5/20: Success
2025-08-12 11:58:14,175 - INFO -    [SUCCESS] Property 6/20: Success
2025-08-12 11:58:16,364 - INFO -    [SUCCESS] Property 7/20: Success
2025-08-12 11:58:18,103 - INFO -    [SUCCESS] Property 8/20: Success
2025-08-12 11:58:25,343 - INFO -    [SUCCESS] Property 9/20: Success
2025-08-12 11:58:31,631 - INFO -    [SUCCESS] Property 10/20: Success
2025-08-12 11:58:37,845 - INFO -    [BREAK] Batch break: 5s
2025-08-12 11:58:42,846 - INFO - 
[BATCH] Processing batch 2: Properties 11-20
2025-08-12 11:58:51,260 - INFO -    [SUCCESS] Property 11/20: Success
2025-08-12 11:58:51,549 - INFO -    [SUCCESS] Property 12/20: Success
2025-08-12 11:58:52,357 - INFO -    [SUCCESS] Property 13/20: Success
2025-08-12 11:58:58,175 - INFO -    [SUCCESS] Property 14/20: Success
2025-08-12 11:58:59,826 - INFO -    [SUCCESS] Property 15/20: Success
2025-08-12 11:58:59,922 - INFO -    [SUCCESS] Property 16/20: Success
2025-08-12 11:59:00,094 - INFO -    [SUCCESS] Property 17/20: Success
2025-08-12 11:59:02,363 - INFO -    [SUCCESS] Property 18/20: Success
2025-08-12 11:59:13,059 - INFO -    [SUCCESS] Property 19/20: Success
2025-08-12 11:59:17,774 - INFO -    [SUCCESS] Property 20/20: Success
2025-08-12 11:59:23,987 - INFO - 
[COMPLETE] Concurrent individual property scraping complete: 20/20 successful
2025-08-12 11:59:23,999 - INFO -    [SAVE] CSV updated with detailed property information
2025-08-12 11:59:24,000 - INFO -    [SUCCESS] Updated CSV with 20 detailed properties
2025-08-12 11:59:26,124 - INFO - WebDriver closed
2025-08-12 11:59:46,534 - INFO - WebDriver closed
2025-08-12 17:49:38,455 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 17:49:41,608 - INFO - [TIMER] Waiting 3.0 seconds before next page...
2025-08-12 17:49:47,310 - INFO - [TIMER] Waiting 2.5 seconds before next page...
2025-08-12 17:49:52,344 - INFO - [TIMER] Waiting 4.7 seconds before next page...
2025-08-12 17:49:59,245 - INFO - [TIMER] Waiting 3.8 seconds before next page...
2025-08-12 17:50:05,595 - INFO - [TIMER] Waiting 4.2 seconds before next page...
2025-08-12 17:50:12,316 - INFO - [TIMER] Waiting 4.4 seconds before next page...
2025-08-12 17:50:19,312 - INFO - [TIMER] Waiting 2.6 seconds before next page...
2025-08-12 17:50:24,009 - INFO - [TIMER] Waiting 3.9 seconds before next page...
2025-08-12 17:50:30,754 - INFO - [TIMER] Waiting 4.2 seconds before next page...
2025-08-12 17:50:37,146 - INFO - [TIMER] Waiting 3.2 seconds before next page...
2025-08-12 17:50:42,696 - INFO - [TIMER] Waiting 3.2 seconds before next page...
2025-08-12 17:50:48,546 - INFO - [TIMER] Waiting 5.6 seconds before next page...
2025-08-12 17:50:56,763 - INFO - [TIMER] Waiting 4.2 seconds before next page...
2025-08-12 17:51:03,305 - INFO - [TIMER] Waiting 4.1 seconds before next page...
2025-08-12 17:51:09,709 - INFO - [TIMER] Waiting 4.7 seconds before next page...
2025-08-12 17:51:16,591 - INFO - [TIMER] Waiting 5.9 seconds before next page...
2025-08-12 17:51:24,888 - INFO - [TIMER] Waiting 5.6 seconds before next page...
2025-08-12 17:51:33,253 - INFO - [TIMER] Waiting 3.5 seconds before next page...
2025-08-12 17:51:42,288 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 17:51:49,352 - INFO - [TIMER] Waiting 3.6 seconds before next page...
2025-08-12 17:51:55,782 - INFO - [TIMER] Waiting 3.3 seconds before next page...
2025-08-12 17:52:01,508 - INFO - [TIMER] Waiting 5.9 seconds before next page...
2025-08-12 17:52:09,729 - INFO - [TIMER] Waiting 2.7 seconds before next page...
2025-08-12 17:52:14,865 - INFO - [TIMER] Waiting 4.7 seconds before next page...
2025-08-12 17:52:22,529 - INFO - [TIMER] Waiting 3.2 seconds before next page...
2025-08-12 17:52:28,888 - INFO - [TIMER] Waiting 3.2 seconds before next page...
2025-08-12 17:52:34,531 - INFO - [TIMER] Waiting 2.9 seconds before next page...
2025-08-12 17:52:40,943 - INFO - [TIMER] Waiting 3.3 seconds before next page...
2025-08-12 17:52:46,150 - WARNING - [ALERT] Bot detection #1 - Implementing recovery strategy
2025-08-12 17:52:46,151 - INFO -    [RETRY] Strategy 1: Extended delay (45s) + User agent rotation
2025-08-12 17:53:35,285 - ERROR -    [ERROR] Failed to restart browser session: 'IntegratedMagicBricksScraper' object has no attribute '_setup_webdriver'
2025-08-12 17:53:51,652 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 17:53:51,652 - INFO -    [LIST] Total properties: 513
2025-08-12 17:53:51,652 - INFO -    [SUCCESS] Valid properties: 513
2025-08-12 17:53:51,652 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 17:53:51,652 - INFO -    [COMPLETE] Average data quality: 96.5%
2025-08-12 17:53:52,223 - INFO - \n[HOUSE] PHASE 2: Starting Individual Property Page Scraping
2025-08-12 17:53:52,223 - INFO - ============================================================
2025-08-12 17:53:52,224 - INFO -    [LIST] Found 513 property URLs for detailed scraping
2025-08-12 17:53:52,224 - INFO - [HOUSE] Starting individual property page scraping for 513 properties
2025-08-12 17:53:52,224 - INFO -    [BATCH] Batch size: 10
2025-08-12 17:53:52,224 - INFO -    [SHIELD] Enhanced anti-scraping: Enabled
2025-08-12 17:53:52,225 - INFO -    [RETRY] Concurrent processing: Enabled
2025-08-12 17:53:52,225 - INFO -    ⚡ Concurrent workers: 4
2025-08-12 17:53:52,225 - INFO - 
[BATCH] Processing batch 1: Properties 1-10
2025-08-12 17:54:01,197 - WARNING - Bot detection on property 6, applying recovery
2025-08-12 17:54:01,470 - WARNING - Bot detection on property 0, applying recovery
2025-08-12 17:54:03,994 - WARNING - Bot detection on property 4, applying recovery
2025-08-12 17:54:06,986 - INFO -    [SUCCESS] Property 1/513: Failed
2025-08-12 17:54:07,855 - WARNING - Bot detection on property 2, applying recovery
2025-08-12 17:54:09,113 - INFO -    [SUCCESS] Property 2/513: Failed
2025-08-12 17:54:10,056 - INFO -    [SUCCESS] Property 3/513: Failed
2025-08-12 17:54:12,468 - WARNING - Bot detection on property 7, applying recovery
2025-08-12 17:54:14,913 - WARNING - Bot detection on property 5, applying recovery
2025-08-12 17:54:15,036 - INFO -    [SUCCESS] Property 4/513: Failed
2025-08-12 17:54:16,209 - WARNING - Bot detection on property 1, applying recovery
2025-08-12 17:54:20,763 - WARNING - Bot detection on property 3, applying recovery
2025-08-12 17:54:22,206 - INFO -    [SUCCESS] Property 5/513: Failed
2025-08-12 17:54:24,038 - INFO -    [SUCCESS] Property 6/513: Failed
2025-08-12 17:54:25,333 - INFO -    [SUCCESS] Property 7/513: Failed
2025-08-12 17:54:28,042 - INFO -    [SUCCESS] Property 8/513: Failed
2025-08-12 17:54:31,444 - ERROR - Thread setup error: Message: Service C:\Users\<USER>\.cache\selenium\chromedriver\win64\139.0.7258.66\chromedriver.exe unexpectedly exited. Status code was: 3221225786

2025-08-12 17:54:44,690 - INFO -    [BREAK] Batch break: 16s
2025-08-12 18:03:19,291 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 18:03:21,852 - INFO - [TIMER] Waiting 4.6 seconds before next page...
2025-08-12 18:03:28,442 - INFO - [TIMER] Waiting 4.7 seconds before next page...
2025-08-12 18:03:33,167 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 18:03:33,167 - INFO -    [LIST] Total properties: 43
2025-08-12 18:03:33,167 - INFO -    [SUCCESS] Valid properties: 43
2025-08-12 18:03:33,167 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 18:03:33,168 - INFO -    [COMPLETE] Average data quality: 98.7%
2025-08-12 18:03:35,268 - INFO - WebDriver closed
2025-08-12 18:03:55,750 - INFO - WebDriver closed
2025-08-12 18:06:19,425 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 18:06:22,265 - INFO - [TIMER] Waiting 2.4 seconds before next page...
2025-08-12 18:06:24,698 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 18:06:24,698 - INFO -    [LIST] Total properties: 21
2025-08-12 18:06:24,698 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 18:06:24,698 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 18:06:24,698 - INFO -    [COMPLETE] Average data quality: 99.8%
2025-08-12 18:06:26,788 - INFO - WebDriver closed
2025-08-12 18:06:47,227 - INFO - WebDriver closed
2025-08-12 18:08:12,021 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 18:08:19,270 - INFO - WebDriver closed
2025-08-12 18:11:05,879 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 18:11:12,500 - WARNING - [ALERT] Bot detection #1 - Implementing recovery strategy
2025-08-12 18:11:12,500 - INFO -    [RETRY] Strategy 1: Extended delay (45s) + User agent rotation
2025-08-12 18:12:01,630 - ERROR -    [ERROR] Failed to restart browser session: 'IntegratedMagicBricksScraper' object has no attribute '_setup_webdriver'
2025-08-12 18:16:11,548 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 18:16:13,344 - INFO - [TIMER] Waiting 2.9 seconds before next page...
2025-08-12 18:16:17,785 - INFO - [TIMER] Waiting 2.2 seconds before next page...
2025-08-12 18:16:20,006 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 18:16:20,007 - INFO -    [LIST] Total properties: 43
2025-08-12 18:16:20,007 - INFO -    [SUCCESS] Valid properties: 43
2025-08-12 18:16:20,007 - INFO -    [SHIELD] Validation success rate: 100.0%
2025-08-12 18:16:20,007 - INFO -    [COMPLETE] Average data quality: 98.7%
2025-08-12 18:16:22,149 - INFO - WebDriver closed
2025-08-12 18:16:42,587 - INFO - WebDriver closed
2025-08-12 18:20:30,324 - WARNING - Error validating property data: 'dict' object has no attribute 'strip'
2025-08-12 18:20:30,345 - WARNING - Error validating property data: 'dict' object has no attribute 'strip'
2025-08-12 19:10:55,182 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 19:10:56,725 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,727 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,730 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,733 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,735 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,738 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,741 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,744 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,746 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,749 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,752 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,754 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,759 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,763 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,766 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,768 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,771 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,775 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,779 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,781 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,784 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,787 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,790 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,793 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,796 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,798 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,801 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,804 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:10:56,805 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 19:11:02,910 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,912 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,914 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,917 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,919 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,920 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,922 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,924 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,927 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,930 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,933 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,935 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,940 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,942 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,945 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,948 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,951 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,953 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,956 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,959 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,961 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,964 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,966 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,968 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,971 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,974 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,976 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,978 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:02,979 - INFO - [TIMER] Waiting 4.6 seconds before next page...
2025-08-12 19:11:09,111 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,113 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,117 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,119 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,122 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,124 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,127 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,130 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,132 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,134 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,137 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,139 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,143 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,145 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,148 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,150 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,152 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,154 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,157 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,162 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,164 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,167 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,169 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,173 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,175 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:09,176 - INFO - [TIMER] Waiting 2.3 seconds before next page...
2025-08-12 19:11:12,742 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,744 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,746 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,750 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,752 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,755 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,758 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,760 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,764 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,767 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,769 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,771 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,774 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,776 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,779 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,781 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,785 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,787 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,792 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,794 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,797 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,799 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,802 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,804 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,807 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:12,809 - INFO - [TIMER] Waiting 2.9 seconds before next page...
2025-08-12 19:11:16,957 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,959 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,962 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,964 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,966 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,968 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,971 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,973 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,976 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,978 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,981 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,983 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,986 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,988 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,991 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,993 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,996 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:16,998 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,001 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,003 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,005 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,007 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,011 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,013 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,016 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,018 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,021 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,022 - ERROR - Error extracting property data: 'successful_extractions'
2025-08-12 19:11:17,023 - INFO - [TIMER] Waiting 4.3 seconds before next page...
2025-08-12 19:11:21,297 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 19:11:21,297 - INFO -    [LIST] Total properties: 0
2025-08-12 19:11:21,297 - INFO -    [SUCCESS] Valid properties: 0
2025-08-12 19:11:21,298 - INFO -    [SHIELD] Validation success rate: 0.0%
2025-08-12 19:11:21,298 - INFO -    [COMPLETE] Average data quality: 0.0%
2025-08-12 19:11:23,425 - INFO - WebDriver closed
2025-08-12 19:11:43,899 - INFO - WebDriver closed
2025-08-12 19:13:11,939 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 19:13:13,901 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 19:13:13,901 - INFO -    [LIST] Total properties: 28
2025-08-12 19:13:13,901 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 19:13:13,901 - INFO -    [SHIELD] Validation success rate: 75.0%
2025-08-12 19:13:13,902 - INFO -    [COMPLETE] Average data quality: 96.8%
2025-08-12 19:13:16,059 - INFO - WebDriver closed
2025-08-12 19:13:36,519 - INFO - WebDriver closed
2025-08-12 19:13:51,798 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 19:13:53,832 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 19:13:53,833 - INFO -    [LIST] Total properties: 28
2025-08-12 19:13:53,833 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 19:13:53,833 - INFO -    [SHIELD] Validation success rate: 75.0%
2025-08-12 19:13:53,833 - INFO -    [COMPLETE] Average data quality: 96.8%
2025-08-12 19:13:56,011 - INFO - WebDriver closed
2025-08-12 19:14:16,502 - INFO - WebDriver closed
2025-08-12 19:14:53,650 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 19:14:55,486 - INFO - [TIMER] Waiting 2.3 seconds before next page...
2025-08-12 19:14:59,238 - INFO - [TIMER] Waiting 4.7 seconds before next page...
2025-08-12 19:15:05,448 - INFO - [TIMER] Waiting 3.5 seconds before next page...
2025-08-12 19:15:10,724 - INFO - [TIMER] Waiting 2.4 seconds before next page...
2025-08-12 19:15:14,811 - INFO - [TIMER] Waiting 4.0 seconds before next page...
2025-08-12 19:15:20,542 - INFO - [TIMER] Waiting 3.3 seconds before next page...
2025-08-12 19:15:25,415 - INFO - [TIMER] Waiting 2.8 seconds before next page...
2025-08-12 19:15:29,473 - INFO - [TIMER] Waiting 2.7 seconds before next page...
2025-08-12 19:15:33,397 - INFO - [TIMER] Waiting 4.5 seconds before next page...
2025-08-12 19:15:39,386 - INFO - [TIMER] Waiting 3.5 seconds before next page...
2025-08-12 19:15:42,853 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 19:15:42,853 - INFO -    [LIST] Total properties: 229
2025-08-12 19:15:42,854 - INFO -    [SUCCESS] Valid properties: 184
2025-08-12 19:15:42,854 - INFO -    [SHIELD] Validation success rate: 80.3%
2025-08-12 19:15:42,854 - INFO -    [COMPLETE] Average data quality: 92.3%
2025-08-12 19:15:44,966 - INFO - WebDriver closed
2025-08-12 19:16:05,417 - INFO - WebDriver closed
2025-08-12 19:19:41,227 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 19:19:42,999 - INFO - [TIMER] Waiting 4.1 seconds before next page...
2025-08-12 19:19:48,442 - INFO - [TIMER] Waiting 3.7 seconds before next page...
2025-08-12 19:19:53,410 - INFO - [TIMER] Waiting 2.9 seconds before next page...
2025-08-12 19:19:57,608 - INFO - [TIMER] Waiting 2.2 seconds before next page...
2025-08-12 19:20:01,280 - INFO - [TIMER] Waiting 3.7 seconds before next page...
2025-08-12 19:20:06,257 - INFO - [TIMER] Waiting 2.8 seconds before next page...
2025-08-12 19:20:10,411 - INFO - [TIMER] Waiting 3.7 seconds before next page...
2025-08-12 19:20:15,204 - INFO - [TIMER] Waiting 2.1 seconds before next page...
2025-08-12 19:20:18,463 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 19:20:25,297 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 19:20:29,713 - INFO - [TIMER] Waiting 4.9 seconds before next page...
2025-08-12 19:20:36,244 - INFO - [TIMER] Waiting 4.1 seconds before next page...
2025-08-12 19:20:41,840 - INFO - [TIMER] Waiting 4.8 seconds before next page...
2025-08-12 19:20:48,314 - INFO - [TIMER] Waiting 4.3 seconds before next page...
2025-08-12 19:20:53,976 - INFO - [TIMER] Waiting 5.9 seconds before next page...
2025-08-12 19:21:01,527 - INFO - [TIMER] Waiting 4.0 seconds before next page...
2025-08-12 19:21:06,800 - INFO - [TIMER] Waiting 5.8 seconds before next page...
2025-08-12 19:21:14,184 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 19:21:20,714 - INFO - [TIMER] Waiting 5.9 seconds before next page...
2025-08-12 19:21:28,049 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 19:21:34,585 - INFO - [TIMER] Waiting 4.5 seconds before next page...
2025-08-12 19:21:40,905 - INFO - [TIMER] Waiting 5.6 seconds before next page...
2025-08-12 19:21:48,000 - INFO - [TIMER] Waiting 5.0 seconds before next page...
2025-08-12 19:21:54,336 - INFO - [TIMER] Waiting 5.8 seconds before next page...
2025-08-12 19:22:01,523 - INFO - [TIMER] Waiting 2.6 seconds before next page...
2025-08-12 19:22:05,805 - INFO - [TIMER] Waiting 4.1 seconds before next page...
2025-08-12 19:22:11,401 - INFO - [TIMER] Waiting 3.9 seconds before next page...
2025-08-12 19:22:16,457 - INFO - [TIMER] Waiting 4.6 seconds before next page...
2025-08-12 19:22:22,204 - WARNING - [ALERT] Bot detection #1 - Implementing recovery strategy
2025-08-12 19:22:22,204 - INFO -    [RETRY] Strategy 1: Extended delay (45s) + User agent rotation
2025-08-12 19:23:11,347 - ERROR -    [ERROR] Failed to restart browser session: 'IntegratedMagicBricksScraper' object has no attribute '_setup_webdriver'
2025-08-12 19:23:27,724 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 19:23:27,724 - INFO -    [LIST] Total properties: 593
2025-08-12 19:23:27,724 - INFO -    [SUCCESS] Valid properties: 513
2025-08-12 19:23:27,724 - INFO -    [SHIELD] Validation success rate: 86.5%
2025-08-12 19:23:27,724 - INFO -    [COMPLETE] Average data quality: 90.5%
2025-08-12 19:23:48,156 - INFO - WebDriver closed
2025-08-12 19:24:08,613 - INFO - WebDriver closed
2025-08-12 20:10:36,799 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 20:10:38,454 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 20:10:38,455 - INFO -    [LIST] Total properties: 0
2025-08-12 20:10:38,455 - INFO -    [SUCCESS] Valid properties: 0
2025-08-12 20:10:38,455 - INFO -    [SHIELD] Validation success rate: 0.0%
2025-08-12 20:10:38,455 - INFO -    [COMPLETE] Average data quality: 0.0%
2025-08-12 20:10:38,455 - ERROR - Scraping failed: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-08-12 20:10:40,575 - INFO - WebDriver closed
2025-08-12 20:11:01,060 - INFO - WebDriver closed
2025-08-12 20:12:16,105 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 20:12:18,072 - INFO - [TIMER] Waiting 4.4 seconds before next page...
2025-08-12 20:12:22,479 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 20:12:22,479 - INFO -    [LIST] Total properties: 28
2025-08-12 20:12:22,479 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 20:12:22,479 - INFO -    [SHIELD] Validation success rate: 75.0%
2025-08-12 20:12:22,479 - INFO -    [COMPLETE] Average data quality: 96.4%
2025-08-12 20:12:22,525 - ERROR - Error saving to CSV: 'charmap' codec can't encode character '\U0001f4be' in position 0: character maps to <undefined>
2025-08-12 20:12:24,609 - INFO - WebDriver closed
2025-08-12 20:12:45,104 - INFO - WebDriver closed
2025-08-12 20:15:17,931 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 20:15:19,951 - INFO - [TIMER] Waiting 3.1 seconds before next page...
2025-08-12 20:15:23,020 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 20:15:23,021 - INFO -    [LIST] Total properties: 28
2025-08-12 20:15:23,021 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 20:15:23,021 - INFO -    [SHIELD] Validation success rate: 75.0%
2025-08-12 20:15:23,021 - INFO -    [COMPLETE] Average data quality: 96.4%
2025-08-12 20:15:25,161 - INFO - WebDriver closed
2025-08-12 20:15:45,614 - INFO - WebDriver closed
