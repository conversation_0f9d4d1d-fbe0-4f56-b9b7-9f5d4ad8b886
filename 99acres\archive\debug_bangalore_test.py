#!/usr/bin/env python3
"""
Debug the Bangalore test issue
"""

from integrated_99acres_scraper import Integrated99acresScraper

def debug_bangalore():
    """Debug what's happening with Bangalore test"""
    print("🔍 Debugging Bangalore Test")
    
    scraper = Integrated99acresScraper(headless=True)
    
    url = "https://www.99acres.com/property-for-rent-in-bangalore-ffid"
    print(f"Testing URL: {url}")
    
    try:
        properties = scraper.scrape_page(url)
        
        print(f"Properties returned: {len(properties) if properties else 0}")
        
        if properties:
            print(f"First property sample:")
            first_prop = properties[0]
            for key, value in first_prop.items():
                if value:
                    print(f"  {key}: {value}")
        else:
            print("No properties returned - this is the issue!")
            
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        scraper.close()

if __name__ == "__main__":
    debug_bangalore()
