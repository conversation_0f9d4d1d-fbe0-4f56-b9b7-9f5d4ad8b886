#!/usr/bin/env python3
"""
Production Advanced Dashboard
Professional-grade dashboard with comprehensive features for 2,735 properties
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, jsonify, request
import sqlite3
import json
import threading
import time
import queue
from datetime import datetime, timedelta
import psutil
import logging
from comprehensive_individual_listing_scraper import ComprehensiveIndividualListingScraper

app = Flask(__name__)

class ProductionScraperMonitor:
    """Production-grade scraper monitor with advanced features"""
    
    def __init__(self):
        self.scraper = None
        self.scraper_thread = None
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        
        # Advanced progress tracking
        self.progress = {
            'total_urls': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'current_url': '',
            'current_title': '',
            'current_property_id': '',
            'avg_time_per_property': 0,
            'estimated_completion': '',
            'success_rate': 0,
            'properties_per_hour': 0
        }
        
        # Advanced logging with categories
        self.logs = []
        self.max_logs = 50
        
        # Performance metrics
        self.performance_metrics = {
            'start_time': None,
            'processing_times': [],
            'memory_usage_history': [],
            'cpu_usage_history': [],
            'error_count': 0,
            'retry_count': 0
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("ProductionMonitor")
    
    def add_log(self, level, message):
        """Add categorized log entry"""
        log_entry = {
            'timestamp': datetime.now().strftime("%H:%M:%S"),
            'level': level,
            'message': message,
            'category': self._categorize_log(message)
        }
        self.logs.append(log_entry)
        
        # Keep only recent logs
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)
        
        print(f"[{log_entry['timestamp']}] {level}: {message}")
    
    def _categorize_log(self, message):
        """Categorize log messages"""
        if any(word in message.lower() for word in ['success', 'completed', 'saved']):
            return 'success'
        elif any(word in message.lower() for word in ['error', 'failed', 'exception']):
            return 'error'
        elif any(word in message.lower() for word in ['warning', 'skip', 'duplicate']):
            return 'warning'
        else:
            return 'info'
    
    def get_comprehensive_sources(self):
        """Get comprehensive URL sources with detailed statistics"""
        try:
            # Main database analysis
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            # Total available URLs
            cursor.execute("""
                SELECT COUNT(*) FROM properties 
                WHERE property_url LIKE 'https://www.99acres.com/%'
            """)
            total_urls = cursor.fetchone()[0]
            
            # City breakdown
            cursor.execute("""
                SELECT city, COUNT(*) FROM properties 
                WHERE property_url LIKE 'https://www.99acres.com/%'
                GROUP BY city ORDER BY COUNT(*) DESC LIMIT 10
            """)
            city_breakdown = cursor.fetchall()
            
            # Property type breakdown
            cursor.execute("""
                SELECT property_type, COUNT(*) FROM properties 
                WHERE property_url LIKE 'https://www.99acres.com/%'
                GROUP BY property_type ORDER BY COUNT(*) DESC LIMIT 10
            """)
            type_breakdown = cursor.fetchall()
            
            conn.close()
            
            # Individual properties database
            extracted_count = 0
            recent_extractions = []
            try:
                conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                cursor2 = conn2.cursor()
                cursor2.execute("SELECT COUNT(*) FROM individual_properties")
                extracted_count = cursor2.fetchone()[0]
                
                # Recent extractions
                cursor2.execute("""
                    SELECT title, price_display, property_url 
                    FROM individual_properties 
                    ORDER BY id DESC LIMIT 5
                """)
                recent_extractions = cursor2.fetchall()
                conn2.close()
            except:
                extracted_count = 0
            
            return {
                'individual_listings': {
                    'name': 'Individual Property Listings',
                    'total': total_urls,
                    'extracted': extracted_count,
                    'remaining': total_urls - extracted_count,
                    'progress_percentage': (extracted_count/total_urls)*100 if total_urls > 0 else 0,
                    'description': f'Comprehensive property database with {total_urls} properties across multiple cities',
                    'city_breakdown': city_breakdown,
                    'type_breakdown': type_breakdown,
                    'recent_extractions': recent_extractions
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sources: {str(e)}")
            return {}
    
    def load_urls_for_scraping(self, max_properties=50, skip_existing=True, city_filter=None):
        """Load URLs with advanced filtering options"""
        try:
            self.add_log('INFO', f"Loading URLs (max: {max_properties}, skip_existing: {skip_existing}, city: {city_filter or 'All'})")
            
            # Build query
            base_query = """
                SELECT property_url FROM properties 
                WHERE property_url LIKE 'https://www.99acres.com/%'
            """
            
            params = []
            if city_filter and city_filter != 'All':
                base_query += " AND city = ?"
                params.append(city_filter)
            
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            cursor.execute(base_query + " ORDER BY RANDOM()", params)
            all_urls = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if skip_existing:
                # Get extracted URLs
                try:
                    conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
                    cursor2 = conn2.cursor()
                    cursor2.execute("SELECT property_url FROM individual_properties WHERE property_url IS NOT NULL")
                    extracted_urls = set(row[0] for row in cursor2.fetchall())
                    conn2.close()
                    
                    # Filter out extracted URLs
                    filtered_urls = [url for url in all_urls if url not in extracted_urls]
                    self.add_log('INFO', f"Filtered {len(all_urls)} -> {len(filtered_urls)} URLs (skipped {len(extracted_urls)} existing)")
                    urls = filtered_urls[:max_properties]
                except Exception as e:
                    self.add_log('WARNING', f"Could not filter existing URLs: {str(e)}")
                    urls = all_urls[:max_properties]
            else:
                urls = all_urls[:max_properties]
            
            self.add_log('INFO', f"Selected {len(urls)} URLs for scraping")
            return urls
            
        except Exception as e:
            self.add_log('ERROR', f"Error loading URLs: {str(e)}")
            return []
    
    def start_scraping(self, config):
        """Start scraping with advanced configuration"""
        if self.is_running:
            return False, "Already running"
        
        try:
            max_properties = config.get('max_properties', 50)
            skip_existing = config.get('skip_existing', True)
            city_filter = config.get('city_filter', None)
            
            # Validate limits
            if max_properties > 500:
                return False, "Maximum 500 properties per batch for safety"
            
            # Load URLs
            urls = self.load_urls_for_scraping(max_properties, skip_existing, city_filter)
            
            if not urls:
                return False, "No URLs to scrape"
            
            # Reset progress and metrics
            self.progress = {
                'total_urls': len(urls),
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0,
                'current_url': '',
                'current_title': '',
                'current_property_id': '',
                'avg_time_per_property': 0,
                'estimated_completion': '',
                'success_rate': 0,
                'properties_per_hour': 0
            }
            
            self.performance_metrics = {
                'start_time': datetime.now(),
                'processing_times': [],
                'memory_usage_history': [],
                'cpu_usage_history': [],
                'error_count': 0,
                'retry_count': 0
            }
            
            # Start scraping thread
            self.scraper_thread = threading.Thread(target=self._run_advanced_scraping, args=(urls, config))
            self.scraper_thread.daemon = True
            self.scraper_thread.start()
            
            self.is_running = True
            self.start_time = datetime.now()
            self.total_pause_duration = 0
            
            return True, f"Started scraping {len(urls)} properties with advanced monitoring"
            
        except Exception as e:
            return False, f"Failed to start: {str(e)}"
    
    def _run_advanced_scraping(self, urls, config):
        """Run scraping with advanced monitoring and error handling"""
        try:
            self.add_log('INFO', "Initializing advanced scraper...")
            
            # Create scraper
            self.scraper = ComprehensiveIndividualListingScraper(headless=True)
            
            # Setup driver
            if not self.scraper.setup_driver():
                self.add_log('ERROR', "Failed to setup browser")
                return
            
            self.add_log('SUCCESS', "Browser setup successful, starting extraction...")
            
            # Process each URL with advanced monitoring
            for i, url in enumerate(urls, 1):
                if not self.is_running:
                    break
                
                # Handle pause
                while self.is_paused and self.is_running:
                    time.sleep(1)
                
                if not self.is_running:
                    break
                
                start_time = time.time()
                self.progress['processed'] = i
                self.progress['current_url'] = url
                
                # Extract property ID from URL
                property_id = url.split('-spid-')[-1] if '-spid-' in url else url.split('/')[-1]
                self.progress['current_property_id'] = property_id
                
                self.add_log('INFO', f"Processing {i}/{len(urls)}: {property_id}")
                
                try:
                    # Extract data
                    property_data = self.scraper.extract_comprehensive_property_data(url)
                    
                    if property_data:
                        # Save to database
                        if self.scraper.save_to_database(property_data):
                            self.progress['successful'] += 1
                            self.progress['current_title'] = property_data.get('title', 'Unknown')[:50]
                            self.add_log('SUCCESS', f"✅ {property_data.get('title', 'Unknown')[:40]}...")
                        else:
                            self.progress['failed'] += 1
                            self.performance_metrics['error_count'] += 1
                            self.add_log('ERROR', f"❌ Failed to save to database")
                    else:
                        self.progress['failed'] += 1
                        self.performance_metrics['error_count'] += 1
                        self.add_log('ERROR', f"❌ No data extracted")
                
                except Exception as e:
                    self.progress['failed'] += 1
                    self.performance_metrics['error_count'] += 1
                    self.add_log('ERROR', f"❌ {str(e)}")
                
                # Update performance metrics
                processing_time = time.time() - start_time
                self.performance_metrics['processing_times'].append(processing_time)
                
                # Calculate averages
                if self.performance_metrics['processing_times']:
                    self.progress['avg_time_per_property'] = sum(self.performance_metrics['processing_times']) / len(self.performance_metrics['processing_times'])
                
                # Calculate success rate
                if self.progress['processed'] > 0:
                    self.progress['success_rate'] = (self.progress['successful'] / self.progress['processed']) * 100
                
                # Calculate properties per hour
                elapsed_hours = (datetime.now() - self.performance_metrics['start_time']).total_seconds() / 3600
                if elapsed_hours > 0:
                    self.progress['properties_per_hour'] = self.progress['processed'] / elapsed_hours
                
                # Estimate completion
                if self.progress['avg_time_per_property'] > 0:
                    remaining_time = (len(urls) - self.progress['processed']) * self.progress['avg_time_per_property']
                    completion_time = datetime.now() + timedelta(seconds=remaining_time)
                    self.progress['estimated_completion'] = completion_time.strftime("%H:%M:%S")
                
                # Record system metrics
                self.performance_metrics['memory_usage_history'].append(psutil.virtual_memory().percent)
                self.performance_metrics['cpu_usage_history'].append(psutil.cpu_percent())
                
                # Keep only recent metrics
                if len(self.performance_metrics['memory_usage_history']) > 100:
                    self.performance_metrics['memory_usage_history'].pop(0)
                    self.performance_metrics['cpu_usage_history'].pop(0)
                
                # Adaptive delay based on system performance
                delay = 3  # Base delay
                if psutil.virtual_memory().percent > 80:
                    delay += 2  # Longer delay if memory is high
                if psutil.cpu_percent() > 80:
                    delay += 1  # Longer delay if CPU is high
                
                time.sleep(delay)
            
            self.add_log('SUCCESS', f"🎉 Scraping completed: {self.progress['successful']} successful, {self.progress['failed']} failed")
            
        except Exception as e:
            self.add_log('ERROR', f"Scraping thread error: {str(e)}")
        finally:
            self.is_running = False
            self.is_paused = False
            if self.scraper and self.scraper.driver:
                try:
                    self.scraper.driver.quit()
                except:
                    pass
    
    def pause_scraping(self):
        """Pause scraping"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            self.pause_time = datetime.now()
            self.add_log('WARNING', "⏸️ Scraping paused")
            return True, "Paused"
        return False, "Not running or already paused"
    
    def resume_scraping(self):
        """Resume scraping"""
        if self.is_running and self.is_paused:
            if self.pause_time:
                self.total_pause_duration += (datetime.now() - self.pause_time).total_seconds()
            self.is_paused = False
            self.pause_time = None
            self.add_log('INFO', "▶️ Scraping resumed")
            return True, "Resumed"
        return False, "Not paused"
    
    def stop_scraping(self):
        """Stop scraping"""
        self.is_running = False
        self.is_paused = False
        self.add_log('WARNING', "⏹️ Scraping stopped")
        return True, "Stopped"
    
    def get_advanced_stats(self):
        """Get comprehensive statistics"""
        elapsed_time = "00:00:00"
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_seconds = elapsed.total_seconds() - self.total_pause_duration
            hours = int(elapsed_seconds // 3600)
            minutes = int((elapsed_seconds % 3600) // 60)
            seconds = int(elapsed_seconds % 60)
            elapsed_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
        # System metrics
        current_memory = psutil.virtual_memory().percent
        current_cpu = psutil.cpu_percent()
        
        # Average system usage
        avg_memory = sum(self.performance_metrics['memory_usage_history']) / len(self.performance_metrics['memory_usage_history']) if self.performance_metrics['memory_usage_history'] else current_memory
        avg_cpu = sum(self.performance_metrics['cpu_usage_history']) / len(self.performance_metrics['cpu_usage_history']) if self.performance_metrics['cpu_usage_history'] else current_cpu
        
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'progress': self.progress,
            'logs': self.logs[-20:],  # Last 20 logs
            'system': {
                'memory_usage': current_memory,
                'cpu_usage': current_cpu,
                'avg_memory_usage': avg_memory,
                'avg_cpu_usage': avg_cpu
            },
            'timing': {
                'elapsed_time': elapsed_time,
                'estimated_completion': self.progress.get('estimated_completion', '--:--:--'),
                'avg_time_per_property': round(self.progress.get('avg_time_per_property', 0), 1),
                'properties_per_hour': round(self.progress.get('properties_per_hour', 0), 1)
            },
            'performance': {
                'success_rate': round(self.progress.get('success_rate', 0), 1),
                'error_count': self.performance_metrics['error_count'],
                'retry_count': self.performance_metrics['retry_count']
            }
        }

# Global monitor
monitor = ProductionScraperMonitor()

@app.route('/')
def dashboard():
    """Advanced production dashboard"""
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>🚀 Production 99acres Dashboard - 2,735 Properties</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333; min-height: 100vh;
        }
        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }
        .header { 
            background: rgba(255,255,255,0.95); padding: 25px; border-radius: 15px; 
            margin-bottom: 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header h1 { color: #2c3e50; font-size: 2.8em; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 1.2em; }
        .header .stats-summary { 
            display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; 
            margin-top: 20px; 
        }
        .summary-stat { 
            text-align: center; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); 
            border-radius: 10px; color: white;
        }
        .summary-stat-value { font-size: 2em; font-weight: bold; }
        .summary-stat-label { font-size: 0.9em; opacity: 0.9; }
        
        .grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px; }
        .card { 
            background: rgba(255,255,255,0.95); padding: 25px; border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px);
        }
        .card h3 { color: #2c3e50; margin-bottom: 15px; font-size: 1.5em; }
        
        .controls-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0; }
        .control-group { display: flex; flex-direction: column; }
        .control-group label { margin-bottom: 8px; font-weight: 600; color: #2c3e50; }
        input, select { 
            padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; 
            font-size: 1em; transition: border-color 0.3s;
        }
        input:focus, select:focus { outline: none; border-color: #667eea; }
        
        .btn { 
            padding: 15px 30px; border: none; border-radius: 10px; cursor: pointer; 
            font-weight: bold; font-size: 1em; transition: all 0.3s;
            text-transform: uppercase; letter-spacing: 1px; margin: 5px;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea, #764ba2); color: white; 
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6); }
        .btn-warning { 
            background: linear-gradient(135deg, #f39c12, #e67e22); color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }
        .btn-warning:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(243, 156, 18, 0.6); }
        .btn-danger { 
            background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        .btn-danger:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(5, 1fr); gap: 15px; margin: 20px 0; }
        .stat { 
            text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea, #764ba2); 
            border-radius: 12px; color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .stat-value { font-size: 2.2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        
        .progress-container { margin: 20px 0; }
        .progress-bar { 
            width: 100%; height: 30px; background: #e0e0e0; border-radius: 15px; 
            overflow: hidden; box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-fill { 
            height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); 
            transition: width 0.5s ease; border-radius: 15px;
            position: relative; overflow: hidden;
        }
        .progress-fill::after {
            content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .logs { 
            max-height: 400px; overflow-y: auto; background: #2c3e50; color: #ecf0f1; 
            padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.3);
        }
        .log-entry { 
            margin: 8px 0; padding: 8px 12px; border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .log-success { border-left-color: #2ecc71; background: rgba(46, 204, 113, 0.15); }
        .log-error { border-left-color: #e74c3c; background: rgba(231, 76, 60, 0.15); }
        .log-warning { border-left-color: #f39c12; background: rgba(243, 156, 18, 0.15); }
        
        .source-info { 
            background: linear-gradient(135deg, #2ecc71, #27ae60); color: white; 
            padding: 25px; border-radius: 12px; margin: 15px 0;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        .source-stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-top: 15px; }
        .source-stat { text-align: center; }
        .source-stat-value { font-size: 2em; font-weight: bold; }
        .source-stat-label { font-size: 0.9em; opacity: 0.9; }
        
        .current-property { 
            background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;
            border-left: 5px solid #667eea;
        }
        .current-property strong { color: #2c3e50; }
        
        .performance-grid { 
            display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; 
            margin: 20px 0;
        }
        .performance-card { 
            background: #34495e; color: white; padding: 20px; border-radius: 10px;
            text-align: center;
        }
        .performance-value { font-size: 1.8em; font-weight: bold; margin-bottom: 8px; }
        
        .full-width { grid-column: 1 / -1; }
        
        .city-breakdown { 
            display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; 
            margin-top: 15px; font-size: 0.9em;
        }
        .city-item { 
            background: rgba(255,255,255,0.2); padding: 8px; border-radius: 5px;
            display: flex; justify-content: space-between;
        }
        
        @media (max-width: 1200px) {
            .grid { grid-template-columns: 1fr; }
            .stats-grid { grid-template-columns: repeat(3, 1fr); }
            .controls-grid { grid-template-columns: 1fr; }
        }
        
        @media (max-width: 768px) {
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
            .performance-grid { grid-template-columns: 1fr; }
            .header .stats-summary { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Production 99acres Dashboard</h1>
            <p>Professional-grade scraper • 2,735 properties • Advanced monitoring • Real-time analytics</p>
            <div class="stats-summary">
                <div class="summary-stat">
                    <div class="summary-stat-value" id="total-available">2,735</div>
                    <div class="summary-stat-label">Total Properties</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value" id="total-extracted">113</div>
                    <div class="summary-stat-label">Extracted</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value" id="total-remaining">2,622</div>
                    <div class="summary-stat-label">Remaining</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value" id="completion-percentage">4.1%</div>
                    <div class="summary-stat-label">Complete</div>
                </div>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>📊 Data Source & City Breakdown</h3>
                <div id="source-info">Loading comprehensive data...</div>
            </div>
            
            <div class="card">
                <h3>🔧 Advanced Controls</h3>
                <div class="controls-grid">
                    <div class="control-group">
                        <label>Properties to Scrape:</label>
                        <input type="number" id="max-properties" value="200" min="1" max="500">
                    </div>
                    <div class="control-group">
                        <label>Skip Existing:</label>
                        <select id="skip-existing">
                            <option value="true">Yes (Recommended)</option>
                            <option value="false">No (Re-scrape all)</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>City Filter:</label>
                        <select id="city-filter">
                            <option value="">All Cities</option>
                        </select>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="startScraping()" id="start-btn">▶️ Start Scraping</button>
                    <button class="btn btn-warning" onclick="pauseResumeScraping()" id="pause-btn" disabled>⏸️ Pause</button>
                    <button class="btn btn-danger" onclick="stopScraping()" id="stop-btn" disabled>⏹️ Stop</button>
                </div>
            </div>
        </div>
        
        <div class="card full-width">
            <h3>📈 Real-time Progress & Performance</h3>
            <div class="stats-grid">
                <div class="stat">
                    <div class="stat-value" id="processed">0</div>
                    <div class="stat-label">Processed</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="successful">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="failed">0</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="success-rate">0%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="status">Ready</div>
                    <div class="stat-label">Status</div>
                </div>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div id="progress-text" style="text-align: center; margin-top: 15px; font-weight: bold; font-size: 1.1em;">Ready for production scraping</div>
            </div>
            
            <div class="current-property" id="current-property-info">
                <strong>Current Property:</strong> <span id="current-property">None</span><br>
                <strong>Property ID:</strong> <span id="current-property-id">--</span><br>
                <strong>Elapsed Time:</strong> <span id="elapsed-time">00:00:00</span> | 
                <strong>Estimated Completion:</strong> <span id="estimated-time">--:--:--</span>
            </div>
            
            <div class="performance-grid">
                <div class="performance-card">
                    <div class="performance-value" id="avg-time">0s</div>
                    <div>Avg Time/Property</div>
                </div>
                <div class="performance-card">
                    <div class="performance-value" id="properties-per-hour">0</div>
                    <div>Properties/Hour</div>
                </div>
                <div class="performance-card">
                    <div class="performance-value" id="memory-usage">0%</div>
                    <div>Memory Usage</div>
                </div>
            </div>
        </div>
        
        <div class="card full-width">
            <h3>📝 Live Activity Logs</h3>
            <div class="logs" id="logs">
                <div class="log-entry log-success">🚀 Production dashboard initialized - 2,735 properties ready</div>
                <div class="log-entry">📊 Database repaired: Fixed 2,603 URLs, removed 124 duplicates</div>
                <div class="log-entry">🔧 Advanced monitoring active - Ready for large-scale operations</div>
            </div>
        </div>
    </div>

    <script>
        let isPaused = false;
        
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update progress stats
                    document.getElementById('processed').textContent = data.progress.processed;
                    document.getElementById('successful').textContent = data.progress.successful;
                    document.getElementById('failed').textContent = data.progress.failed;
                    document.getElementById('success-rate').textContent = data.performance.success_rate + '%';
                    
                    let status = 'Ready';
                    if (data.is_running && data.is_paused) status = 'Paused';
                    else if (data.is_running) status = 'Running';
                    document.getElementById('status').textContent = status;
                    
                    // Update progress bar
                    const totalProperties = data.progress.total_urls || 0;
                    const percent = totalProperties > 0 ? (data.progress.processed / totalProperties) * 100 : 0;
                    document.getElementById('progress-fill').style.width = percent + '%';
                    
                    const progressText = totalProperties > 0 ? 
                        `${data.progress.processed}/${totalProperties} properties (${percent.toFixed(1)}%) • Success: ${data.performance.success_rate}%` :
                        'Ready for production scraping';
                    document.getElementById('progress-text').textContent = progressText;
                    
                    // Update current property
                    const currentProp = data.progress.current_title || data.progress.current_url || 'None';
                    document.getElementById('current-property').textContent = currentProp.length > 60 ? 
                        currentProp.substring(0, 60) + '...' : currentProp;
                    document.getElementById('current-property-id').textContent = data.progress.current_property_id || '--';
                    
                    // Update timing
                    document.getElementById('elapsed-time').textContent = data.timing.elapsed_time;
                    document.getElementById('estimated-time').textContent = data.timing.estimated_completion;
                    document.getElementById('avg-time').textContent = data.timing.avg_time_per_property + 's';
                    document.getElementById('properties-per-hour').textContent = Math.round(data.timing.properties_per_hour);
                    
                    // Update system info
                    document.getElementById('memory-usage').textContent = data.system.memory_usage.toFixed(1) + '%';
                    
                    // Update logs with enhanced styling
                    const logsContainer = document.getElementById('logs');
                    logsContainer.innerHTML = data.logs.map(log => {
                        let className = 'log-entry';
                        if (log.category === 'success') className += ' log-success';
                        else if (log.category === 'error') className += ' log-error';
                        else if (log.category === 'warning') className += ' log-warning';
                        
                        return `<div class="${className}">[${log.timestamp}] ${log.level}: ${log.message}</div>`;
                    }).join('');
                    logsContainer.scrollTop = logsContainer.scrollHeight;
                    
                    // Update buttons
                    document.getElementById('start-btn').disabled = data.is_running;
                    document.getElementById('pause-btn').disabled = !data.is_running;
                    document.getElementById('stop-btn').disabled = !data.is_running;
                    
                    // Update pause button text
                    const pauseBtn = document.getElementById('pause-btn');
                    if (data.is_paused) {
                        pauseBtn.textContent = '▶️ Resume';
                        isPaused = true;
                    } else {
                        pauseBtn.textContent = '⏸️ Pause';
                        isPaused = false;
                    }
                })
                .catch(error => console.error('Error updating stats:', error));
        }
        
        function loadSourceInfo() {
            fetch('/api/sources')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('source-info');
                    container.innerHTML = '';
                    
                    Object.values(data).forEach(source => {
                        // Update header stats
                        document.getElementById('total-available').textContent = source.total;
                        document.getElementById('total-extracted').textContent = source.extracted;
                        document.getElementById('total-remaining').textContent = source.remaining;
                        document.getElementById('completion-percentage').textContent = source.progress_percentage.toFixed(1) + '%';
                        
                        const div = document.createElement('div');
                        div.className = 'source-info';
                        div.innerHTML = `
                            <strong>${source.name}</strong>
                            <p style="margin: 10px 0;">${source.description}</p>
                            <div class="source-stats">
                                <div class="source-stat">
                                    <div class="source-stat-value">${source.total}</div>
                                    <div class="source-stat-label">Total</div>
                                </div>
                                <div class="source-stat">
                                    <div class="source-stat-value">${source.extracted}</div>
                                    <div class="source-stat-label">Extracted</div>
                                </div>
                                <div class="source-stat">
                                    <div class="source-stat-value">${source.remaining}</div>
                                    <div class="source-stat-label">Remaining</div>
                                </div>
                                <div class="source-stat">
                                    <div class="source-stat-value">${source.progress_percentage.toFixed(1)}%</div>
                                    <div class="source-stat-label">Progress</div>
                                </div>
                            </div>
                            <div class="city-breakdown">
                                ${source.city_breakdown.slice(0, 9).map(([city, count]) => 
                                    `<div class="city-item"><span>${city}</span><span>${count}</span></div>`
                                ).join('')}
                            </div>
                        `;
                        container.appendChild(div);
                        
                        // Populate city filter
                        const cityFilter = document.getElementById('city-filter');
                        cityFilter.innerHTML = '<option value="">All Cities</option>';
                        source.city_breakdown.forEach(([city, count]) => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = `${city} (${count})`;
                            cityFilter.appendChild(option);
                        });
                    });
                })
                .catch(error => console.error('Error loading sources:', error));
        }
        
        function startScraping() {
            const config = {
                max_properties: parseInt(document.getElementById('max-properties').value),
                skip_existing: document.getElementById('skip-existing').value === 'true',
                city_filter: document.getElementById('city-filter').value || null
            };
            
            if (config.max_properties > 500) {
                alert('Maximum 500 properties per batch for safety. Use multiple batches for larger operations.');
                return;
            }
            
            if (config.max_properties > 200) {
                if (!confirm(`You're about to scrape ${config.max_properties} properties. This may take several hours. Continue?`)) {
                    return;
                }
            }
            
            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Failed to start scraping: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error starting scraping:', error);
                alert('Error starting scraping. Check console for details.');
            });
        }
        
        function pauseResumeScraping() {
            const endpoint = isPaused ? '/api/resume' : '/api/pause';
            fetch(endpoint, {method: 'POST'})
                .then(response => response.json())
                .catch(error => console.error('Error pausing/resuming:', error));
        }
        
        function stopScraping() {
            if (confirm('Are you sure you want to stop scraping? Progress will be saved.')) {
                fetch('/api/stop', {method: 'POST'})
                    .then(response => response.json())
                    .catch(error => console.error('Error stopping scraping:', error));
            }
        }
        
        // Initialize dashboard
        loadSourceInfo();
        updateStats();
        setInterval(updateStats, 2000); // Update every 2 seconds
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                startScraping();
            } else if (e.ctrlKey && e.key === ' ') {
                e.preventDefault();
                pauseResumeScraping();
            } else if (e.ctrlKey && e.key === 'Escape') {
                stopScraping();
            }
        });
        
        // Add notification for large batches
        document.getElementById('max-properties').addEventListener('change', function() {
            const value = parseInt(this.value);
            if (value > 300) {
                this.style.borderColor = '#ff6b6b';
                this.title = 'Large batch - will take several hours';
            } else if (value > 100) {
                this.style.borderColor = '#f39c12';
                this.title = 'Medium batch - will take 1-2 hours';
            } else {
                this.style.borderColor = '#e0e0e0';
                this.title = '';
            }
        });
    </script>
</body>
</html>
    '''

@app.route('/api/stats')
def get_stats():
    return jsonify(monitor.get_advanced_stats())

@app.route('/api/sources')
def get_sources():
    return jsonify(monitor.get_comprehensive_sources())

@app.route('/api/start', methods=['POST'])
def start_scraping():
    config = request.json
    success, message = monitor.start_scraping(config)
    return jsonify({'success': success, 'message': message})

@app.route('/api/pause', methods=['POST'])
def pause_scraping():
    success, message = monitor.pause_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/resume', methods=['POST'])
def resume_scraping():
    success, message = monitor.resume_scraping()
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_scraping():
    success, message = monitor.stop_scraping()
    return jsonify({'success': success, 'message': message})

def main():
    print("🚀 PRODUCTION ADVANCED DASHBOARD")
    print("=" * 60)
    print("🌐 Dashboard: http://localhost:5003")
    print("📊 Properties: 2,735 (repaired and ready)")
    print("🔧 Features: Advanced monitoring, pause/resume, city filtering")
    print("⚡ Performance: Real-time metrics, success rates, ETA")
    print("🎯 Ready for 200-300 property testing!")
    
    app.run(debug=True, host='0.0.0.0', port=5003)

if __name__ == "__main__":
    main()
