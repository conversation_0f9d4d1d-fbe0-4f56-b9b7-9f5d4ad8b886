2025-08-12 20:12:16,105 - INFO - Chrome WebDriver initialized successfully (attempt 1)
2025-08-12 20:12:18,072 - INFO - [TIMER] Waiting 4.4 seconds before next page...
2025-08-12 20:12:22,479 - INFO - \n[COMPLETE] DATA QUALITY REPORT
2025-08-12 20:12:22,479 - INFO -    [LIST] Total properties: 28
2025-08-12 20:12:22,479 - INFO -    [SUCCESS] Valid properties: 21
2025-08-12 20:12:22,479 - INFO -    [SHIELD] Validation success rate: 75.0%
2025-08-12 20:12:22,479 - INFO -    [COMPLETE] Average data quality: 96.4%
2025-08-12 20:12:22,525 - ERROR - Error saving to CSV: 'charmap' codec can't encode character '\U0001f4be' in position 0: character maps to <undefined>
2025-08-12 20:12:24,609 - INFO - WebD<PERSON> closed
2025-08-12 20:12:45,104 - INFO - WebD<PERSON> closed
[START] Starting MagicBricks CLI Scraper
   [CITY] City: Gurgaon
   [MODE] Mode: full
   [PAGES] Max pages: 1
   [INDIVIDUAL] Individual pages: False
   [FORCE] Force full: False
   [HEADLESS] Headless: True
   [EXPORT] Export formats: csv

[DATABASE] Incremental Database Schema Enhancement Initialized
[DATE] Date Parsing System Initialized
[DATE] Date Parsing System Initialized
[STOP] Smart Stopping Logic Initialized
[URL] URL Tracking System Initialized
[CONFIG] User Mode Options System Initialized
[SYSTEM] Complete Incremental Scraping System Initialized
============================================================
[DATE] Date Parsing System Initialized
[DATE] Date Parsing System Initialized
[STOP] Smart Stopping Logic Initialized
[URL] URL Tracking System Initialized
[SETUP] Setting up incremental scraping system...
[SETUP] Setting up incremental scraping system...

[STEP1] Step 1: Database Schema Enhancement
[ROCKET] STARTING DATABASE SCHEMA ENHANCEMENT
============================================================
[ERROR] System setup failed: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
[WARNING] Incremental system setup failed - falling back to full scraping
[ROCKET] Integrated MagicBricks Scraper Initialized
   [STATS] Incremental scraping: Enabled
   [CONFIG] Custom configuration: Default
[SUCCESS] Started full scraping session for Gurgaon
[URL] Base URL: https://www.magicbricks.com/property-for-sale-in-gurgaon-pppfs

[PAGE] Scraping page 1: https://www.magicbricks.com/property-for-sale-in-gurgaon-pppfs
   [TARGET] Found 30 properties using selector: .mb-srp__card
   [SUCCESS] Extracted 28 properties from page 1
[STOP] Reached maximum page limit: 1

[REPORT] SCRAPING SESSION COMPLETE
==================================================
[SUCCESS] Mode: full
[SUCCESS] Pages scraped: 1
[SUCCESS] Properties found: 30
[SUCCESS] Properties saved: 28
[SUCCESS] Duration: 0m 6s

[SUCCESS] Scraping completed successfully!
   [STATS] Properties scraped: 28
   [STATS] Pages scraped: 1
   [TIME] Duration: 0m 6s
   [FILE] Output file: No CSV file generated

[STATS] Session Statistics:
   [ID] Session ID: None
   [QUALITY] Data quality: N/A%
   [FOUND] Properties found: 30
   [SAVE] Properties saved: 28
