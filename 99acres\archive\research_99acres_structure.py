#!/usr/bin/env python3
"""
99acres Website Structure Research Script
Analyzes the 99acres website to understand data patterns, CSS selectors, and extraction logic.
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import requests
from datetime import datetime


class NinetyNineAcresResearcher:
    """Research tool for analyzing 99acres website structure"""
    
    def __init__(self, headless=False):
        self.headless = headless
        self.driver = None
        self.research_data = {
            'timestamp': datetime.now().isoformat(),
            'url_patterns': {},
            'css_selectors': {},
            'data_fields': {},
            'pagination': {},
            'anti_detection': {},
            'sample_data': []
        }
    
    def setup_driver(self):
        """Setup Chrome WebDriver for research"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Chrome WebDriver initialized for research")
    
    def analyze_url_patterns(self):
        """Analyze URL patterns for different cities and property types"""
        print("\n🔍 Analyzing URL patterns...")
        
        # Test cities
        test_cities = ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad']
        
        url_patterns = {
            'sale': {},
            'rent': {},
            'pagination': {}
        }
        
        for city in test_cities:
            # Sale URLs
            sale_url = f"https://www.99acres.com/property-for-sale-in-{city}-ffid"
            rent_url = f"https://www.99acres.com/property-for-rent-in-{city}-ffid"
            
            url_patterns['sale'][city] = sale_url
            url_patterns['rent'][city] = rent_url
            
            print(f"   📍 {city.title()}: Sale - {sale_url}")
            print(f"   📍 {city.title()}: Rent - {rent_url}")
        
        self.research_data['url_patterns'] = url_patterns
        return url_patterns
    
    def analyze_property_cards(self, url):
        """Analyze property card structure and CSS selectors"""
        print(f"\n🔍 Analyzing property cards on: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)  # Wait for page load
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Try to identify property cards
            potential_selectors = [
                'div[data-testid="property-card"]',
                '.property-card',
                '.srpTuple',
                '.tuple',
                '.property-item',
                '.listing-card',
                '[class*="property"]',
                '[class*="card"]',
                '[class*="tuple"]'
            ]
            
            property_cards = []
            working_selector = None
            
            for selector in potential_selectors:
                cards = soup.select(selector)
                if cards and len(cards) > 5:  # Need multiple cards
                    property_cards = cards
                    working_selector = selector
                    print(f"   ✅ Found {len(cards)} property cards with selector: {selector}")
                    break
            
            if not property_cards:
                print("   ❌ Could not identify property cards")
                return None
            
            # Analyze first few cards for data structure
            sample_cards = property_cards[:3]
            card_analysis = []
            
            for i, card in enumerate(sample_cards):
                print(f"\n   📋 Analyzing card {i+1}:")
                card_data = self.extract_card_data(card)
                card_analysis.append(card_data)
                
                # Print found data
                for field, value in card_data.items():
                    if value:
                        print(f"      {field}: {value[:100]}...")
            
            self.research_data['css_selectors']['property_cards'] = working_selector
            self.research_data['sample_data'] = card_analysis
            
            return card_analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing property cards: {str(e)}")
            return None
    
    def extract_card_data(self, card):
        """Extract data from a single property card"""
        data = {}
        
        # Common field patterns to look for
        field_patterns = {
            'title': ['h1', 'h2', 'h3', '.title', '.property-title', '[class*="title"]'],
            'price': ['.price', '.cost', '[class*="price"]', '[class*="cost"]'],
            'area': ['.area', '.size', '[class*="area"]', '[class*="size"]'],
            'location': ['.location', '.address', '[class*="location"]', '[class*="address"]'],
            'bedrooms': ['.bedroom', '.bhk', '[class*="bedroom"]', '[class*="bhk"]'],
            'bathrooms': ['.bathroom', '.bath', '[class*="bathroom"]', '[class*="bath"]'],
            'contact': ['.contact', '.phone', '[class*="contact"]', '[class*="phone"]'],
            'image': ['img', '.image', '[class*="image"]'],
            'link': ['a', '[href]']
        }
        
        for field, selectors in field_patterns.items():
            for selector in selectors:
                elements = card.select(selector)
                if elements:
                    if field == 'image':
                        data[field] = elements[0].get('src', '') or elements[0].get('data-src', '')
                    elif field == 'link':
                        data[field] = elements[0].get('href', '')
                    else:
                        data[field] = elements[0].get_text(strip=True)
                    break
        
        return data
    
    def analyze_pagination(self, url):
        """Analyze pagination structure"""
        print(f"\n🔍 Analyzing pagination on: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Look for pagination elements
            pagination_selectors = [
                '.pagination',
                '.pager',
                '[class*="pagination"]',
                '[class*="pager"]',
                'nav',
                '.page-numbers'
            ]
            
            pagination_info = {}
            
            for selector in pagination_selectors:
                elements = soup.select(selector)
                if elements:
                    pagination_info[selector] = len(elements)
                    print(f"   📄 Found pagination with selector: {selector} ({len(elements)} elements)")
            
            # Look for next/previous buttons
            next_selectors = ['[class*="next"]', '[class*="forward"]', 'a[href*="page"]']
            for selector in next_selectors:
                elements = soup.select(selector)
                if elements:
                    pagination_info[f'next_{selector}'] = len(elements)
            
            self.research_data['pagination'] = pagination_info
            return pagination_info
            
        except Exception as e:
            print(f"   ❌ Error analyzing pagination: {str(e)}")
            return None
    
    def run_comprehensive_research(self):
        """Run comprehensive research on 99acres website"""
        print("🚀 Starting comprehensive 99acres research...")
        print("="*60)
        
        try:
            # Setup driver
            self.setup_driver()
            
            # 1. Analyze URL patterns
            url_patterns = self.analyze_url_patterns()
            
            # 2. Analyze property cards on Mumbai sale page
            mumbai_sale_url = url_patterns['sale']['mumbai']
            card_analysis = self.analyze_property_cards(mumbai_sale_url)
            
            # 3. Analyze pagination
            pagination_analysis = self.analyze_pagination(mumbai_sale_url)
            
            # 4. Save research data
            self.save_research_data()
            
            print("\n✅ Research completed successfully!")
            print(f"📊 Research data saved to: 99acres_research_data.json")
            
        except Exception as e:
            print(f"❌ Research failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def save_research_data(self):
        """Save research data to JSON file"""
        with open('data/99acres_research_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.research_data, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    researcher = NinetyNineAcresResearcher(headless=False)
    researcher.run_comprehensive_research()
