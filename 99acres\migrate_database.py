#!/usr/bin/env python3
"""
Migrate database to support all 50+ fields
"""

import sqlite3
import os

def migrate_database():
    """Migrate database to new schema"""
    db_path = 'data/99acres_properties.db'
    
    if os.path.exists(db_path):
        # Backup existing database
        backup_path = 'data/99acres_properties_backup.db'
        print(f"📦 Backing up existing database to {backup_path}")
        
        # Copy existing data
        conn_old = sqlite3.connect(db_path)
        conn_backup = sqlite3.connect(backup_path)
        conn_old.backup(conn_backup)
        conn_backup.close()
        conn_old.close()
        
        # Remove old database
        os.remove(db_path)
        print(f"🗑️ Removed old database")
    
    print(f"✅ Database migration complete. New schema will be created on next run.")

if __name__ == "__main__":
    migrate_database()
