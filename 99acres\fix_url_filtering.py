#!/usr/bin/env python3
"""
Fix URL Filtering Issue
Investigate and fix the URL filtering that's hiding 2000+ properties
"""

import sqlite3

def analyze_url_patterns():
    """Analyze URL patterns to understand the filtering issue"""
    print("🔍 ANALYZING URL PATTERNS")
    print("=" * 50)
    
    conn = sqlite3.connect('data/99acres_properties.db')
    cursor = conn.cursor()
    
    # Check supposedly malformed URLs
    print("1. Checking 'malformed' URLs:")
    cursor.execute("SELECT property_url FROM properties WHERE property_url LIKE '%httpswww%' LIMIT 10")
    malformed = cursor.fetchall()
    for i, (url,) in enumerate(malformed, 1):
        print(f"   {i}. {url}")
    
    # Check valid URLs
    print("\n2. Checking 'valid' URLs:")
    cursor.execute("SELECT property_url FROM properties WHERE property_url NOT LIKE '%httpswww%' LIMIT 10")
    valid = cursor.fetchall()
    for i, (url,) in enumerate(valid, 1):
        print(f"   {i}. {url}")
    
    # Check URL patterns
    print("\n3. URL pattern analysis:")
    cursor.execute("SELECT DISTINCT SUBSTR(property_url, 1, 30) as pattern, COUNT(*) FROM properties GROUP BY pattern ORDER BY COUNT(*) DESC LIMIT 10")
    patterns = cursor.fetchall()
    for pattern, count in patterns:
        print(f"   {pattern}... : {count} URLs")
    
    conn.close()

def fix_url_filtering():
    """Create proper URL filtering logic"""
    print("\n🔧 CREATING PROPER URL FILTERING")
    print("=" * 50)
    
    conn = sqlite3.connect('data/99acres_properties.db')
    cursor = conn.cursor()
    
    # Test different filtering approaches
    filters = [
        ("No filter", "property_url IS NOT NULL AND property_url != ''"),
        ("Current filter", "property_url IS NOT NULL AND property_url != '' AND property_url NOT LIKE '%httpswww%'"),
        ("Starts with https", "property_url LIKE 'https://www.99acres.com/%'"),
        ("Contains 99acres", "property_url LIKE '%99acres.com%'"),
        ("Valid format", "property_url LIKE 'https://www.99acres.com/%' AND LENGTH(property_url) > 50")
    ]
    
    for name, filter_sql in filters:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {filter_sql}")
        count = cursor.fetchone()[0]
        print(f"   {name}: {count} URLs")
    
    # Get sample of each
    print("\n4. Sample URLs for each filter:")
    for name, filter_sql in filters:
        print(f"\n   {name} samples:")
        cursor.execute(f"SELECT property_url FROM properties WHERE {filter_sql} LIMIT 3")
        samples = cursor.fetchall()
        for url, in samples:
            print(f"     {url}")
    
    conn.close()

def create_fixed_dashboard():
    """Create dashboard with proper URL filtering"""
    print("\n🚀 CREATING FIXED DASHBOARD")
    print("=" * 50)
    
    # The correct filter should be:
    correct_filter = "property_url LIKE 'https://www.99acres.com/%' AND LENGTH(property_url) > 50"
    
    conn = sqlite3.connect('data/99acres_properties.db')
    cursor = conn.cursor()
    cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {correct_filter}")
    total_available = cursor.fetchone()[0]
    
    # Check extracted count
    try:
        conn2 = sqlite3.connect('data/individual_properties_comprehensive.db')
        cursor2 = conn2.cursor()
        cursor2.execute("SELECT COUNT(*) FROM individual_properties")
        extracted = cursor2.fetchone()[0]
        conn2.close()
    except:
        extracted = 0
    
    print(f"✅ FIXED NUMBERS:")
    print(f"   Total Available: {total_available} properties")
    print(f"   Already Extracted: {extracted} properties") 
    print(f"   Remaining: {total_available - extracted} properties")
    print(f"   Progress: {(extracted/total_available)*100:.1f}%")
    
    conn.close()
    
    return total_available, extracted

def main():
    """Main function"""
    analyze_url_patterns()
    fix_url_filtering()
    total, extracted = create_fixed_dashboard()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 50)
    print(f"❌ PROBLEM: Filtering logic was hiding {total-132} properties!")
    print(f"✅ SOLUTION: Use proper URL validation instead of 'httpswww' filter")
    print(f"📊 RESULT: {total} properties available for scraping")
    print(f"🔧 ACTION: Update dashboard to use correct filtering")

if __name__ == "__main__":
    main()
