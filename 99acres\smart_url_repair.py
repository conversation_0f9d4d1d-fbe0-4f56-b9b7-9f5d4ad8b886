#!/usr/bin/env python3
"""
Smart URL Repair
Intelligently repair URLs while handling duplicates and constraints
"""

import sqlite3

def smart_repair_urls():
    """Smart repair that handles duplicates and constraints"""
    print("🔧 SMART URL REPAIR WITH DUPLICATE HANDLING")
    print("=" * 60)
    
    conn = sqlite3.connect('data/99acres_properties.db')
    cursor = conn.cursor()
    
    # First, let's understand the constraint
    cursor.execute("PRAGMA table_info(properties)")
    columns = cursor.fetchall()
    print("Table structure:")
    for col in columns:
        print(f"  {col}")
    
    # Check for existing duplicates
    cursor.execute("""
        SELECT property_url, COUNT(*) 
        FROM properties 
        WHERE property_url LIKE 'https://www.99acres.com/%'
        GROUP BY property_url 
        HAVING COUNT(*) > 1
    """)
    existing_duplicates = cursor.fetchall()
    print(f"\nExisting duplicates in valid URLs: {len(existing_duplicates)}")
    
    # Find malformed URLs and their potential fixed versions
    cursor.execute("SELECT id, property_url FROM properties WHERE property_url LIKE 'httpswww.99acres.com%'")
    malformed_urls = cursor.fetchall()
    print(f"Malformed URLs to fix: {len(malformed_urls)}")
    
    # Check which fixed URLs would create duplicates
    potential_duplicates = []
    safe_to_fix = []
    
    for property_id, malformed_url in malformed_urls:
        fixed_url = malformed_url.replace('httpswww.99acres.com', 'https://www.99acres.com/')
        
        # Check if this fixed URL already exists
        cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url = ?", (fixed_url,))
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            potential_duplicates.append((property_id, malformed_url, fixed_url))
        else:
            safe_to_fix.append((property_id, malformed_url, fixed_url))
    
    print(f"Safe to fix: {len(safe_to_fix)}")
    print(f"Would create duplicates: {len(potential_duplicates)}")
    
    # Strategy 1: Fix safe URLs first
    print(f"\n📝 Fixing {len(safe_to_fix)} safe URLs...")
    fixed_count = 0
    for property_id, malformed_url, fixed_url in safe_to_fix:
        try:
            cursor.execute("UPDATE properties SET property_url = ? WHERE id = ?", (fixed_url, property_id))
            fixed_count += 1
            if fixed_count % 100 == 0:
                print(f"  Fixed {fixed_count}/{len(safe_to_fix)} URLs...")
        except Exception as e:
            print(f"  Error fixing ID {property_id}: {str(e)}")
    
    conn.commit()
    print(f"✅ Fixed {fixed_count} URLs without conflicts")
    
    # Strategy 2: Handle duplicates by removing the malformed ones
    print(f"\n🗑️ Handling {len(potential_duplicates)} potential duplicates...")
    removed_count = 0
    for property_id, malformed_url, fixed_url in potential_duplicates:
        try:
            # Remove the malformed entry since we already have the correct one
            cursor.execute("DELETE FROM properties WHERE id = ?", (property_id,))
            removed_count += 1
            if removed_count % 100 == 0:
                print(f"  Removed {removed_count}/{len(potential_duplicates)} duplicates...")
        except Exception as e:
            print(f"  Error removing ID {property_id}: {str(e)}")
    
    conn.commit()
    print(f"✅ Removed {removed_count} duplicate entries")
    
    # Final verification
    cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE 'https://www.99acres.com/%'")
    total_valid = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE 'httpswww.99acres.com%'")
    remaining_malformed = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_properties = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"\n🎯 REPAIR SUMMARY:")
    print(f"   Total Properties: {total_properties}")
    print(f"   Valid URLs: {total_valid}")
    print(f"   Remaining Malformed: {remaining_malformed}")
    print(f"   URLs Fixed: {fixed_count}")
    print(f"   Duplicates Removed: {removed_count}")
    
    return total_valid

def verify_repair():
    """Verify the repair was successful"""
    print(f"\n🔍 VERIFICATION")
    print("=" * 30)
    
    conn = sqlite3.connect('data/99acres_properties.db')
    cursor = conn.cursor()
    
    # Check URL distribution
    cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE 'https://www.99acres.com/%'")
    valid_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url LIKE 'httpswww.99acres.com%'")
    malformed_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_count = cursor.fetchone()[0]
    
    print(f"Total Properties: {total_count}")
    print(f"Valid URLs: {valid_count} ({(valid_count/total_count)*100:.1f}%)")
    print(f"Malformed URLs: {malformed_count} ({(malformed_count/total_count)*100:.1f}%)")
    
    # Sample valid URLs
    cursor.execute("SELECT property_url FROM properties WHERE property_url LIKE 'https://www.99acres.com/%' LIMIT 5")
    samples = cursor.fetchall()
    print(f"\nSample valid URLs:")
    for i, (url,) in enumerate(samples, 1):
        print(f"  {i}. {url}")
    
    # Check for any remaining issues
    cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NULL OR property_url = ''")
    null_count = cursor.fetchone()[0]
    print(f"\nNull/Empty URLs: {null_count}")
    
    conn.close()
    
    return valid_count

def main():
    """Main repair function"""
    print("🚀 SMART URL REPAIR SYSTEM")
    print("=" * 60)
    
    total_valid = smart_repair_urls()
    final_count = verify_repair()
    
    print(f"\n✅ REPAIR COMPLETE!")
    print(f"🎯 Result: {final_count} valid URLs ready for scraping")
    print(f"📈 Improvement: From 132 to {final_count} URLs ({((final_count-132)/132)*100:.1f}% increase)")
    
    if final_count > 2000:
        print(f"🎉 SUCCESS! Recovered 2000+ properties for scraping!")
    
    return final_count

if __name__ == "__main__":
    main()
