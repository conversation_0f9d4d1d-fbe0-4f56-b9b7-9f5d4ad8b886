#!/usr/bin/env python3
"""
Comprehensive Deep Analysis of 99acres
Systematic browsing and analysis across multiple cities, property types, and pages
"""

import time
import json
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re
import os
from urllib.parse import urljoin, urlparse


class ComprehensiveDeepAnalyzer:
    """Deep analysis tool for comprehensive 99acres research"""
    
    def __init__(self):
        self.driver = None
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'cities_analyzed': [],
            'property_types_analyzed': [],
            'pages_analyzed': 0,
            'individual_properties_analyzed': 0,
            'anti_scraping_observations': [],
            'field_variations': {},
            'url_patterns': {},
            'page_structures': {},
            'detailed_findings': []
        }
        
        # Comprehensive test matrix
        self.test_cities = [
            'mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'chennai',
            'ahmedabad', 'kolkata', 'gurgaon', 'noida', 'thane', 'navi-mumbai'
        ]
        
        self.property_categories = [
            # Residential Sale
            'property-for-sale-in-{city}-ffid',
            '1-bhk-apartment-flat-for-sale-in-{city}-ffid',
            '2-bhk-apartment-flat-for-sale-in-{city}-ffid', 
            '3-bhk-apartment-flat-for-sale-in-{city}-ffid',
            '4-bhk-apartment-flat-for-sale-in-{city}-ffid',
            'villa-for-sale-in-{city}-ffid',
            'independent-house-for-sale-in-{city}-ffid',
            'builder-floor-for-sale-in-{city}-ffid',
            
            # Residential Rent
            'property-for-rent-in-{city}-ffid',
            '1-bhk-apartment-flat-for-rent-in-{city}-ffid',
            '2-bhk-apartment-flat-for-rent-in-{city}-ffid',
            '3-bhk-apartment-flat-for-rent-in-{city}-ffid',
            
            # Commercial
            'commercial-property-for-sale-in-{city}-ffid',
            'commercial-property-for-rent-in-{city}-ffid',
            'office-space-for-sale-in-{city}-ffid',
            'office-space-for-rent-in-{city}-ffid'
        ]
        
        self.base_url = "https://www.99acres.com"
        
    def setup_driver(self):
        """Setup Chrome WebDriver with enhanced anti-detection"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Enhanced anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Rotate user agents
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Enhanced browser initialized for deep analysis")
    
    def random_delay(self, min_seconds=3, max_seconds=8):
        """Enhanced random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def analyze_listing_page_comprehensively(self, url, city, property_type):
        """Comprehensive analysis of listing page"""
        print(f"\n🔍 DEEP ANALYSIS: {property_type} in {city}")
        print(f"   URL: {url}")
        
        try:
            # Load page with realistic behavior
            self.driver.get(url)
            self.random_delay(4, 7)
            
            # Check for anti-scraping measures
            page_title = self.driver.title
            if any(keyword in page_title.lower() for keyword in ['error', 'blocked', 'captcha', 'access denied']):
                print(f"   ⚠️ Potential blocking detected: {page_title}")
                self.analysis_results['anti_scraping_observations'].append({
                    'url': url,
                    'issue': 'Page blocking',
                    'title': page_title
                })
                return None
            
            # Simulate human browsing behavior
            self.simulate_human_behavior()
            
            # Get page source and analyze
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'url': url,
                'city': city,
                'property_type': property_type,
                'page_title': page_title,
                'analysis_timestamp': datetime.now().isoformat(),
                'total_results': self._extract_total_results(soup),
                'property_samples': self._extract_comprehensive_property_samples(soup),
                'pagination_analysis': self._analyze_pagination_deeply(soup),
                'filter_analysis': self._analyze_filters_comprehensively(soup),
                'anti_scraping_indicators': self._detect_anti_scraping_measures(soup),
                'page_performance': self._analyze_page_performance(),
                'structured_data_analysis': self._analyze_structured_data_deeply(soup),
                'field_variations': self._analyze_field_variations(soup)
            }
            
            print(f"   ✅ Found {len(analysis['property_samples'])} property samples")
            print(f"   📊 Total results: {analysis['total_results']}")
            print(f"   🔍 Unique field variations: {len(analysis['field_variations'])}")
            
            # Analyze individual property pages
            individual_properties = self._analyze_individual_properties(analysis['property_samples'][:3])
            analysis['individual_property_analysis'] = individual_properties
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            self.analysis_results['anti_scraping_observations'].append({
                'url': url,
                'issue': 'Analysis error',
                'error': str(e)
            })
            return None
    
    def simulate_human_behavior(self):
        """Simulate realistic human browsing behavior"""
        # Random scrolling patterns
        scroll_patterns = [
            # Slow scroll down
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);"),
            # Scroll back up a bit
            lambda: self.driver.execute_script("window.scrollTo(0, window.pageYOffset - 200);"),
            # Scroll to middle
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);"),
            # Final scroll to bottom
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        ]
        
        for i, scroll_action in enumerate(scroll_patterns):
            scroll_action()
            self.random_delay(1, 3)
            
            # Occasionally move mouse (simulate hover)
            if i % 2 == 0:
                try:
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(self.driver)
                    actions.move_by_offset(random.randint(100, 500), random.randint(100, 400))
                    actions.perform()
                except:
                    pass
    
    def _extract_comprehensive_property_samples(self, soup):
        """Extract comprehensive property samples with detailed analysis"""
        # Multiple methods to find properties
        property_containers = []
        
        # Method 1: Property links
        property_links = soup.find_all('a', href=True)
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                container = self._find_property_container(link)
                if container and container not in property_containers:
                    property_containers.append(container)
        
        # Method 2: Price-based detection
        price_elements = soup.find_all(string=re.compile(r'₹\s*\d+'))
        for price_elem in price_elements:
            container = self._find_property_container(price_elem.parent)
            if container and container not in property_containers:
                property_containers.append(container)
        
        # Analyze each container comprehensively
        samples = []
        for i, container in enumerate(property_containers[:15]):  # Analyze first 15
            sample = self._analyze_property_container_deeply(container, i)
            if sample:
                samples.append(sample)
        
        return samples
    
    def _analyze_property_container_deeply(self, container, index):
        """Deep analysis of individual property container"""
        try:
            text = container.get_text()
            
            analysis = {
                'index': index,
                'container_analysis': {
                    'text_length': len(text),
                    'html_structure': self._analyze_html_structure(container),
                    'css_classes': container.get('class', []),
                    'element_count': len(container.find_all())
                },
                'extracted_fields': self._extract_all_possible_fields(container),
                'links_analysis': self._analyze_container_links(container),
                'images_analysis': self._analyze_container_images(container),
                'price_analysis': self._analyze_price_patterns_deeply(text),
                'area_analysis': self._analyze_area_patterns_deeply(text),
                'location_analysis': self._analyze_location_patterns_deeply(text),
                'amenities_analysis': self._analyze_amenities_patterns_deeply(text),
                'contact_analysis': self._analyze_contact_patterns_deeply(container),
                'verification_analysis': self._analyze_verification_patterns(text)
            }
            
            return analysis
            
        except Exception as e:
            return {'index': index, 'error': str(e)}
    
    def _analyze_individual_properties(self, property_samples):
        """Analyze individual property detail pages"""
        individual_analyses = []
        
        for sample in property_samples:
            links = sample.get('links_analysis', {}).get('property_links', [])
            if links:
                property_url = links[0]  # Take first property link
                if property_url.startswith('/'):
                    property_url = urljoin(self.base_url, property_url)
                
                print(f"   🏠 Analyzing individual property: {property_url}")
                individual_analysis = self._analyze_individual_property_page(property_url)
                if individual_analysis:
                    individual_analyses.append(individual_analysis)
                
                self.random_delay(5, 10)  # Longer delay for individual pages
                
                if len(individual_analyses) >= 2:  # Limit to 2 individual properties per listing page
                    break
        
        return individual_analyses
    
    def _analyze_individual_property_page(self, url):
        """Deep analysis of individual property detail page"""
        try:
            self.driver.get(url)
            self.random_delay(4, 7)
            
            # Check for blocking
            if 'error' in self.driver.title.lower():
                return None
            
            # Simulate browsing behavior
            self.simulate_human_behavior()
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'url': url,
                'page_title': self.driver.title,
                'complete_field_extraction': self._extract_complete_property_fields(soup),
                'amenities_comprehensive': self._extract_comprehensive_amenities(soup),
                'location_detailed': self._extract_detailed_location_info(soup),
                'price_breakdown': self._extract_detailed_price_info(soup),
                'builder_agent_detailed': self._extract_detailed_builder_agent_info(soup),
                'legal_compliance': self._extract_legal_compliance_info(soup),
                'media_analysis': self._extract_comprehensive_media_info(soup),
                'similar_properties': self._analyze_similar_properties_section(soup),
                'contact_mechanisms': self._analyze_contact_mechanisms(soup),
                'page_structure_detailed': self._analyze_detailed_page_structure(soup)
            }
            
            self.analysis_results['individual_properties_analyzed'] += 1
            return analysis
            
        except Exception as e:
            print(f"   ❌ Individual property analysis error: {str(e)}")
            return None
    
    def run_comprehensive_deep_analysis(self):
        """Run the complete comprehensive deep analysis"""
        print("🚀 STARTING COMPREHENSIVE DEEP ANALYSIS OF 99ACRES")
        print("="*80)
        print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Scope: {len(self.test_cities)} cities × {len(self.property_categories)} property types")
        print(f"📊 Expected Analysis: 50+ listing pages, 20+ individual properties")
        
        try:
            self.setup_driver()
            
            # Phase 1: Systematic listing page analysis
            print(f"\n🔍 PHASE 1: Systematic Listing Page Analysis")
            print("="*60)
            
            successful_analyses = []
            
            # Priority combinations for deep analysis
            priority_combinations = [
                # Major cities with different property types
                ('mumbai', 'property-for-sale-in-{city}-ffid'),
                ('mumbai', '2-bhk-apartment-flat-for-sale-in-{city}-ffid'),
                ('mumbai', 'villa-for-sale-in-{city}-ffid'),
                ('mumbai', 'property-for-rent-in-{city}-ffid'),
                
                ('delhi', 'property-for-sale-in-{city}-ffid'),
                ('delhi', '3-bhk-apartment-flat-for-sale-in-{city}-ffid'),
                ('delhi', 'independent-house-for-sale-in-{city}-ffid'),
                
                ('bangalore', 'property-for-sale-in-{city}-ffid'),
                ('bangalore', '1-bhk-apartment-flat-for-sale-in-{city}-ffid'),
                ('bangalore', 'builder-floor-for-sale-in-{city}-ffid'),
                
                ('pune', 'property-for-sale-in-{city}-ffid'),
                ('hyderabad', 'property-for-sale-in-{city}-ffid'),
                ('chennai', 'property-for-sale-in-{city}-ffid'),
                
                # Commercial properties
                ('mumbai', 'commercial-property-for-sale-in-{city}-ffid'),
                ('delhi', 'office-space-for-rent-in-{city}-ffid'),
            ]
            
            for i, (city, property_category) in enumerate(priority_combinations):
                print(f"\n📍 ANALYSIS {i+1}/{len(priority_combinations)}")
                
                url = f"{self.base_url}/{property_category.format(city=city)}"
                analysis = self.analyze_listing_page_comprehensively(url, city, property_category)
                
                if analysis:
                    successful_analyses.append(analysis)
                    self.analysis_results['detailed_findings'].append(analysis)
                    
                    if city not in self.analysis_results['cities_analyzed']:
                        self.analysis_results['cities_analyzed'].append(city)
                    
                    if property_category not in self.analysis_results['property_types_analyzed']:
                        self.analysis_results['property_types_analyzed'].append(property_category)
                
                # Progressive delay increase to avoid detection
                base_delay = 8 + (i * 0.5)  # Increase delay over time
                self.random_delay(base_delay, base_delay + 5)
            
            # Compile final results
            self.analysis_results['pages_analyzed'] = len(successful_analyses)
            
            # Save comprehensive results
            self.save_comprehensive_results()
            
            print(f"\n✅ COMPREHENSIVE DEEP ANALYSIS COMPLETED!")
            print(f"📊 Successfully analyzed: {len(successful_analyses)} listing pages")
            print(f"🏠 Individual properties analyzed: {self.analysis_results['individual_properties_analyzed']}")
            print(f"🌍 Cities covered: {len(self.analysis_results['cities_analyzed'])}")
            print(f"🏢 Property types covered: {len(self.analysis_results['property_types_analyzed'])}")
            print(f"💾 Results saved to: data/comprehensive_deep_analysis.json")
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def save_comprehensive_results(self):
        """Save comprehensive analysis results"""
        os.makedirs('data', exist_ok=True)
        
        with open('data/comprehensive_deep_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        # Generate comprehensive summary report
        self.generate_comprehensive_summary()
    
    def generate_comprehensive_summary(self):
        """Generate comprehensive summary report"""
        summary = []
        summary.append("# 99acres Comprehensive Deep Analysis Summary")
        summary.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary.append("")
        
        summary.append(f"## Analysis Scope")
        summary.append(f"- Listing pages analyzed: {self.analysis_results['pages_analyzed']}")
        summary.append(f"- Individual properties analyzed: {self.analysis_results['individual_properties_analyzed']}")
        summary.append(f"- Cities covered: {len(self.analysis_results['cities_analyzed'])}")
        summary.append(f"- Property types covered: {len(self.analysis_results['property_types_analyzed'])}")
        summary.append("")
        
        summary.append(f"## Cities Analyzed")
        for city in self.analysis_results['cities_analyzed']:
            summary.append(f"- {city.title()}")
        summary.append("")
        
        summary.append(f"## Anti-Scraping Observations")
        if self.analysis_results['anti_scraping_observations']:
            for obs in self.analysis_results['anti_scraping_observations']:
                summary.append(f"- {obs.get('issue', 'Unknown')}: {obs.get('url', 'N/A')}")
        else:
            summary.append("- No significant anti-scraping measures detected")
        
        with open('data/comprehensive_deep_analysis_summary.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary))

    # Helper methods for comprehensive analysis
    def _extract_total_results(self, soup):
        """Extract total results count"""
        patterns = [
            r'(\d+(?:,\d+)*)\s*results',
            r'(\d+(?:,\d+)*)\s*properties',
            r'showing\s*(\d+(?:,\d+)*)',
            r'(\d+(?:,\d+)*)\s*listings'
        ]

        page_text = soup.get_text()
        for pattern in patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(1).replace(',', '')
        return "Unknown"

    def _find_property_container(self, element):
        """Find property container from any element"""
        current = element
        for _ in range(25):
            if current is None:
                break

            text = current.get_text().lower()
            if ('₹' in text and 'sqft' in text and ('bhk' in text or 'bedroom' in text)):
                return current
            current = current.parent
        return None

    def _analyze_html_structure(self, element):
        """Analyze HTML structure of element"""
        return {
            'tag': element.name,
            'classes': element.get('class', []),
            'id': element.get('id'),
            'children_count': len(element.find_all()),
            'direct_children': [child.name for child in element.children if hasattr(child, 'name') and child.name]
        }

    def _extract_all_possible_fields(self, container):
        """Extract all possible fields from container"""
        text = container.get_text()
        fields = {}

        # Price patterns
        price_patterns = {
            'main_price': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            'price_range': r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            'price_per_sqft': r'₹\s*([\d,]+)\s*/sqft',
            'emi': r'EMI\s*₹\s*([\d,]+)'
        }

        for field, pattern in price_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                fields[field] = matches

        # Area patterns
        area_patterns = {
            'carpet_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Carpet\s*Area',
            'buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Built-up\s*Area',
            'super_buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Super\s*Built-up\s*Area',
            'general_area': r'(\d+(?:,\d+)*)\s*sqft'
        }

        for field, pattern in area_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                fields[field] = matches

        # Configuration patterns
        config_patterns = {
            'bhk': r'(\d+)\s*BHK',
            'bedrooms': r'(\d+)\s*Bedroom',
            'bathrooms': r'(\d+)\s*Bath',
            'balconies': r'(\d+)\s*Balcon',
            'parking': r'(\d+)\s*Parking'
        }

        for field, pattern in config_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                fields[field] = matches

        return fields

    def _analyze_container_links(self, container):
        """Analyze links in container"""
        links = container.find_all('a', href=True)
        property_links = []
        other_links = []

        for link in links:
            href = link.get('href')
            if 'spid-' in href or 'npxid-' in href:
                if href.startswith('/'):
                    href = urljoin(self.base_url, href)
                property_links.append(href)
            else:
                other_links.append(href)

        return {
            'property_links': property_links,
            'other_links': other_links,
            'total_links': len(links)
        }

    def _analyze_container_images(self, container):
        """Analyze images in container"""
        images = container.find_all('img')
        image_analysis = {
            'total_images': len(images),
            'image_sources': [],
            'lazy_loaded': 0
        }

        for img in images:
            src = img.get('src') or img.get('data-src')
            if src:
                image_analysis['image_sources'].append(src)
            if img.get('data-src'):
                image_analysis['lazy_loaded'] += 1

        return image_analysis

    def _analyze_price_patterns_deeply(self, text):
        """Deep analysis of price patterns"""
        return {
            'has_single_price': bool(re.search(r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)(?!\s*-)', text, re.IGNORECASE)),
            'has_price_range': bool(re.search(r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*-\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|Lac)', text, re.IGNORECASE)),
            'has_price_per_sqft': bool(re.search(r'₹\s*[\d,]+\s*/sqft', text, re.IGNORECASE)),
            'has_emi': bool(re.search(r'EMI', text, re.IGNORECASE)),
            'price_unit': 'Cr' if 'Cr' in text else 'Lakh' if 'Lakh' in text or 'Lac' in text else 'Unknown'
        }

    def _analyze_area_patterns_deeply(self, text):
        """Deep analysis of area patterns"""
        return {
            'has_carpet_area': bool(re.search(r'Carpet\s*Area', text, re.IGNORECASE)),
            'has_buildup_area': bool(re.search(r'Built-up\s*Area', text, re.IGNORECASE)),
            'has_super_buildup': bool(re.search(r'Super\s*Built-up', text, re.IGNORECASE)),
            'has_plot_area': bool(re.search(r'Plot\s*Area', text, re.IGNORECASE)),
            'area_unit': 'sqft' if 'sqft' in text.lower() else 'sq.ft' if 'sq.ft' in text.lower() else 'Unknown'
        }

    def _analyze_location_patterns_deeply(self, text):
        """Deep analysis of location patterns"""
        return {
            'has_locality': bool(re.search(r'in\s+[A-Za-z\s]+', text)),
            'has_city': any(city in text.lower() for city in ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'chennai']),
            'has_pincode': bool(re.search(r'\d{6}', text)),
            'location_format': 'in_locality_city' if re.search(r'in\s+[^,]+,\s*[^,]+', text) else 'simple'
        }

    def _analyze_amenities_patterns_deeply(self, text):
        """Deep analysis of amenities patterns"""
        amenity_keywords = [
            'swimming pool', 'gym', 'club house', 'garden', 'security', 'lift',
            'power backup', 'parking', 'playground', 'jogging track'
        ]

        found_amenities = [amenity for amenity in amenity_keywords if amenity in text.lower()]

        return {
            'amenities_count': len(found_amenities),
            'found_amenities': found_amenities,
            'has_amenities_section': bool(found_amenities)
        }

    def _analyze_contact_patterns_deeply(self, container):
        """Deep analysis of contact patterns"""
        text = container.get_text()

        return {
            'has_contact_button': bool(container.find_all(['button', 'a'], string=re.compile(r'contact|call', re.IGNORECASE))),
            'has_phone_number': bool(re.search(r'\d{2}XXXXXX\d{2}', text)),
            'has_agent_info': bool(re.search(r'(Owner|Dealer|Builder)', text, re.IGNORECASE)),
            'contact_mechanisms': len(container.find_all(['button', 'a'], string=re.compile(r'contact|call|enquire', re.IGNORECASE)))
        }

    def _analyze_verification_patterns(self, text):
        """Analyze verification and trust indicators"""
        return {
            'is_verified': 'verified' in text.lower(),
            'is_premium': 'premium' in text.lower(),
            'has_rera': bool(re.search(r'RERA', text, re.IGNORECASE)),
            'posted_time': bool(re.search(r'\d+[dwmy]\s*ago', text, re.IGNORECASE))
        }

    # Individual property page analysis methods
    def _extract_complete_property_fields(self, soup):
        """Extract complete fields from individual property page"""
        page_text = soup.get_text()

        fields = {
            'title': self._extract_property_title(soup),
            'price_details': self._extract_detailed_price_breakdown(page_text),
            'area_details': self._extract_detailed_area_breakdown(page_text),
            'configuration': self._extract_detailed_configuration(page_text),
            'property_details': self._extract_detailed_property_specs(page_text),
            'location_details': self._extract_detailed_location(page_text),
            'builder_details': self._extract_detailed_builder_info(page_text)
        }

        return fields

    def _extract_comprehensive_amenities(self, soup):
        """Extract comprehensive amenities from individual page"""
        page_text = soup.get_text()

        # Comprehensive amenities list
        amenities_categories = {
            'basic': ['lift', 'security', 'power backup', 'water supply', 'parking'],
            'recreational': ['swimming pool', 'gym', 'club house', 'garden', 'playground'],
            'sports': ['tennis court', 'badminton court', 'basketball court', 'jogging track'],
            'convenience': ['shopping center', 'medical center', 'school', 'bank', 'atm'],
            'technology': ['wi-fi', 'cctv', 'intercom', 'video door phone'],
            'safety': ['fire safety', 'earthquake resistant', 'gated community']
        }

        found_amenities = {}
        for category, amenities in amenities_categories.items():
            found_amenities[category] = [amenity for amenity in amenities if amenity in page_text.lower()]

        return found_amenities

    def _extract_detailed_location_info(self, soup):
        """Extract detailed location information"""
        page_text = soup.get_text()

        return {
            'address': self._extract_full_address(page_text),
            'landmarks': self._extract_nearby_landmarks(page_text),
            'connectivity': self._extract_connectivity_info(page_text),
            'neighborhood': self._extract_neighborhood_info(page_text)
        }

    def _extract_detailed_price_info(self, soup):
        """Extract detailed price breakdown"""
        page_text = soup.get_text()

        return {
            'base_price': self._extract_base_price(page_text),
            'additional_charges': self._extract_additional_charges(page_text),
            'payment_plan': self._extract_payment_plan(page_text),
            'loan_details': self._extract_loan_details(page_text)
        }

    def _extract_detailed_builder_agent_info(self, soup):
        """Extract detailed builder/agent information"""
        page_text = soup.get_text()

        return {
            'builder_name': self._extract_builder_name(page_text),
            'builder_projects': self._extract_builder_projects(page_text),
            'agent_details': self._extract_agent_details(page_text),
            'contact_options': self._extract_contact_options(soup)
        }

    def _extract_legal_compliance_info(self, soup):
        """Extract legal and compliance information"""
        page_text = soup.get_text()

        return {
            'rera_details': self._extract_rera_details(page_text),
            'approvals': self._extract_approvals(page_text),
            'legal_documents': self._extract_legal_documents(page_text)
        }

    def _extract_comprehensive_media_info(self, soup):
        """Extract comprehensive media information"""
        return {
            'images': self._analyze_property_images(soup),
            'videos': self._analyze_property_videos(soup),
            'virtual_tour': self._analyze_virtual_tour(soup),
            'floor_plans': self._analyze_floor_plans(soup)
        }

    def _analyze_similar_properties_section(self, soup):
        """Analyze similar properties section"""
        similar_section = soup.find(string=re.compile(r'similar|related|recommended', re.IGNORECASE))
        if similar_section:
            return {
                'has_similar_section': True,
                'similar_count': len(soup.find_all('a', href=re.compile(r'spid-|npxid-')))
            }
        return {'has_similar_section': False}

    def _analyze_contact_mechanisms(self, soup):
        """Analyze all contact mechanisms"""
        return {
            'contact_buttons': len(soup.find_all(['button', 'a'], string=re.compile(r'contact|call', re.IGNORECASE))),
            'phone_reveal': bool(soup.find(string=re.compile(r'view number|show number', re.IGNORECASE))),
            'enquiry_forms': len(soup.find_all('form')),
            'chat_options': bool(soup.find(string=re.compile(r'chat|message', re.IGNORECASE)))
        }

    def _analyze_detailed_page_structure(self, soup):
        """Analyze detailed page structure"""
        return {
            'total_elements': len(soup.find_all()),
            'scripts': len(soup.find_all('script')),
            'external_scripts': len(soup.find_all('script', src=True)),
            'json_ld_scripts': len(soup.find_all('script', type='application/ld+json')),
            'forms': len(soup.find_all('form')),
            'iframes': len(soup.find_all('iframe'))
        }

    # Additional analysis methods
    def _analyze_pagination_deeply(self, soup):
        """Deep analysis of pagination"""
        pagination_elements = soup.find_all(['a', 'button'], string=re.compile(r'next|previous|page|\d+', re.IGNORECASE))
        return {
            'has_pagination': len(pagination_elements) > 0,
            'pagination_elements': len(pagination_elements),
            'max_pages': self._extract_max_pages(soup)
        }

    def _analyze_filters_comprehensively(self, soup):
        """Comprehensive filter analysis"""
        filter_elements = soup.find_all(['select', 'input', 'button'])
        return {
            'total_filters': len(filter_elements),
            'filter_types': self._categorize_filters(filter_elements),
            'has_advanced_filters': bool(soup.find(string=re.compile(r'advanced|more filters', re.IGNORECASE)))
        }

    def _detect_anti_scraping_measures(self, soup):
        """Detect anti-scraping measures"""
        page_text = soup.get_text().lower()

        return {
            'has_captcha': 'captcha' in page_text,
            'has_rate_limiting': any(term in page_text for term in ['rate limit', 'too many requests']),
            'has_bot_detection': any(term in page_text for term in ['bot', 'automated', 'suspicious']),
            'script_heavy': len(soup.find_all('script')) > 20,
            'dynamic_loading': bool(soup.find_all(attrs={'data-src': True}))
        }

    def _analyze_page_performance(self):
        """Analyze page performance metrics"""
        try:
            # Get performance timing
            performance = self.driver.execute_script("return window.performance.timing")
            load_time = performance['loadEventEnd'] - performance['navigationStart']
            return {
                'load_time_ms': load_time,
                'dom_ready_time': performance['domContentLoadedEventEnd'] - performance['navigationStart']
            }
        except:
            return {'load_time_ms': 'unknown', 'dom_ready_time': 'unknown'}

    def _analyze_structured_data_deeply(self, soup):
        """Deep analysis of structured data"""
        json_scripts = soup.find_all('script', type='application/ld+json')
        structured_data_analysis = {
            'script_count': len(json_scripts),
            'data_types': [],
            'field_coverage': {}
        }

        for script in json_scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    data_type = data.get('@type', 'Unknown')
                    structured_data_analysis['data_types'].append(data_type)

                    # Analyze field coverage
                    if 'property' in data_type.lower() or 'apartment' in data_type.lower():
                        structured_data_analysis['field_coverage'] = self._analyze_structured_fields(data)
            except:
                continue

        return structured_data_analysis

    def _analyze_field_variations(self, soup):
        """Analyze field variations across properties"""
        # This would analyze how fields vary across different properties
        # Implementation would depend on specific requirements
        return {
            'price_formats': self._find_price_format_variations(soup),
            'area_formats': self._find_area_format_variations(soup),
            'location_formats': self._find_location_format_variations(soup)
        }

    # Placeholder methods for detailed extraction (would be implemented based on actual page analysis)
    def _extract_property_title(self, soup): return soup.title.string if soup.title else ""
    def _extract_detailed_price_breakdown(self, text): return {}
    def _extract_detailed_area_breakdown(self, text): return {}
    def _extract_detailed_configuration(self, text): return {}
    def _extract_detailed_property_specs(self, text): return {}
    def _extract_detailed_location(self, text): return {}
    def _extract_detailed_builder_info(self, text): return {}
    def _extract_full_address(self, text): return ""
    def _extract_nearby_landmarks(self, text): return []
    def _extract_connectivity_info(self, text): return {}
    def _extract_neighborhood_info(self, text): return {}
    def _extract_base_price(self, text): return ""
    def _extract_additional_charges(self, text): return {}
    def _extract_payment_plan(self, text): return {}
    def _extract_loan_details(self, text): return {}
    def _extract_builder_name(self, text): return ""
    def _extract_builder_projects(self, text): return []
    def _extract_agent_details(self, text): return {}
    def _extract_contact_options(self, soup): return {}
    def _extract_rera_details(self, text): return {}
    def _extract_approvals(self, text): return []
    def _extract_legal_documents(self, text): return []
    def _analyze_property_images(self, soup): return {}
    def _analyze_property_videos(self, soup): return {}
    def _analyze_virtual_tour(self, soup): return {}
    def _analyze_floor_plans(self, soup): return {}
    def _extract_max_pages(self, soup): return 0
    def _categorize_filters(self, elements): return {}
    def _analyze_structured_fields(self, data): return {}
    def _find_price_format_variations(self, soup): return []
    def _find_area_format_variations(self, soup): return []
    def _find_location_format_variations(self, soup): return []


if __name__ == "__main__":
    analyzer = ComprehensiveDeepAnalyzer()
    analyzer.run_comprehensive_deep_analysis()
