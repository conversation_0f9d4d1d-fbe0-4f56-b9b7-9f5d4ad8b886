#!/usr/bin/env python3
"""
Debug the enhanced field extraction
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Anti-detection measures
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def debug_extraction():
    """Debug the field extraction"""
    driver = setup_driver()
    
    try:
        url = "https://www.99acres.com/property-for-sale-in-mumbai-ffid"
        print(f"🔍 Loading page: {url}")
        
        driver.get(url)
        time.sleep(5)
        
        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Find property links
        property_links = soup.find_all('a', href=True)
        property_cards = []
        
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                # Find the container
                current = link
                for _ in range(20):
                    if current is None:
                        break
                    
                    text = current.get_text().lower()
                    if ('₹' in text and 'sqft' in text and ('bhk' in text or 'bedroom' in text)):
                        if current not in property_cards:
                            property_cards.append(current)
                        break
                    current = current.parent
        
        print(f"✅ Found {len(property_cards)} property cards")
        
        # Debug first property card
        if property_cards:
            card = property_cards[0]
            card_text = card.get_text()
            
            print(f"\n🏠 DEBUGGING FIRST PROPERTY CARD")
            print(f"="*60)
            print(f"📝 Card text length: {len(card_text)} characters")
            print(f"📄 First 500 characters:")
            print(f"   {card_text[:500]}...")
            
            # Test price extraction
            print(f"\n💰 PRICE EXTRACTION TEST:")
            price_patterns = [
                (r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)', 'Cr'),
                (r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)', 'Lakh'),
            ]
            
            for pattern, unit in price_patterns:
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    print(f"   ✅ Found price: ₹{matches[0]} {unit}")
                    break
            else:
                print(f"   ❌ No price found")
            
            # Test area extraction
            print(f"\n📐 AREA EXTRACTION TEST:")
            area_match = re.search(r'(\d+(?:,\d+)*)\s*sqft', card_text, re.IGNORECASE)
            if area_match:
                print(f"   ✅ Found area: {area_match.group(1)} sqft")
            else:
                print(f"   ❌ No area found")
            
            # Test BHK extraction
            print(f"\n🏠 BHK EXTRACTION TEST:")
            bhk_match = re.search(r'(\d+)\s*BHK', card_text, re.IGNORECASE)
            if bhk_match:
                print(f"   ✅ Found BHK: {bhk_match.group(1)}")
            else:
                print(f"   ❌ No BHK found")
            
            # Test property type extraction
            print(f"\n🏢 PROPERTY TYPE TEST:")
            property_type_match = re.search(r'(Apartment|Villa|Independent House|Builder Floor|Studio|Penthouse|Plot)', card_text, re.IGNORECASE)
            if property_type_match:
                print(f"   ✅ Found type: {property_type_match.group(1)}")
            else:
                print(f"   ❌ No property type found")
            
            # Test amenities extraction
            print(f"\n🎯 AMENITIES TEST:")
            amenity_keywords = ['Swimming Pool', 'Gym', 'Club House', 'Garden', 'Security', 'Lift']
            found_amenities = []
            for amenity in amenity_keywords:
                if amenity.lower() in card_text.lower():
                    found_amenities.append(amenity)
            
            if found_amenities:
                print(f"   ✅ Found amenities: {', '.join(found_amenities)}")
            else:
                print(f"   ❌ No amenities found")
            
            # Test location extraction
            print(f"\n📍 LOCATION TEST:")
            # Get title first
            headings = card.find_all(['h1', 'h2', 'h3', 'h4'])
            title = ""
            for heading in headings:
                text = heading.get_text(strip=True)
                if text and ('bhk' in text.lower() or 'apartment' in text.lower() or 'flat' in text.lower()):
                    title = text
                    break
            
            if title:
                print(f"   Title: {title}")
                location_match = re.search(r'in\s+([^,]+)', title)
                if location_match:
                    print(f"   ✅ Found locality: {location_match.group(1).strip()}")
                else:
                    print(f"   ❌ No locality found in title")
            else:
                print(f"   ❌ No title found")
            
            # Test structured data
            print(f"\n📊 STRUCTURED DATA TEST:")
            json_scripts = soup.find_all('script', type='application/ld+json')
            print(f"   Found {len(json_scripts)} JSON-LD scripts")
            
            if json_scripts:
                try:
                    import json
                    for i, script in enumerate(json_scripts[:2]):  # Check first 2
                        try:
                            data = json.loads(script.string)
                            print(f"   Script {i+1}: {type(data)} with keys: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                        except:
                            print(f"   Script {i+1}: Invalid JSON")
                except ImportError:
                    print(f"   JSON module not available")
    
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_extraction()
