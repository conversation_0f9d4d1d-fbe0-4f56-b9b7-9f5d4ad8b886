#!/usr/bin/env python3
"""
Simple 99acres Field Analyzer
Direct approach to analyze multiple listing URLs and extract all fields
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
from collections import defaultdict
from datetime import datetime

class Simple99acresAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.all_fields = defaultdict(set)
        self.analyzed_urls = []
        
    def get_page_content(self, url):
        """Get page content with error handling"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"❌ Error fetching {url}: {str(e)}")
            return None
    
    def extract_all_fields(self, soup, url):
        """Extract all possible fields from a property page"""
        fields = {}
        
        # 1. Basic text extraction
        page_text = soup.get_text()
        
        # 2. Price patterns
        price_patterns = [
            r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr',
            r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh',
            r'₹\s*([\d,]+)\s*/sqft',
            r'EMI\s*₹\s*([\d,]+)'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[f'price_{pattern[:10]}'] = matches
        
        # 3. Property specifications
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'(\d+)\s*sq\.?\s*ft', 'area_sqft_alt'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West)\s*facing', 'facing'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing'),
            (r'(\d+)\s*years?\s*old', 'property_age')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 4. Meta tags
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            if meta.get('property'):
                fields[f"meta_{meta.get('property')}"] = meta.get('content', '')
            elif meta.get('name'):
                fields[f"meta_{meta.get('name')}"] = meta.get('content', '')
        
        # 5. JSON-LD structured data
        json_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_scripts:
            try:
                json_data = json.loads(script.string)
                fields['json_ld'] = json_data
            except:
                pass
        
        # 6. All headings
        for i in range(1, 7):
            headings = soup.find_all(f'h{i}')
            if headings:
                fields[f'h{i}_headings'] = [h.get_text(strip=True) for h in headings]
        
        # 7. All links
        links = soup.find_all('a', href=True)
        fields['all_links'] = [link.get('href') for link in links if link.get('href')]
        
        # 8. All images
        images = soup.find_all('img', src=True)
        fields['all_images'] = [img.get('src') for img in images if img.get('src')]
        
        # 9. Form inputs
        inputs = soup.find_all('input')
        if inputs:
            fields['form_inputs'] = [{'type': inp.get('type'), 'name': inp.get('name'), 'placeholder': inp.get('placeholder')} for inp in inputs]
        
        # 10. Data attributes
        elements_with_data = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        data_attrs = {}
        for elem in elements_with_data:
            for attr, value in elem.attrs.items():
                if attr.startswith('data-'):
                    data_attrs[attr] = value
        if data_attrs:
            fields['data_attributes'] = data_attrs
        
        # 11. Class names (for CSS selectors)
        all_classes = set()
        for elem in soup.find_all(class_=True):
            if isinstance(elem.get('class'), list):
                all_classes.update(elem.get('class'))
        fields['css_classes'] = list(all_classes)
        
        # 12. ID attributes
        all_ids = [elem.get('id') for elem in soup.find_all(id=True)]
        if all_ids:
            fields['element_ids'] = all_ids
        
        return fields
    
    def analyze_url(self, url):
        """Analyze a single URL"""
        print(f"🔍 Analyzing: {url}")
        
        content = self.get_page_content(url)
        if not content:
            return None
        
        soup = BeautifulSoup(content, 'html.parser')
        fields = self.extract_all_fields(soup, url)
        
        # Store in global collection
        for field_name, field_value in fields.items():
            self.all_fields[field_name].add(str(field_value)[:100])  # Limit length for storage
        
        self.analyzed_urls.append(url)
        print(f"✅ Extracted {len(fields)} field types")
        
        return fields
    
    def analyze_multiple_urls(self, urls):
        """Analyze multiple URLs"""
        print(f"🚀 Starting analysis of {len(urls)} URLs")
        print("="*60)
        
        results = {}
        
        for i, url in enumerate(urls, 1):
            print(f"\n📍 URL {i}/{len(urls)}")
            
            result = self.analyze_url(url)
            if result:
                results[url] = result
            
            # Respectful delay
            time.sleep(2)
        
        return results
    
    def generate_comprehensive_report(self, results):
        """Generate comprehensive field analysis report"""
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_urls_analyzed': len(self.analyzed_urls),
            'total_unique_fields': len(self.all_fields),
            'field_summary': {},
            'detailed_results': results
        }
        
        # Field frequency analysis
        for field_name, values in self.all_fields.items():
            report['field_summary'][field_name] = {
                'frequency': len(values),
                'sample_values': list(values)[:10]  # First 10 samples
            }
        
        return report

def main():
    """Main analysis function"""
    
    # Sample URLs to analyze (mix of different property types and cities)
    test_urls = [
        "https://www.99acres.com/property-for-sale-in-mumbai-ffid",
        "https://www.99acres.com/property-for-sale-in-delhi-ffid", 
        "https://www.99acres.com/property-for-sale-in-bangalore-ffid",
        "https://www.99acres.com/property-for-sale-in-pune-ffid",
        "https://www.99acres.com/property-for-rent-in-mumbai-ffid",
        "https://www.99acres.com/1-bhk-apartment-flat-for-sale-in-mumbai-ffid",
        "https://www.99acres.com/2-bhk-apartment-flat-for-sale-in-mumbai-ffid",
        "https://www.99acres.com/3-bhk-apartment-flat-for-sale-in-mumbai-ffid"
    ]
    
    analyzer = Simple99acresAnalyzer()
    
    # Analyze all URLs
    results = analyzer.analyze_multiple_urls(test_urls)
    
    # Generate comprehensive report
    report = analyzer.generate_comprehensive_report(results)
    
    # Save results
    with open('99acres_field_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print("\n" + "="*60)
    print("✅ ANALYSIS COMPLETE!")
    print("="*60)
    print(f"📊 URLs Analyzed: {report['total_urls_analyzed']}")
    print(f"🔍 Unique Fields Found: {report['total_unique_fields']}")
    print(f"📄 Report saved: 99acres_field_analysis_report.json")
    
    # Print top fields
    print(f"\n🏆 TOP 20 MOST COMMON FIELDS:")
    sorted_fields = sorted(report['field_summary'].items(), key=lambda x: x[1]['frequency'], reverse=True)
    for field_name, field_info in sorted_fields[:20]:
        print(f"   {field_name}: {field_info['frequency']} occurrences")

if __name__ == "__main__":
    main()
