#!/usr/bin/env python3
"""
Comprehensive Individual Listing Analyzer for 99acres
Deep analysis of individual property pages across multiple cities and property types
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
import re
from collections import defaultdict

class ComprehensiveListingAnalyzer:
    def __init__(self):
        self.driver = None
        self.analysis_results = []
        self.field_patterns = defaultdict(set)
        self.selector_analysis = defaultdict(list)
        self.documentation = []
        
    def setup_driver(self):
        """Setup Chrome WebDriver for analysis"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        print("✅ Browser initialized for comprehensive analysis")
    
    def create_diverse_sample(self):
        """Create a diverse sample of properties for deep analysis"""
        print("🎯 Creating Diverse Sample for Deep Analysis")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect('data/99acres_properties.db')
            cursor = conn.cursor()
            
            # Get total count
            cursor.execute("SELECT COUNT(*) FROM properties WHERE property_url IS NOT NULL")
            total_count = cursor.fetchone()[0]
            print(f"📊 Total properties in database: {total_count}")
            
            diverse_sample = []
            
            # 1. Sample by cities (5-10 properties per major city)
            cities = ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Hyderabad', 'Chennai', 'Kolkata']
            
            for city in cities:
                print(f"\n🏙️ Sampling from {city}:")
                
                # Sale properties
                cursor.execute("""
                    SELECT property_url, title, price, locality, bedrooms, city, property_type
                    FROM properties 
                    WHERE city LIKE ? AND property_type = 'Sale' AND property_url IS NOT NULL 
                    ORDER BY RANDOM() LIMIT 5
                """, (f'%{city}%',))
                sale_props = cursor.fetchall()
                
                # Rent properties  
                cursor.execute("""
                    SELECT property_url, title, price, locality, bedrooms, city, property_type
                    FROM properties 
                    WHERE city LIKE ? AND property_type = 'Rent' AND property_url IS NOT NULL 
                    ORDER BY RANDOM() LIMIT 5
                """, (f'%{city}%',))
                rent_props = cursor.fetchall()
                
                city_props = sale_props + rent_props
                diverse_sample.extend(city_props)
                
                print(f"   Sale: {len(sale_props)} properties")
                print(f"   Rent: {len(rent_props)} properties")
            
            # 2. Sample by bedroom configurations
            print(f"\n🛏️ Sampling by bedroom configurations:")
            for bhk in ['1', '2', '3', '4', '5']:
                cursor.execute("""
                    SELECT property_url, title, price, locality, bedrooms, city, property_type
                    FROM properties 
                    WHERE bedrooms = ? AND property_url IS NOT NULL 
                    ORDER BY RANDOM() LIMIT 3
                """, (bhk,))
                bhk_props = cursor.fetchall()
                diverse_sample.extend(bhk_props)
                print(f"   {bhk} BHK: {len(bhk_props)} properties")
            
            # 3. Remove duplicates and format
            seen_urls = set()
            final_sample = []
            
            for i, (url, title, price, locality, bedrooms, city, prop_type) in enumerate(diverse_sample):
                if url not in seen_urls:
                    seen_urls.add(url)
                    final_sample.append({
                        'index': len(final_sample) + 1,
                        'url': url,
                        'title': title,
                        'price': price,
                        'locality': locality,
                        'bedrooms': bedrooms,
                        'city': city,
                        'property_type': prop_type,
                        'category': f"{city}_{prop_type}_{bedrooms}BHK"
                    })
            
            # Save sample
            sample_data = {
                'creation_timestamp': datetime.now().isoformat(),
                'total_sample_size': len(final_sample),
                'sampling_criteria': {
                    'cities': cities,
                    'property_types': ['Sale', 'Rent'],
                    'bedroom_configs': ['1', '2', '3', '4', '5'],
                    'properties_per_city': '5-10',
                    'properties_per_bhk': '3'
                },
                'sample_properties': final_sample
            }
            
            with open('comprehensive_analysis_sample.json', 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Created diverse sample of {len(final_sample)} properties")
            print(f"💾 Saved to: comprehensive_analysis_sample.json")
            
            # Print sample summary
            print(f"\n📋 Sample Summary:")
            city_counts = defaultdict(int)
            type_counts = defaultdict(int)
            bhk_counts = defaultdict(int)
            
            for prop in final_sample:
                city_counts[prop['city']] += 1
                type_counts[prop['property_type']] += 1
                bhk_counts[prop['bedrooms']] += 1
            
            print(f"   Cities: {dict(city_counts)}")
            print(f"   Types: {dict(type_counts)}")
            print(f"   BHK: {dict(bhk_counts)}")
            
            conn.close()
            return final_sample
            
        except Exception as e:
            print(f"❌ Error creating sample: {str(e)}")
            return []
    
    def analyze_individual_property_page(self, property_info):
        """Deep analysis of an individual property page"""
        url = property_info['url']
        category = property_info['category']
        
        print(f"\n🔍 Analyzing: {property_info['title'][:50]}...")
        print(f"   Category: {category}")
        print(f"   URL: {url}")
        
        try:
            # Navigate to property page
            self.driver.get(url)
            time.sleep(5)  # Wait for page to load
            
            # Get page source
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Comprehensive analysis
            analysis = {
                'property_info': property_info,
                'page_analysis': {
                    'title': soup.find('title').get_text(strip=True) if soup.find('title') else '',
                    'url': url,
                    'timestamp': datetime.now().isoformat()
                },
                'extracted_data': {},
                'selector_analysis': {},
                'field_patterns': {},
                'page_structure': {}
            }
            
            # 1. Extract all possible data fields
            analysis['extracted_data'] = self.extract_comprehensive_data(soup, url)
            
            # 2. Analyze CSS selectors and page structure
            analysis['selector_analysis'] = self.analyze_selectors_and_structure(soup)
            
            # 3. Identify field patterns
            analysis['field_patterns'] = self.identify_field_patterns(soup)
            
            # 4. Document page structure
            analysis['page_structure'] = self.document_page_structure(soup)
            
            # Store analysis
            self.analysis_results.append(analysis)
            
            # Update global patterns
            for field_name, field_value in analysis['extracted_data'].items():
                self.field_patterns[field_name].add(str(field_value)[:100])
            
            for selector_type, selectors in analysis['selector_analysis'].items():
                self.selector_analysis[selector_type].extend(selectors)
            
            print(f"   ✅ Analysis complete: {len(analysis['extracted_data'])} fields extracted")
            
            # Add to documentation
            self.documentation.append({
                'property': property_info,
                'key_findings': {
                    'total_fields': len(analysis['extracted_data']),
                    'unique_selectors': len(analysis['selector_analysis']),
                    'page_complexity': len(analysis['page_structure'])
                }
            })
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing property: {str(e)}")
            return None
    
    def extract_comprehensive_data(self, soup, url):
        """Extract all possible data from property page"""
        data = {}
        page_text = soup.get_text()
        
        # Basic information
        data['url'] = url
        data['page_title'] = soup.find('title').get_text(strip=True) if soup.find('title') else ''
        
        # Price patterns (comprehensive)
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr(?:ore)?', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh?', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/\s*sqft', 'price_per_sqft'),
            (r'₹\s*([\d,]+)\s*/\s*month', 'rent_per_month'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges'),
            (r'Security\s*Deposit\s*₹\s*([\d,]+)', 'security_deposit'),
            (r'Total\s*Price\s*₹\s*([\d,]+)', 'total_price')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches
        
        # Property specifications (detailed)
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'(\d+)\s*sq\.?\s*ft', 'area_sqft_alt'),
            (r'Carpet\s*Area\s*(\d+)', 'carpet_area'),
            (r'Built-up\s*Area\s*(\d+)', 'builtup_area'),
            (r'Super\s*Area\s*(\d+)', 'super_area'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date'),
            (r'(Apartment|Villa|Plot|House|Office|Shop)', 'property_subtype')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                data[field_name] = matches
        
        # Continue with more patterns...
        return data
    
    def analyze_selectors_and_structure(self, soup):
        """Analyze CSS selectors and page structure"""
        selectors = {
            'price_selectors': [],
            'specification_selectors': [],
            'amenity_selectors': [],
            'contact_selectors': [],
            'image_selectors': [],
            'description_selectors': []
        }
        
        # Find elements with price-related content
        price_elements = soup.find_all(text=re.compile(r'₹|Price|EMI|Rent', re.IGNORECASE))
        for elem in price_elements[:5]:  # Limit to avoid too much data
            parent = elem.parent
            if parent and parent.get('class'):
                selectors['price_selectors'].append({
                    'tag': parent.name,
                    'classes': parent.get('class'),
                    'text_sample': str(elem)[:50]
                })
        
        # Find specification containers
        spec_keywords = ['BHK', 'sqft', 'Bedroom', 'Bathroom', 'Floor']
        for keyword in spec_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for elem in elements[:3]:
                parent = elem.parent
                if parent and parent.get('class'):
                    selectors['specification_selectors'].append({
                        'keyword': keyword,
                        'tag': parent.name,
                        'classes': parent.get('class'),
                        'text_sample': str(elem)[:50]
                    })
        
        return selectors
    
    def identify_field_patterns(self, soup):
        """Identify patterns in field extraction"""
        patterns = {}
        
        # Analyze structured data
        json_scripts = soup.find_all('script', type='application/ld+json')
        if json_scripts:
            patterns['structured_data_count'] = len(json_scripts)
            for i, script in enumerate(json_scripts):
                try:
                    json_data = json.loads(script.string)
                    patterns[f'structured_data_{i}_type'] = json_data.get('@type', 'Unknown')
                except:
                    pass
        
        # Analyze meta tags
        meta_tags = soup.find_all('meta')
        patterns['meta_tags_count'] = len(meta_tags)
        
        return patterns
    
    def document_page_structure(self, soup):
        """Document the overall page structure"""
        structure = {
            'total_elements': len(soup.find_all()),
            'div_count': len(soup.find_all('div')),
            'span_count': len(soup.find_all('span')),
            'img_count': len(soup.find_all('img')),
            'script_count': len(soup.find_all('script')),
            'unique_classes': len(set([cls for elem in soup.find_all(class_=True) for cls in elem.get('class', [])]))
        }
        
        return structure

def main():
    """Main analysis function"""
    print("🔍 99acres Comprehensive Individual Listing Analyzer")
    print("=" * 60)
    
    analyzer = ComprehensiveListingAnalyzer()
    
    try:
        # Step 1: Create diverse sample
        sample_properties = analyzer.create_diverse_sample()
        
        if not sample_properties:
            print("❌ Failed to create sample")
            return
        
        # Step 2: Setup browser
        analyzer.setup_driver()
        
        # Step 3: Analyze each property (limit to first 20 for initial analysis)
        print(f"\n🚀 Starting Deep Analysis of {min(20, len(sample_properties))} Properties")
        print("=" * 60)
        
        for i, prop in enumerate(sample_properties[:20], 1):
            print(f"\n📍 Property {i}/20")
            analysis = analyzer.analyze_individual_property_page(prop)
            
            if analysis:
                # Brief pause between properties
                time.sleep(3)
        
        # Step 4: Generate comprehensive report
        print(f"\n📊 Generating Comprehensive Analysis Report...")
        
        report = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_properties_analyzed': len(analyzer.analysis_results),
                'total_unique_fields': len(analyzer.field_patterns),
                'analyzer_version': 'Comprehensive 1.0'
            },
            'field_frequency_analysis': {},
            'selector_patterns': analyzer.selector_analysis,
            'documentation': analyzer.documentation,
            'detailed_analysis': analyzer.analysis_results
        }
        
        # Field frequency analysis
        for field_name, values in analyzer.field_patterns.items():
            report['field_frequency_analysis'][field_name] = {
                'frequency': len(values),
                'sample_values': list(values)[:5]
            }
        
        # Save comprehensive report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'comprehensive_listing_analysis_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🎉 COMPREHENSIVE ANALYSIS COMPLETE!")
        print("="*60)
        print(f"📊 Properties Analyzed: {report['analysis_metadata']['total_properties_analyzed']}")
        print(f"🔍 Unique Fields Found: {report['analysis_metadata']['total_unique_fields']}")
        print(f"📄 Report saved: {filename}")
        
        # Print top findings
        print(f"\n🏆 TOP 15 MOST COMMON FIELDS:")
        sorted_fields = sorted(report['field_frequency_analysis'].items(), 
                             key=lambda x: x[1]['frequency'], reverse=True)
        for i, (field_name, field_info) in enumerate(sorted_fields[:15], 1):
            print(f"   {i:2d}. {field_name}: {field_info['frequency']} occurrences")
        
        print(f"\n📋 Analysis complete! Ready to build comprehensive scraper.")
        
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
    finally:
        if analyzer.driver:
            analyzer.driver.quit()

if __name__ == "__main__":
    main()
