#!/usr/bin/env python3
"""
Individual Property Page Analyzer
Deep analysis of individual property detail pages to understand complete data structure
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import re
import random
import os


class IndividualPropertyAnalyzer:
    """Analyze individual property detail pages"""
    
    def __init__(self):
        self.driver = None
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'properties_analyzed': [],
            'complete_field_mapping': {},
            'data_completeness': {},
            'page_structures': {},
            'anti_scraping_analysis': {}
        }
        
        # Sample property URLs from our research
        self.sample_property_urls = [
            "https://www.99acres.com/4-bhk-bedroom-apartment-flat-for-sale-in-chembur-east-central-mumbai-2061-sq-ft-r1-spid-*********",
            "https://www.99acres.com/3-bhk-bedroom-apartment-flat-for-sale-in-pokharan-road-mumbai-thane-2200-sq-ft-r1-spid-*********",
            "https://www.99acres.com/2-bhk-bedroom-apartment-flat-for-sale-in-mangaon-raigad-3400-sq-ft-r1-spid-*********",
        ]
    
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser initialized for individual property analysis")
    
    def analyze_individual_property(self, url):
        """Comprehensive analysis of individual property page"""
        print(f"\n🏠 ANALYZING INDIVIDUAL PROPERTY")
        print(f"   URL: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(5)
            
            page_title = self.driver.title
            print(f"   📄 Page title: {page_title}")
            
            # Scroll to load all content
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            analysis = {
                'url': url,
                'page_title': page_title,
                'basic_details': self._extract_basic_details(soup),
                'price_details': self._extract_price_details(soup),
                'property_details': self._extract_property_details(soup),
                'location_details': self._extract_location_details(soup),
                'amenities': self._extract_amenities(soup),
                'builder_agent_info': self._extract_builder_agent_info(soup),
                'images_media': self._extract_images_media(soup),
                'rera_legal': self._extract_rera_legal_info(soup),
                'contact_info': self._extract_contact_info(soup),
                'structured_data': self._extract_structured_data(soup),
                'all_text_fields': self._extract_all_text_fields(soup),
                'page_structure': self._analyze_page_structure(soup)
            }
            
            print(f"   ✅ Extracted {len(analysis['basic_details'])} basic details")
            print(f"   💰 Price details: {len(analysis['price_details'])} fields")
            print(f"   🏠 Property details: {len(analysis['property_details'])} fields")
            print(f"   📍 Location details: {len(analysis['location_details'])} fields")
            print(f"   🎯 Amenities: {len(analysis['amenities'])} items")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {'error': str(e), 'url': url}
    
    def _extract_basic_details(self, soup):
        """Extract basic property details"""
        details = {}
        page_text = soup.get_text()
        
        # Property title
        title_selectors = ['h1', '.property-title', '[data-testid="property-title"]']
        for selector in title_selectors:
            elements = soup.select(selector)
            if elements:
                details['title'] = elements[0].get_text(strip=True)
                break
        
        # Property type
        type_patterns = [
            r'(Apartment|Villa|Independent House|Builder Floor|Studio|Penthouse|Plot)',
            r'(Flat|House|Bungalow|Row House|Duplex)'
        ]
        for pattern in type_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                details['property_type'] = matches[0]
                break
        
        # BHK configuration
        bhk_patterns = [
            r'(\d+)\s*BHK',
            r'(\d+)\s*Bedroom'
        ]
        for pattern in bhk_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                details['bhk'] = matches[0]
                break
        
        # Bathrooms
        bath_match = re.search(r'(\d+)\s*Bath', page_text, re.IGNORECASE)
        if bath_match:
            details['bathrooms'] = bath_match.group(1)
        
        # Balconies
        balcony_match = re.search(r'(\d+)\s*Balcon', page_text, re.IGNORECASE)
        if balcony_match:
            details['balconies'] = balcony_match.group(1)
        
        return details
    
    def _extract_price_details(self, soup):
        """Extract all price-related information"""
        details = {}
        page_text = soup.get_text()
        
        # Main price
        price_patterns = [
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr',
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh'
        ]
        for pattern in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                unit = 'Cr' if 'Cr' in pattern else 'Lakh'
                details['main_price'] = f"₹{matches[0]} {unit}"
                break
        
        # Price per sqft
        price_per_sqft = re.search(r'₹\s*([\d,]+)\s*/sqft', page_text, re.IGNORECASE)
        if price_per_sqft:
            details['price_per_sqft'] = f"₹{price_per_sqft.group(1)} /sqft"
        
        # EMI
        emi_match = re.search(r'EMI\s*₹\s*([\d,]+)', page_text, re.IGNORECASE)
        if emi_match:
            details['emi'] = f"₹{emi_match.group(1)}"
        
        # Booking amount
        booking_match = re.search(r'Booking\s*Amount\s*₹\s*([\d,]+)', page_text, re.IGNORECASE)
        if booking_match:
            details['booking_amount'] = f"₹{booking_match.group(1)}"
        
        return details
    
    def _extract_property_details(self, soup):
        """Extract detailed property specifications"""
        details = {}
        page_text = soup.get_text()
        
        # Area details
        area_patterns = {
            'carpet_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Carpet\s*Area',
            'buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Built-up\s*Area',
            'super_buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Super\s*Built-up\s*Area',
            'plot_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Plot\s*Area'
        }
        
        for area_type, pattern in area_patterns.items():
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                details[area_type] = f"{match.group(1)} sqft"
        
        # Floor details
        floor_match = re.search(r'(\d+)(?:st|nd|rd|th)\s*Floor', page_text, re.IGNORECASE)
        if floor_match:
            details['floor'] = floor_match.group(1)
        
        total_floors_match = re.search(r'of\s*(\d+)\s*Floors', page_text, re.IGNORECASE)
        if total_floors_match:
            details['total_floors'] = total_floors_match.group(1)
        
        # Facing direction
        facing_match = re.search(r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*Facing', page_text, re.IGNORECASE)
        if facing_match:
            details['facing'] = facing_match.group(1)
        
        # Age of property
        age_match = re.search(r'(\d+)\s*Years?\s*Old', page_text, re.IGNORECASE)
        if age_match:
            details['age'] = f"{age_match.group(1)} years"
        
        # Furnishing
        furnishing_match = re.search(r'(Furnished|Semi-Furnished|Unfurnished)', page_text, re.IGNORECASE)
        if furnishing_match:
            details['furnishing'] = furnishing_match.group(1)
        
        # Parking
        parking_match = re.search(r'(\d+)\s*Parking', page_text, re.IGNORECASE)
        if parking_match:
            details['parking'] = f"{parking_match.group(1)} parking"
        
        # Construction status
        status_match = re.search(r'(Ready To Move|Under Construction|New Launch|Partially Ready)', page_text, re.IGNORECASE)
        if status_match:
            details['construction_status'] = status_match.group(1)
        
        # Possession
        possession_match = re.search(r'Possession\s*in\s*([^,\n]+)', page_text, re.IGNORECASE)
        if possession_match:
            details['possession'] = possession_match.group(1).strip()
        
        return details
    
    def _extract_location_details(self, soup):
        """Extract location information"""
        details = {}
        page_text = soup.get_text()
        
        # Society/Project name
        society_patterns = [
            r'Project:\s*([^\n]+)',
            r'Society:\s*([^\n]+)',
            r'^([A-Z][A-Za-z\s&]+?)(?:\s+RESALE|\s+NEW|\s+₹)'
        ]
        
        for pattern in society_patterns:
            match = re.search(pattern, page_text, re.MULTILINE)
            if match:
                details['society'] = match.group(1).strip()
                break
        
        # Locality
        locality_match = re.search(r'in\s+([^,\n]+?)(?:,|\s+Mumbai|\s+Delhi|\s+Bangalore)', page_text, re.IGNORECASE)
        if locality_match:
            details['locality'] = locality_match.group(1).strip()
        
        # City and state
        city_patterns = [
            r'(Mumbai|Delhi|Bangalore|Pune|Hyderabad|Chennai|Kolkata|Ahmedabad)',
            r'in\s+[^,]+,\s*([^,\n]+)'
        ]
        
        for pattern in city_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                details['city'] = match.group(1).strip()
                break
        
        # Pincode
        pincode_match = re.search(r'(\d{6})', page_text)
        if pincode_match:
            details['pincode'] = pincode_match.group(1)
        
        return details
    
    def _extract_amenities(self, soup):
        """Extract amenities list"""
        amenities = []
        page_text = soup.get_text()
        
        # Common amenities patterns
        amenity_keywords = [
            'Swimming Pool', 'Gym', 'Club House', 'Garden', 'Security', 'Lift',
            'Power Backup', 'Car Parking', 'Playground', 'Jogging Track',
            'Community Hall', 'Library', 'Spa', 'Tennis Court', 'Basketball Court',
            'Badminton Court', 'Yoga Room', 'Party Hall', 'Business Center',
            'Wi-Fi', 'CCTV', 'Intercom', 'Fire Safety', 'Earthquake Resistant'
        ]
        
        for amenity in amenity_keywords:
            if amenity.lower() in page_text.lower():
                amenities.append(amenity)
        
        return amenities
    
    def _extract_builder_agent_info(self, soup):
        """Extract builder/agent information"""
        details = {}
        page_text = soup.get_text()
        
        # Builder name
        builder_patterns = [
            r'Builder:\s*([^\n]+)',
            r'by\s+([A-Z][A-Za-z\s&]+)',
            r'Developed\s+by\s+([^\n]+)'
        ]
        
        for pattern in builder_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                details['builder'] = match.group(1).strip()
                break
        
        # Agent type
        agent_type_match = re.search(r'(Owner|Dealer|Builder|Agent)', page_text, re.IGNORECASE)
        if agent_type_match:
            details['agent_type'] = agent_type_match.group(1)
        
        # Posted time
        posted_match = re.search(r'(\d+[dwmy])\s*ago', page_text, re.IGNORECASE)
        if posted_match:
            details['posted_time'] = posted_match.group(1) + ' ago'
        
        return details
    
    def _extract_images_media(self, soup):
        """Extract images and media information"""
        details = {}
        
        # Count images
        images = soup.find_all('img')
        property_images = [img for img in images if img.get('src') and 'property' in img.get('src', '').lower()]
        details['total_images'] = len(images)
        details['property_images'] = len(property_images)
        
        # Look for video
        videos = soup.find_all(['video', 'iframe'])
        details['has_video'] = len(videos) > 0
        
        # Sample image URLs
        sample_images = []
        for img in images[:5]:
            src = img.get('src') or img.get('data-src')
            if src:
                sample_images.append(src)
        details['sample_image_urls'] = sample_images
        
        return details
    
    def _extract_rera_legal_info(self, soup):
        """Extract RERA and legal information"""
        details = {}
        page_text = soup.get_text()
        
        # RERA ID
        rera_patterns = [
            r'RERA\s*ID[:\s]*([A-Z0-9]+)',
            r'RERA\s*No[:\s]*([A-Z0-9]+)',
            r'Registration\s*No[:\s]*([A-Z0-9]+)'
        ]
        
        for pattern in rera_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                details['rera_id'] = match.group(1)
                break
        
        # Legal verification
        if 'verified' in page_text.lower():
            details['verified'] = True
        
        if 'rera approved' in page_text.lower():
            details['rera_approved'] = True
        
        return details
    
    def _extract_contact_info(self, soup):
        """Extract contact information"""
        details = {}
        
        # Look for contact buttons
        contact_buttons = soup.find_all(['button', 'a'], text=re.compile(r'contact|call|phone', re.IGNORECASE))
        details['contact_buttons'] = len(contact_buttons)
        
        # Look for phone numbers (masked)
        phone_patterns = soup.find_all(text=re.compile(r'\d{2}XXXXXX\d{2}'))
        details['masked_phones'] = len(phone_patterns)
        
        return details
    
    def _extract_structured_data(self, soup):
        """Extract structured data (JSON-LD)"""
        structured_data = []
        
        json_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_scripts:
            try:
                data = json.loads(script.string)
                structured_data.append(data)
            except:
                pass
        
        return {
            'count': len(structured_data),
            'data': structured_data
        }
    
    def _extract_all_text_fields(self, soup):
        """Extract all possible text fields for analysis"""
        # Get all text content
        all_text = soup.get_text()
        
        # Split into lines and analyze
        lines = [line.strip() for line in all_text.split('\n') if line.strip()]
        
        # Categorize lines
        categorized = {
            'price_lines': [line for line in lines if '₹' in line],
            'area_lines': [line for line in lines if 'sqft' in line.lower()],
            'bhk_lines': [line for line in lines if 'bhk' in line.lower()],
            'location_lines': [line for line in lines if any(city in line.lower() for city in ['mumbai', 'delhi', 'bangalore'])],
            'total_lines': len(lines)
        }
        
        return categorized
    
    def _analyze_page_structure(self, soup):
        """Analyze overall page structure"""
        structure = {
            'total_elements': len(soup.find_all()),
            'scripts': len(soup.find_all('script')),
            'external_scripts': len(soup.find_all('script', src=True)),
            'forms': len(soup.find_all('form')),
            'inputs': len(soup.find_all('input')),
            'buttons': len(soup.find_all('button')),
            'images': len(soup.find_all('img')),
            'links': len(soup.find_all('a', href=True)),
            'divs': len(soup.find_all('div')),
            'spans': len(soup.find_all('span'))
        }
        
        return structure
    
    def run_individual_analysis(self):
        """Run analysis on individual property pages"""
        print("🚀 STARTING INDIVIDUAL PROPERTY ANALYSIS")
        print("="*80)
        
        try:
            self.setup_driver()
            
            # Load property URLs from our research data
            property_urls = self._load_property_urls_from_research()
            
            print(f"🎯 Analyzing {len(property_urls)} individual properties")
            
            for i, url in enumerate(property_urls[:5]):  # Analyze first 5
                print(f"\n📍 PROPERTY {i+1}/{min(5, len(property_urls))}")
                
                analysis = self.analyze_individual_property(url)
                if analysis and 'error' not in analysis:
                    self.analysis_results['properties_analyzed'].append(analysis)
                
                time.sleep(random.uniform(10, 15))  # Longer delay for individual pages
            
            # Save results
            self.save_analysis_results()
            
            print(f"\n✅ INDIVIDUAL PROPERTY ANALYSIS COMPLETED!")
            print(f"📊 Successfully analyzed: {len(self.analysis_results['properties_analyzed'])} properties")
            print(f"💾 Results saved to: data/individual_property_analysis.json")
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def _load_property_urls_from_research(self):
        """Load property URLs from our research data"""
        urls = []
        
        try:
            with open('data/deep_99acres_research.json', 'r', encoding='utf-8') as f:
                research_data = json.load(f)
            
            for finding in research_data.get('detailed_findings', []):
                for sample in finding.get('property_samples', []):
                    for link in sample.get('property_links', []):
                        if link not in urls:
                            urls.append(link)
        except:
            # Fallback to sample URLs
            urls = self.sample_property_urls
        
        return urls
    
    def save_analysis_results(self):
        """Save analysis results"""
        os.makedirs('data', exist_ok=True)
        
        with open('data/individual_property_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    analyzer = IndividualPropertyAnalyzer()
    analyzer.run_individual_analysis()
