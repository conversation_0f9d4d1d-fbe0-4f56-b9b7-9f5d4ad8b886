#!/usr/bin/env python3
"""
99acres Optimized Deep Analysis System
Systematic analysis of 100-200+ individual property pages across multiple cities and property types
"""

import asyncio
import aiohttp
import json
import csv
import time
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
from collections import defaultdict, Counter
import logging
from dataclasses import dataclass
from typing import List, Dict, Set
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PropertyAnalysisTarget:
    city: str
    property_type: str
    price_range: str
    target_count: int
    search_url: str

class OptimizedPropertyAnalyzer:
    def __init__(self):
        self.base_url = "https://www.99acres.com"
        self.session = None
        self.analyzed_properties = []
        self.data_fields_by_type = defaultdict(lambda: defaultdict(set))
        self.property_variations = defaultdict(set)
        self.analysis_targets = []
        
        # Optimized headers with rotation
        self.headers_pool = [
            {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            },
            {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            },
            {
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
        ]
        
        self.setup_analysis_targets()

    def setup_analysis_targets(self):
        """Define comprehensive analysis targets for systematic coverage"""
        
        # Mumbai targets
        mumbai_targets = [
            PropertyAnalysisTarget("mumbai", "apartment", "budget", 10, 
                                 f"{self.base_url}/search/property/buy/mumbai?budget=0-100&property_type=apartment"),
            PropertyAnalysisTarget("mumbai", "apartment", "mid-range", 10,
                                 f"{self.base_url}/search/property/buy/mumbai?budget=100-500&property_type=apartment"),
            PropertyAnalysisTarget("mumbai", "apartment", "premium", 10,
                                 f"{self.base_url}/search/property/buy/mumbai?budget=500-2000&property_type=apartment"),
            PropertyAnalysisTarget("mumbai", "villa", "luxury", 8,
                                 f"{self.base_url}/search/property/buy/mumbai?property_type=villa"),
            PropertyAnalysisTarget("mumbai", "plot", "residential", 8,
                                 f"{self.base_url}/search/property/buy/mumbai?property_type=plot"),
            PropertyAnalysisTarget("mumbai", "commercial", "office", 6,
                                 f"{self.base_url}/search/property/buy/mumbai?property_type=office")
        ]
        
        # Delhi targets
        delhi_targets = [
            PropertyAnalysisTarget("delhi", "apartment", "budget", 10,
                                 f"{self.base_url}/search/property/buy/delhi?budget=0-100&property_type=apartment"),
            PropertyAnalysisTarget("delhi", "apartment", "mid-range", 10,
                                 f"{self.base_url}/search/property/buy/delhi?budget=100-500&property_type=apartment"),
            PropertyAnalysisTarget("delhi", "apartment", "premium", 10,
                                 f"{self.base_url}/search/property/buy/delhi?budget=500-2000&property_type=apartment"),
            PropertyAnalysisTarget("delhi", "villa", "luxury", 8,
                                 f"{self.base_url}/search/property/buy/delhi?property_type=villa"),
            PropertyAnalysisTarget("delhi", "plot", "residential", 8,
                                 f"{self.base_url}/search/property/buy/delhi?property_type=plot")
        ]
        
        # Bangalore targets
        bangalore_targets = [
            PropertyAnalysisTarget("bangalore", "apartment", "budget", 10,
                                 f"{self.base_url}/search/property/buy/bangalore?budget=0-100&property_type=apartment"),
            PropertyAnalysisTarget("bangalore", "apartment", "mid-range", 10,
                                 f"{self.base_url}/search/property/buy/bangalore?budget=100-500&property_type=apartment"),
            PropertyAnalysisTarget("bangalore", "apartment", "premium", 10,
                                 f"{self.base_url}/search/property/buy/bangalore?budget=500-2000&property_type=apartment"),
            PropertyAnalysisTarget("bangalore", "villa", "luxury", 8,
                                 f"{self.base_url}/search/property/buy/bangalore?property_type=villa")
        ]
        
        # Pune targets
        pune_targets = [
            PropertyAnalysisTarget("pune", "apartment", "budget", 8,
                                 f"{self.base_url}/search/property/buy/pune?budget=0-100&property_type=apartment"),
            PropertyAnalysisTarget("pune", "apartment", "mid-range", 8,
                                 f"{self.base_url}/search/property/buy/pune?budget=100-500&property_type=apartment"),
            PropertyAnalysisTarget("pune", "villa", "luxury", 6,
                                 f"{self.base_url}/search/property/buy/pune?property_type=villa")
        ]
        
        # Chennai targets
        chennai_targets = [
            PropertyAnalysisTarget("chennai", "apartment", "budget", 8,
                                 f"{self.base_url}/search/property/buy/chennai?budget=0-100&property_type=apartment"),
            PropertyAnalysisTarget("chennai", "apartment", "mid-range", 8,
                                 f"{self.base_url}/search/property/buy/chennai?budget=100-500&property_type=apartment"),
            PropertyAnalysisTarget("chennai", "villa", "luxury", 6,
                                 f"{self.base_url}/search/property/buy/chennai?property_type=villa")
        ]
        
        self.analysis_targets = mumbai_targets + delhi_targets + bangalore_targets + pune_targets + chennai_targets
        
        total_properties = sum(target.target_count for target in self.analysis_targets)
        logger.info(f"Analysis targets set up: {len(self.analysis_targets)} categories, {total_properties} total properties")

    async def create_session(self):
        """Create optimized aiohttp session"""
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=8,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=random.choice(self.headers_pool)
        )

    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()

    async def get_page_with_retry(self, url, retries=3):
        """Get page content with exponential backoff retry"""
        for attempt in range(retries):
            try:
                # Rotate headers for each request
                headers = random.choice(self.headers_pool)
                
                async with self.session.get(url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        return content
                    elif response.status == 429:  # Rate limited
                        wait_time = (2 ** attempt) * random.uniform(1, 3)
                        logger.warning(f"Rate limited, waiting {wait_time:.1f}s")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt < retries - 1:
                    wait_time = (2 ** attempt) * random.uniform(0.5, 1.5)
                    await asyncio.sleep(wait_time)
                    
        return None

    async def extract_property_urls_from_target(self, target: PropertyAnalysisTarget):
        """Extract property URLs for a specific analysis target"""
        property_urls = []
        pages_to_check = min(5, (target.target_count // 10) + 1)  # Adaptive page count
        
        for page in range(1, pages_to_check + 1):
            search_url = f"{target.search_url}&page={page}"
            logger.info(f"Extracting URLs from {target.city} {target.property_type} {target.price_range}, page {page}")
            
            content = await self.get_page_with_retry(search_url)
            if not content:
                continue
                
            soup = BeautifulSoup(content, 'html.parser')
            
            # Multiple selectors for property links
            property_selectors = [
                'a[href*="/bhk-"]',
                'a[href*="/bedroom-"]',
                'a[href*="spid-"]',
                'a[href*="npspid-"]',
                'a[href*="npxid-"]',
                'a[href*="/plot-"]',
                'a[href*="/office-"]',
                'a[href*="/shop-"]'
            ]
            
            for selector in property_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href and href.startswith('/'):
                        full_url = urljoin(self.base_url, href)
                        property_urls.append({
                            'url': full_url,
                            'city': target.city,
                            'property_type': target.property_type,
                            'price_range': target.price_range
                        })
            
            # Rate limiting between pages
            await asyncio.sleep(random.uniform(1, 2))
            
            if len(property_urls) >= target.target_count:
                break
        
        # Remove duplicates and limit to target count
        unique_urls = []
        seen_urls = set()
        for prop in property_urls:
            if prop['url'] not in seen_urls:
                unique_urls.append(prop)
                seen_urls.add(prop['url'])
                if len(unique_urls) >= target.target_count:
                    break
        
        logger.info(f"Extracted {len(unique_urls)} unique URLs for {target.city} {target.property_type} {target.price_range}")
        return unique_urls

    async def analyze_property_comprehensive(self, property_info):
        """Comprehensive analysis of individual property page"""
        url = property_info['url']
        logger.info(f"Analyzing: {url}")
        
        content = await self.get_page_with_retry(url)
        if not content:
            return None
            
        soup = BeautifulSoup(content, 'html.parser')
        
        property_data = {
            'url': url,
            'city': property_info['city'],
            'property_type': property_info['property_type'],
            'price_range': property_info['price_range'],
            'extracted_fields': {},
            'field_variations': {},
            'page_structure': {}
        }
        
        # Comprehensive field extraction
        self.extract_all_property_fields(soup, property_data)
        
        # Track field variations by property type
        prop_type_key = f"{property_info['city']}_{property_info['property_type']}_{property_info['price_range']}"
        for field_name, field_value in property_data['extracted_fields'].items():
            self.data_fields_by_type[prop_type_key][field_name].add(str(type(field_value).__name__))
            self.property_variations[field_name].add(str(field_value)[:100])  # Sample variations
        
        return property_data

    def extract_all_property_fields(self, soup, property_data):
        """Extract all possible fields from property page"""
        fields = property_data['extracted_fields']
        
        # Core property information
        self.extract_core_fields(soup, fields)
        
        # Specifications and features
        self.extract_specifications(soup, fields)
        
        # Amenities and facilities
        self.extract_amenities(soup, fields)
        
        # Location and nearby places
        self.extract_location_data(soup, fields)
        
        # Financial and market data
        self.extract_financial_data(soup, fields)
        
        # Legal and compliance
        self.extract_legal_data(soup, fields)
        
        # Agent and listing information
        self.extract_agent_data(soup, fields)
        
        # Advanced features
        self.extract_advanced_features(soup, fields)

    def extract_core_fields(self, soup, fields):
        """Extract core property information"""
        # Property title
        title_selectors = ['h1', 'title', '[data-label="title"]', '.property-title']
        for selector in title_selectors:
            elem = soup.select_one(selector)
            if elem and 'title' not in fields:
                fields['title'] = elem.get_text(strip=True)
                break
        
        # Price information with multiple patterns
        price_patterns = [
            r'₹\s*[\d,]+\.?\d*\s*(Cr|Crore|L|Lac|Lakh)',
            r'Price[:\s]*₹[\d,]+',
            r'₹[\d,]+\s*per\s*sq\.?ft',
            r'Total\s*Price[:\s]*₹[\d,]+'
        ]
        
        for pattern in price_patterns:
            price_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if price_match and 'price' not in fields:
                fields['price'] = price_match.strip()
                break
        
        # Area information with variations
        area_patterns = [
            r'\d+\.?\d*\s*sq\.?ft',
            r'\d+\.?\d*\s*sqft',
            r'Carpet\s*Area[:\s]*\d+',
            r'Built[- ]up\s*Area[:\s]*\d+',
            r'Super\s*Area[:\s]*\d+'
        ]
        
        for pattern in area_patterns:
            area_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if area_match and 'area' not in fields:
                fields['area'] = area_match.strip()
                break
        
        # Configuration
        config_patterns = [
            r'\d+\s*BHK',
            r'\d+\s*Bedroom',
            r'\d+\s*BR',
            r'\d+\s*Bed'
        ]
        
        for pattern in config_patterns:
            config_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if config_match and 'configuration' not in fields:
                fields['configuration'] = config_match.strip()
                break

    def extract_specifications(self, soup, fields):
        """Extract detailed specifications"""
        # Floor details
        floor_patterns = [
            r'\d+\w*\s*floor',
            r'\d+\w*\s*of\s*\d+',
            r'Floor[:\s]*\d+',
            r'Ground\s*Floor',
            r'Basement'
        ]
        
        for pattern in floor_patterns:
            floor_match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if floor_match and 'floor_details' not in fields:
                fields['floor_details'] = floor_match.strip()
                break
        
        # Facing direction
        facing_keywords = ['North', 'South', 'East', 'West', 'North-East', 'North-West', 'South-East', 'South-West']
        for keyword in facing_keywords:
            facing_match = soup.find(text=re.compile(f'{keyword}.*Facing|Facing.*{keyword}', re.IGNORECASE))
            if facing_match and 'facing' not in fields:
                fields['facing'] = facing_match.strip()
                break
        
        # Additional specifications
        spec_keywords = {
            'furnishing': ['Furnished', 'Unfurnished', 'Semi-Furnished'],
            'parking': ['Parking', 'Car Park', 'Vehicle'],
            'property_age': ['Year Old', 'New Construction', 'Age'],
            'overlooking': ['Overlooking', 'View', 'Facing'],
            'flooring': ['Marble', 'Vitrified', 'Wooden', 'Ceramic'],
            'water_source': ['Municipal', 'Borewell', 'Tanker'],
            'power_backup': ['Power Backup', 'Generator', 'UPS']
        }
        
        for field_name, keywords in spec_keywords.items():
            if field_name not in fields:
                for keyword in keywords:
                    match = soup.find(text=re.compile(keyword, re.IGNORECASE))
                    if match:
                        fields[field_name] = match.strip()
                        break

    def extract_amenities(self, soup, fields):
        """Extract amenities and features"""
        amenity_categories = {
            'basic_amenities': ['Lift', 'Elevator', 'Security', 'CCTV', 'Intercom'],
            'recreational': ['Swimming Pool', 'Gym', 'Gymnasium', 'Club House', 'Sports'],
            'convenience': ['Shopping', 'ATM', 'Medical', 'Pharmacy'],
            'safety': ['Fire Safety', 'Security Guard', 'CCTV Camera'],
            'green_features': ['Garden', 'Park', 'Landscaping', 'Green Area'],
            'utilities': ['Water Supply', 'Sewage', 'Waste Management']
        }
        
        for category, keywords in amenity_categories.items():
            found_amenities = []
            for keyword in keywords:
                if soup.find(text=re.compile(keyword, re.IGNORECASE)):
                    found_amenities.append(keyword)
            
            if found_amenities:
                fields[category] = found_amenities

    def extract_location_data(self, soup, fields):
        """Extract location and nearby places"""
        nearby_categories = {
            'schools': ['School', 'College', 'University', 'Education'],
            'hospitals': ['Hospital', 'Clinic', 'Medical', 'Health'],
            'transport': ['Metro', 'Railway', 'Bus Stop', 'Airport'],
            'shopping': ['Mall', 'Market', 'Shopping Center'],
            'banks': ['Bank', 'ATM', 'Financial'],
            'restaurants': ['Restaurant', 'Cafe', 'Food']
        }
        
        for category, keywords in nearby_categories.items():
            found_places = []
            for keyword in keywords:
                matches = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
                for match in matches[:3]:  # Limit to first 3 matches
                    found_places.append(match.strip())
            
            if found_places:
                fields[f'nearby_{category}'] = found_places

    def extract_financial_data(self, soup, fields):
        """Extract financial and market data"""
        financial_patterns = {
            'emi': r'EMI[:\s]*₹[\d,]+',
            'price_per_sqft': r'₹[\d,]+\s*per\s*sq\.?ft',
            'maintenance': r'Maintenance[:\s]*₹[\d,]+',
            'booking_amount': r'Booking[:\s]*₹[\d,]+',
            'registration': r'Registration[:\s]*₹[\d,]+'
        }
        
        for field_name, pattern in financial_patterns.items():
            match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if match:
                fields[field_name] = match.strip()

    def extract_legal_data(self, soup, fields):
        """Extract legal and compliance data"""
        legal_patterns = {
            'rera_registration': r'RERA[:\s]*[A-Z0-9]+',
            'approvals': r'Approval|Clearance|NOC',
            'ownership': r'Freehold|Leasehold|Co-operative'
        }
        
        for field_name, pattern in legal_patterns.items():
            match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if match:
                fields[field_name] = match.strip()

    def extract_agent_data(self, soup, fields):
        """Extract agent and listing information"""
        agent_patterns = {
            'posted_by': r'(Owner|Dealer|Builder|Agent)',
            'agent_name': r'Agent[:\s]*([A-Za-z\s]+)',
            'agency_name': r'Agency[:\s]*([A-Za-z\s]+)'
        }
        
        for field_name, pattern in agent_patterns.items():
            match = soup.find(text=re.compile(pattern, re.IGNORECASE))
            if match:
                fields[field_name] = match.strip()

    def extract_advanced_features(self, soup, fields):
        """Extract advanced features and media"""
        # Check for advanced features
        advanced_features = {
            'virtual_tour': ['3D Tour', 'Virtual Tour', 'VR'],
            'video': ['Video', 'Walkthrough'],
            'floor_plan': ['Floor Plan', 'Layout'],
            'brochure': ['Brochure', 'Download']
        }
        
        for feature, keywords in advanced_features.items():
            for keyword in keywords:
                if soup.find(text=re.compile(keyword, re.IGNORECASE)):
                    fields[feature] = True
                    break

    async def run_comprehensive_analysis(self):
        """Run comprehensive analysis across all targets"""
        await self.create_session()
        
        try:
            all_property_urls = []
            
            # Collect URLs for all targets
            for target in self.analysis_targets:
                logger.info(f"Processing target: {target.city} {target.property_type} {target.price_range}")
                target_urls = await self.extract_property_urls_from_target(target)
                all_property_urls.extend(target_urls)
                
                # Rate limiting between targets
                await asyncio.sleep(random.uniform(2, 4))
            
            logger.info(f"Total property URLs collected: {len(all_property_urls)}")
            
            # Analyze properties in optimized batches
            batch_size = 15  # Optimized batch size
            for i in range(0, len(all_property_urls), batch_size):
                batch = all_property_urls[i:i + batch_size]
                
                logger.info(f"Processing batch {i//batch_size + 1}/{(len(all_property_urls)-1)//batch_size + 1}")
                
                # Process batch with controlled concurrency
                semaphore = asyncio.Semaphore(8)  # Limit concurrent requests
                
                async def analyze_with_semaphore(prop_info):
                    async with semaphore:
                        return await self.analyze_property_comprehensive(prop_info)
                
                tasks = [analyze_with_semaphore(prop_info) for prop_info in batch]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, dict):
                        self.analyzed_properties.append(result)
                
                logger.info(f"Analyzed {len(self.analyzed_properties)} properties so far")
                
                # Adaptive rate limiting
                await asyncio.sleep(random.uniform(3, 6))
                
        finally:
            await self.close_session()

    def generate_comprehensive_report(self):
        """Generate detailed analysis report"""
        # Analyze field coverage by property type
        field_coverage = {}
        for prop_type, fields in self.data_fields_by_type.items():
            field_coverage[prop_type] = {
                'total_fields': len(fields),
                'fields': dict(fields)
            }
        
        report = {
            'analysis_summary': {
                'total_properties_analyzed': len(self.analyzed_properties),
                'total_targets': len(self.analysis_targets),
                'unique_data_fields': len(self.property_variations),
                'field_coverage_by_type': field_coverage
            },
            'property_type_breakdown': self.get_property_type_breakdown(),
            'field_variations': dict(self.property_variations),
            'sample_properties': self.analyzed_properties[:10],
            'recommendations': self.generate_recommendations()
        }
        
        # Save comprehensive report
        with open('99acres_comprehensive_deep_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str, ensure_ascii=False)
        
        # Save detailed CSV
        self.save_detailed_csv()
        
        logger.info(f"Comprehensive analysis complete!")
        logger.info(f"Properties analyzed: {len(self.analyzed_properties)}")
        logger.info(f"Unique data fields found: {len(self.property_variations)}")
        
        return report

    def get_property_type_breakdown(self):
        """Get breakdown by property type"""
        breakdown = defaultdict(lambda: defaultdict(int))
        
        for prop in self.analyzed_properties:
            city = prop['city']
            prop_type = prop['property_type']
            price_range = prop['price_range']
            
            breakdown[city][f"{prop_type}_{price_range}"] += 1
        
        return dict(breakdown)

    def save_detailed_csv(self):
        """Save detailed CSV with all extracted data"""
        if not self.analyzed_properties:
            return
        
        # Get all unique field names
        all_fields = set()
        for prop in self.analyzed_properties:
            all_fields.update(prop['extracted_fields'].keys())
        
        fieldnames = ['url', 'city', 'property_type', 'price_range'] + sorted(all_fields)
        
        with open('99acres_comprehensive_properties_data.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for prop in self.analyzed_properties:
                row = {
                    'url': prop['url'],
                    'city': prop['city'],
                    'property_type': prop['property_type'],
                    'price_range': prop['price_range']
                }
                
                # Add extracted fields
                for field in all_fields:
                    value = prop['extracted_fields'].get(field, '')
                    if isinstance(value, list):
                        row[field] = '; '.join(str(v) for v in value)
                    else:
                        row[field] = str(value) if value else ''
                
                writer.writerow(row)

    def generate_recommendations(self):
        """Generate implementation recommendations"""
        return {
            'data_extraction': {
                'priority_fields': list(self.property_variations.keys())[:20],
                'field_variations_to_handle': len(self.property_variations),
                'recommended_parsing_strategies': [
                    'Multi-pattern regex matching',
                    'Fallback selector chains',
                    'Context-aware field extraction',
                    'Property-type specific parsers'
                ]
            },
            'scalability': {
                'concurrent_requests': 8,
                'batch_size': 15,
                'rate_limiting': '3-6 seconds between batches',
                'retry_strategy': 'Exponential backoff with jitter'
            },
            'quality_assurance': {
                'field_coverage_target': '90%+',
                'data_validation_rules': 'Property-type specific',
                'duplicate_detection': 'URL and content-based',
                'quality_scoring': 'Completeness and accuracy metrics'
            }
        }

async def main():
    """Main function to run comprehensive analysis"""
    analyzer = OptimizedPropertyAnalyzer()
    
    logger.info("Starting comprehensive 99acres deep analysis...")
    logger.info(f"Target: {sum(t.target_count for t in analyzer.analysis_targets)} properties across {len(analyzer.analysis_targets)} categories")
    
    # Run the analysis
    await analyzer.run_comprehensive_analysis()
    
    # Generate comprehensive report
    report = analyzer.generate_comprehensive_report()
    
    print("\n" + "="*60)
    print("COMPREHENSIVE DEEP ANALYSIS COMPLETE")
    print("="*60)
    print(f"Total Properties Analyzed: {report['analysis_summary']['total_properties_analyzed']}")
    print(f"Unique Data Fields Found: {report['analysis_summary']['unique_data_fields']}")
    print(f"Analysis Targets Covered: {report['analysis_summary']['total_targets']}")
    
    print(f"\nProperty Type Breakdown:")
    for city, types in report['property_type_breakdown'].items():
        print(f"  {city.title()}:")
        for prop_type, count in types.items():
            print(f"    {prop_type}: {count} properties")
    
    print(f"\nFiles Generated:")
    print(f"  - 99acres_comprehensive_deep_analysis_report.json")
    print(f"  - 99acres_comprehensive_properties_data.csv")

if __name__ == "__main__":
    asyncio.run(main())
