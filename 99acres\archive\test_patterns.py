#!/usr/bin/env python3
"""
Test extraction patterns
"""

import re

def test_price_patterns():
    """Test price extraction patterns"""
    test_text = "satya Deep 3RESALE1 BHK Flat in New Panvel, Navi Mumbai₹34.25 Lac₹5,197 /sqft659 sqft"
    
    print(f"Test text: {test_text}")
    print(f"\nTesting price patterns:")
    
    price_patterns = [
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)', 'Cr'),
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)', 'Lakh'),
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lac(?!\s*-)', 'Lakh'),
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*crore(?!\s*-)', 'Cr'),
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*lakh(?!\s*-)', 'Lakh'),
        (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*lac(?!\s*-)', 'Lakh'),
    ]
    
    for i, (pattern, unit) in enumerate(price_patterns):
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"   Pattern {i+1} ({unit}): {matches}")
        if matches:
            print(f"   ✅ Found: ₹{matches[0]} {unit}")
            break
    else:
        print(f"   ❌ No matches found")
    
    # Test simpler pattern
    print(f"\nTesting simpler patterns:")
    simple_patterns = [
        r'₹(\d+(?:\.\d+)?)\s*Lac',
        r'₹(\d+(?:\.\d+)?)\s*Lakh',
        r'₹([\d.]+)\s*Lac',
    ]
    
    for i, pattern in enumerate(simple_patterns):
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"   Simple {i+1}: {matches}")

if __name__ == "__main__":
    test_price_patterns()
