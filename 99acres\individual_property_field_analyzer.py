#!/usr/bin/env python3
"""
Individual Property Field Analyzer for 99acres
Simple, focused approach to analyze individual property pages and extract all available fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integrated_99acres_scraper import Integrated99acresScraper
from bs4 import BeautifulSoup
import json
import time
import re
from collections import defaultdict
from datetime import datetime

class IndividualPropertyFieldAnalyzer:
    def __init__(self):
        self.scraper = Integrated99acresScraper(headless=False)
        self.all_fields = defaultdict(set)
        self.analyzed_properties = []
        
    def extract_all_fields_from_property(self, soup, url):
        """Extract all possible fields from an individual property page"""
        fields = {}
        page_text = soup.get_text()
        
        # 1. Basic Property Information
        fields['url'] = url
        fields['page_title'] = soup.find('title').get_text(strip=True) if soup.find('title') else ''
        
        # 2. Price Information Patterns
        price_patterns = [
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Cr', 'price_crores'),
            (r'₹\s*([\d,]+(?:\.\d+)?)\s*Lakh', 'price_lakhs'),
            (r'₹\s*([\d,]+)\s*/sqft', 'price_per_sqft'),
            (r'EMI\s*₹\s*([\d,]+)', 'emi_amount'),
            (r'Booking\s*Amount\s*₹\s*([\d,]+)', 'booking_amount'),
            (r'Maintenance\s*₹\s*([\d,]+)', 'maintenance_charges'),
            (r'Total\s*Price\s*₹\s*([\d,]+)', 'total_price')
        ]
        
        for pattern, field_name in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 3. Property Specifications
        spec_patterns = [
            (r'(\d+)\s*BHK', 'bhk_config'),
            (r'(\d+)\s*Bedroom', 'bedrooms'),
            (r'(\d+)\s*Bathroom', 'bathrooms'),
            (r'(\d+)\s*Balcon', 'balconies'),
            (r'(\d+)\s*sqft', 'area_sqft'),
            (r'(\d+)\s*sq\.?\s*ft', 'area_sqft_alt'),
            (r'Floor\s*(\d+)', 'floor_number'),
            (r'(\d+)\s*floors?', 'total_floors'),
            (r'(North|South|East|West)\s*facing', 'facing_direction'),
            (r'(Furnished|Semi-furnished|Unfurnished)', 'furnishing_status'),
            (r'(\d+)\s*years?\s*old', 'property_age'),
            (r'(Ready to move|Under construction|New launch)', 'construction_status'),
            (r'Possession\s*:\s*([^,\n]+)', 'possession_date'),
            (r'Carpet\s*Area\s*(\d+)', 'carpet_area'),
            (r'Built-up\s*Area\s*(\d+)', 'builtup_area'),
            (r'Super\s*Area\s*(\d+)', 'super_area')
        ]
        
        for pattern, field_name in spec_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 4. Location Information
        location_patterns = [
            (r'Located in\s*([^,\n]+)', 'location_area'),
            (r'Near\s*([^,\n]+)', 'nearby_landmarks'),
            (r'(\d{6})', 'pincode'),
            (r'(Mumbai|Delhi|Bangalore|Pune|Chennai|Hyderabad|Kolkata|Ahmedabad)', 'city'),
            (r'Metro\s*Station\s*([^,\n]+)', 'metro_station'),
            (r'Railway\s*Station\s*([^,\n]+)', 'railway_station'),
            (r'Airport\s*([^,\n]+)', 'airport_distance')
        ]
        
        for pattern, field_name in location_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 5. Builder/Developer Information
        builder_patterns = [
            (r'By\s*([A-Z][^,\n]+(?:Group|Developers?|Builders?|Construction|Realty|Properties))', 'builder_name'),
            (r'Developer\s*:\s*([^,\n]+)', 'developer_name'),
            (r'RERA\s*(?:ID|Number)\s*:\s*([A-Z0-9]+)', 'rera_number'),
            (r'Builder\s*([^,\n]+)', 'builder_info')
        ]
        
        for pattern, field_name in builder_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                fields[field_name] = matches
        
        # 6. Amenities Detection
        amenities_keywords = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
            'Club House', 'Lift', 'Power Backup', 'Water Supply', 'Intercom',
            'Fire Safety', 'CCTV', 'Jogging Track', 'Tennis Court', 'Basketball',
            'Badminton', 'Spa', 'Sauna', 'Library', 'Business Center', 'Wi-Fi',
            'Children Play Area', 'Senior Citizen Sit Out', 'Multipurpose Hall',
            'Amphitheatre', 'Yoga Deck', 'Meditation Area', 'Cafeteria'
        ]
        
        found_amenities = []
        for amenity in amenities_keywords:
            if amenity.lower() in page_text.lower():
                found_amenities.append(amenity)
        
        if found_amenities:
            fields['amenities'] = found_amenities
        
        # 7. Contact Information
        contact_patterns = [
            (r'(\+91[\s-]?\d{10})', 'phone_numbers'),
            (r'(\d{10})', 'mobile_numbers'),
            (r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 'email_addresses')
        ]
        
        for pattern, field_name in contact_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                fields[field_name] = list(set(matches))  # Remove duplicates
        
        # 8. Meta Information
        meta_description = soup.find('meta', attrs={'name': 'description'})
        if meta_description:
            fields['meta_description'] = meta_description.get('content', '')
        
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            fields['meta_keywords'] = meta_keywords.get('content', '')
        
        # 9. JSON-LD Structured Data
        json_scripts = soup.find_all('script', type='application/ld+json')
        for i, script in enumerate(json_scripts):
            try:
                json_data = json.loads(script.string)
                fields[f'structured_data_{i}'] = json_data
            except:
                pass
        
        # 10. Images Count
        images = soup.find_all('img', src=True)
        property_images = [img.get('src') for img in images if img.get('src') and any(keyword in img.get('src', '').lower() for keyword in ['property', 'photo', 'image'])]
        if property_images:
            fields['total_images'] = len(property_images)
            fields['sample_image_urls'] = property_images[:3]  # First 3 images
        
        # 11. Additional Data Attributes
        data_elements = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        data_attrs = {}
        for elem in data_elements:
            for attr, value in elem.attrs.items():
                if attr.startswith('data-') and value:
                    data_attrs[attr] = str(value)[:100]  # Limit length
        
        if data_attrs:
            fields['data_attributes'] = data_attrs
        
        # 12. CSS Classes (for understanding page structure)
        all_classes = set()
        for elem in soup.find_all(class_=True):
            if isinstance(elem.get('class'), list):
                all_classes.update(elem.get('class'))
        fields['css_classes_count'] = len(all_classes)
        
        return fields
    
    def analyze_individual_properties(self, max_properties=10):
        """Analyze individual property pages"""
        print(f"🚀 Starting Individual Property Field Analysis")
        print(f"🎯 Target: {max_properties} individual property pages")
        print("="*60)
        
        try:
            # Setup scraper
            self.scraper.setup_driver()
            
            # Get property URLs from Mumbai
            mumbai_url = self.scraper.generate_city_url('mumbai', 'sale')
            print(f"📋 Getting property URLs from: {mumbai_url}")
            
            properties = self.scraper.scrape_page(mumbai_url)
            print(f"✅ Found {len(properties)} properties")
            
            if not properties:
                print("❌ No properties found. Check if scraper is working correctly.")
                return None
            
            # Analyze individual property pages
            analyzed_count = 0
            for i, prop in enumerate(properties[:max_properties]):
                if 'url' not in prop or not prop['url']:
                    continue
                
                print(f"\n🏠 Analyzing property {analyzed_count + 1}/{max_properties}")
                print(f"   URL: {prop['url']}")
                
                try:
                    # Navigate to property page
                    self.scraper.driver.get(prop['url'])
                    time.sleep(3)
                    
                    # Get page source and analyze
                    soup = BeautifulSoup(self.scraper.driver.page_source, 'html.parser')
                    fields = self.extract_all_fields_from_property(soup, prop['url'])
                    
                    # Store results
                    analysis_result = {
                        'property_index': analyzed_count + 1,
                        'url': prop['url'],
                        'listing_data': prop,
                        'extracted_fields': fields,
                        'total_fields_extracted': len(fields),
                        'analysis_timestamp': datetime.now().isoformat()
                    }
                    
                    self.analyzed_properties.append(analysis_result)
                    
                    # Update global field collection
                    for field_name, field_value in fields.items():
                        self.all_fields[field_name].add(str(field_value)[:100])
                    
                    print(f"   ✅ Extracted {len(fields)} field types")
                    analyzed_count += 1
                    
                    # Respectful delay
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"   ❌ Error analyzing property: {str(e)}")
                    continue
            
            print(f"\n✅ Analysis complete! Analyzed {analyzed_count} properties")
            return self.generate_field_analysis_report()
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
            return None
        finally:
            if self.scraper.driver:
                self.scraper.driver.quit()
    
    def generate_field_analysis_report(self):
        """Generate comprehensive field analysis report"""
        report = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_properties_analyzed': len(self.analyzed_properties),
                'total_unique_fields': len(self.all_fields),
                'analyzer_version': '1.0'
            },
            'field_frequency_analysis': {},
            'sample_properties': self.analyzed_properties[:3],  # First 3 as samples
            'all_analyzed_properties': self.analyzed_properties
        }
        
        # Field frequency analysis
        for field_name, values in self.all_fields.items():
            report['field_frequency_analysis'][field_name] = {
                'frequency': len(values),
                'sample_values': list(values)[:5]  # First 5 samples
            }
        
        return report

def main():
    """Main analysis function"""
    print("🔍 99acres Individual Property Field Analyzer")
    print("=" * 50)
    
    analyzer = IndividualPropertyFieldAnalyzer()
    
    # Analyze 10 individual properties
    report = analyzer.analyze_individual_properties(max_properties=10)
    
    if report:
        # Save comprehensive report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'individual_property_field_analysis_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*60)
        print("🎉 INDIVIDUAL PROPERTY FIELD ANALYSIS COMPLETE!")
        print("="*60)
        print(f"📊 Properties Analyzed: {report['analysis_metadata']['total_properties_analyzed']}")
        print(f"🔍 Unique Fields Found: {report['analysis_metadata']['total_unique_fields']}")
        print(f"📄 Report saved: {filename}")
        
        # Print top fields
        print(f"\n🏆 TOP 20 MOST COMMON FIELDS:")
        sorted_fields = sorted(report['field_frequency_analysis'].items(), 
                             key=lambda x: x[1]['frequency'], reverse=True)
        for i, (field_name, field_info) in enumerate(sorted_fields[:20], 1):
            print(f"   {i:2d}. {field_name}: {field_info['frequency']} occurrences")
            if field_info['sample_values']:
                sample = str(field_info['sample_values'][0])[:50]
                print(f"       Sample: {sample}...")
        
        print(f"\n📋 Analysis complete! Check {filename} for detailed results.")
    else:
        print("❌ Analysis failed. Please check the scraper configuration.")

if __name__ == "__main__":
    main()
