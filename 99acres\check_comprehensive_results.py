#!/usr/bin/env python3
"""
Check Comprehensive Scraping Results
Analyze the results from the comprehensive individual listing scraper
"""

import sqlite3
import json
from datetime import datetime

def check_comprehensive_results():
    """Check the results from comprehensive scraping"""
    
    db_path = 'data/individual_properties_comprehensive.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Comprehensive Individual Property Scraping Results")
        print("=" * 60)
        
        # Get total count
        cursor.execute("SELECT COUNT(*) FROM individual_properties")
        total_count = cursor.fetchone()[0]
        print(f"📊 Total properties scraped: {total_count}")
        
        # Get sample data
        cursor.execute("SELECT * FROM individual_properties LIMIT 3")
        sample_data = cursor.fetchall()
        
        # Get column names
        cursor.execute("PRAGMA table_info(individual_properties)")
        columns = [col[1] for col in cursor.fetchall()]
        
        print(f"\n📋 Database Schema ({len(columns)} columns):")
        for i, col in enumerate(columns, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n📄 Sample Property Data:")
        for i, row in enumerate(sample_data, 1):
            print(f"\n🏠 Property {i}:")
            for j, (col, value) in enumerate(zip(columns, row)):
                if value and col not in ['id', 'scraped_timestamp', 'last_updated']:
                    if isinstance(value, str) and len(value) > 100:
                        print(f"   {col}: {value[:100]}...")
                    else:
                        print(f"   {col}: {value}")
        
        # Field coverage analysis
        print(f"\n📊 Field Coverage Analysis:")
        high_coverage_fields = []
        medium_coverage_fields = []
        low_coverage_fields = []
        
        for column in columns:
            if column not in ['id', 'scraped_timestamp', 'last_updated', 'data_source']:
                cursor.execute(f"SELECT COUNT(*) FROM individual_properties WHERE {column} IS NOT NULL AND {column} != ''")
                count = cursor.fetchone()[0]
                coverage_percentage = (count / total_count) * 100
                
                if coverage_percentage >= 80:
                    high_coverage_fields.append((column, coverage_percentage))
                elif coverage_percentage >= 40:
                    medium_coverage_fields.append((column, coverage_percentage))
                else:
                    low_coverage_fields.append((column, coverage_percentage))
        
        print(f"\n🏆 HIGH COVERAGE FIELDS (80%+): {len(high_coverage_fields)}")
        for field, coverage in sorted(high_coverage_fields, key=lambda x: x[1], reverse=True):
            print(f"   {field}: {coverage:.1f}%")
        
        print(f"\n📈 MEDIUM COVERAGE FIELDS (40-80%): {len(medium_coverage_fields)}")
        for field, coverage in sorted(medium_coverage_fields, key=lambda x: x[1], reverse=True):
            print(f"   {field}: {coverage:.1f}%")
        
        print(f"\n📉 LOW COVERAGE FIELDS (<40%): {len(low_coverage_fields)}")
        for field, coverage in sorted(low_coverage_fields, key=lambda x: x[1], reverse=True):
            print(f"   {field}: {coverage:.1f}%")
        
        # City and type distribution
        cursor.execute("SELECT city, COUNT(*) FROM individual_properties WHERE city IS NOT NULL GROUP BY city")
        city_dist = cursor.fetchall()
        
        print(f"\n🏙️ City Distribution:")
        for city, count in city_dist:
            print(f"   {city}: {count} properties")
        
        # Price analysis
        cursor.execute("SELECT AVG(price_crores), MIN(price_crores), MAX(price_crores) FROM individual_properties WHERE price_crores IS NOT NULL")
        price_stats = cursor.fetchone()
        
        if price_stats[0]:
            print(f"\n💰 Price Analysis (Crores):")
            print(f"   Average: ₹{price_stats[0]:.2f} Cr")
            print(f"   Minimum: ₹{price_stats[1]:.2f} Cr")
            print(f"   Maximum: ₹{price_stats[2]:.2f} Cr")
        
        # BHK distribution
        cursor.execute("SELECT bhk_config, COUNT(*) FROM individual_properties WHERE bhk_config IS NOT NULL GROUP BY bhk_config")
        bhk_dist = cursor.fetchall()
        
        print(f"\n🛏️ BHK Configuration Distribution:")
        for bhk, count in bhk_dist:
            print(f"   {bhk}: {count} properties")
        
        conn.close()
        
        # Summary
        print(f"\n" + "=" * 60)
        print(f"🎉 COMPREHENSIVE SCRAPING SUCCESS!")
        print(f"=" * 60)
        print(f"✅ Successfully extracted data from {total_count} properties")
        print(f"📊 {len(high_coverage_fields)} fields with 80%+ coverage")
        print(f"📈 {len(medium_coverage_fields)} fields with 40-80% coverage")
        print(f"🔍 Total unique data fields: {len(columns) - 4}")  # Excluding metadata columns
        print(f"🏙️ Cities covered: {len(city_dist)}")
        print(f"💾 Database: {db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking results: {str(e)}")
        return False

if __name__ == "__main__":
    check_comprehensive_results()
