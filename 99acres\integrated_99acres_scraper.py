#!/usr/bin/env python3
"""
99acres Comprehensive Property Scraper
Based on the sophisticated MagicBricks scraper architecture.

Features:
- Comprehensive property data extraction
- Incremental scraping capabilities
- Multi-city support
- Advanced error handling
- Database integration
- Export functionality
"""

import time
import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import random
import logging
import os
import re
from data_validation_system import DataValidationSystem
from urllib.parse import urljoin, urlparse


class Integrated99acresScraper:
    """
    Comprehensive 99acres property scraper with advanced features
    """
    
    def __init__(self, headless=True, database_path="data/99acres_properties.db"):
        self.headless = headless
        self.database_path = database_path
        self.driver = None
        self.base_url = "https://www.99acres.com"
        self.session_start_time = datetime.now()

        # Initialize validation system
        self.validator = DataValidationSystem()

        # Initialize logging
        self.setup_logging()

        # Initialize database
        self.setup_database()
        
        # Property data schema (matching MagicBricks for compatibility)
        # Enhanced property schema with all 50+ fields from research
        self.property_schema = {
            # Basic Information
            'title': '',
            'property_type': '',
            'bedrooms': '',
            'bathrooms': '',
            'balconies': '',

            # Price Information
            'price': '',
            'price_per_sqft': '',
            'emi': '',
            'booking_amount': '',
            'price_range': '',
            'currency': 'INR',

            # Area Details
            'area': '',
            'area_unit': 'sqft',
            'carpet_area': '',
            'buildup_area': '',
            'super_buildup_area': '',
            'plot_area': '',

            # Property Specifications
            'floor': '',
            'total_floors': '',
            'facing': '',
            'age': '',
            'furnishing': '',
            'parking': '',
            'construction_status': '',
            'possession': '',
            'completion': '',

            # Location Information
            'society': '',
            'locality': '',
            'city': '',
            'state': '',
            'pincode': '',
            'latitude': '',
            'longitude': '',

            # Builder/Agent Information
            'builder': '',
            'agent_name': '',
            'agent_phone': '',
            'agent_type': '',
            'posted_time': '',
            'posted_date': '',

            # Legal & Compliance
            'rera_id': '',
            'rera_approved': '',
            'verification_status': '',
            'verified': '',

            # Property Features
            'amenities': '',
            'highlights': '',
            'special_features': '',

            # Media & Contact
            'property_url': '',
            'image_urls': '',
            'images': '',
            'has_video': '',
            'contact_buttons': '',
            'masked_phones': '',

            # Transaction Details
            'transaction_type': '',  # Resale, New Booking
            'property_status': '',   # Ready, Under Construction
            'listing_type': '',      # Individual, Project

            # Additional Details
            'description': '',
            'property_id': '',
            'project_name': '',
            'tower_name': '',
            'unit_number': '',

            # Metadata
            'scraped_at': '',
            'source': '99acres',
            'data_quality_score': 0
        }
        
        # CSS selectors (will be populated from research)
        self.selectors = {
            'property_cards': 'div[data-testid="property-card"], .property-card, .srpTuple',
            'title': 'h2, .property-title, [class*="title"]',
            'price': '[class*="price"], [class*="cost"]',
            'area': '[class*="area"], [class*="size"]',
            'bhk': '[class*="bhk"], [class*="bedroom"]',
            'location': '[class*="location"], [class*="address"]',
            'contact': 'button:contains("View Number"), button:contains("Contact")',
            'images': 'img',
            'links': 'a[href*="spid-"], a[href*="npxid-"]',
            'rera': '[class*="rera"]',
            'agent': '[class*="agent"], [class*="dealer"]'
        }
        
        self.logger.info("99acres scraper initialized successfully")
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        log_filename = f"{log_dir}/99acres_scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def setup_database(self):
        """Setup SQLite database with property schema"""
        os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Create enhanced properties table with all 50+ fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS properties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,

                -- Basic Information
                title TEXT,
                property_type TEXT,
                bedrooms TEXT,
                bathrooms TEXT,
                balconies TEXT,

                -- Price Information (Enhanced based on deep research)
                price TEXT,
                price_per_sqft TEXT,
                emi TEXT,
                booking_amount TEXT,
                price_range TEXT,
                min_price TEXT,
                max_price TEXT,
                price_unit TEXT,
                price_negotiable TEXT,
                maintenance_charges TEXT,
                currency TEXT DEFAULT 'INR',

                -- Area Details (Enhanced based on deep research)
                area TEXT,
                area_unit TEXT DEFAULT 'sqft',
                carpet_area TEXT,
                buildup_area TEXT,
                super_buildup_area TEXT,
                plot_area TEXT,
                area_range TEXT,
                area_type TEXT,
                land_area TEXT,
                covered_area TEXT,

                -- Property Specifications
                floor TEXT,
                total_floors TEXT,
                facing TEXT,
                age TEXT,
                furnishing TEXT,
                parking TEXT,
                construction_status TEXT,
                possession TEXT,
                completion TEXT,

                -- Location Information (Enhanced based on deep research)
                society TEXT,
                locality TEXT,
                sub_locality TEXT,
                city TEXT,
                state TEXT,
                pincode TEXT,
                area_code TEXT,
                latitude TEXT,
                longitude TEXT,
                address_line1 TEXT,
                address_line2 TEXT,
                landmark TEXT,
                distance_from_metro TEXT,
                connectivity_info TEXT,

                -- Builder/Agent Information
                builder TEXT,
                agent_name TEXT,
                agent_phone TEXT,
                agent_type TEXT,
                posted_time TEXT,
                posted_date TEXT,

                -- Legal & Compliance
                rera_id TEXT,
                rera_approved TEXT,
                verification_status TEXT,
                verified TEXT,

                -- Property Features (Enhanced based on deep research)
                amenities TEXT,
                amenities_basic TEXT,
                amenities_recreational TEXT,
                amenities_sports TEXT,
                amenities_convenience TEXT,
                amenities_technology TEXT,
                amenities_safety TEXT,
                highlights TEXT,
                special_features TEXT,
                property_highlights TEXT,
                unique_selling_points TEXT,

                -- Media & Contact
                property_url TEXT UNIQUE,
                image_urls TEXT,
                images TEXT,
                has_video TEXT,
                contact_buttons TEXT,
                masked_phones TEXT,

                -- Transaction Details
                transaction_type TEXT,
                property_status TEXT,
                listing_type TEXT,

                -- Additional Details (Enhanced based on deep research)
                description TEXT,
                property_id TEXT,
                project_name TEXT,
                tower_name TEXT,
                unit_number TEXT,
                configuration_details TEXT,
                floor_plan_details TEXT,

                -- Quality and Validation (Enhanced)
                data_quality_score INTEGER DEFAULT 0,
                field_completeness_score INTEGER DEFAULT 0,
                validation_status TEXT,
                extraction_method TEXT,
                structured_data_available TEXT,

                -- Research Insights (New fields from deep analysis)
                container_class TEXT,
                extraction_confidence INTEGER DEFAULT 0,
                city_validated TEXT,
                property_type_validated TEXT,

                -- Metadata
                scraped_at TEXT,
                source TEXT DEFAULT '99acres',
                last_updated TEXT,
                scraping_session_id TEXT
            )
        ''')
        
        # Create scraping sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scraping_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE,
                city TEXT,
                start_time TEXT,
                end_time TEXT,
                total_properties INTEGER,
                new_properties INTEGER,
                updated_properties INTEGER,
                status TEXT,
                error_message TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"Database initialized: {self.database_path}")
    
    def setup_driver(self):
        """Setup Chrome WebDriver with anti-detection measures"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Basic options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Anti-detection measures
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Random user agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        self.logger.info("Chrome WebDriver initialized with anti-detection measures")
    
    def generate_city_url(self, city, property_type='sale'):
        """Generate URL for city property listings"""
        if property_type == 'sale':
            return f"{self.base_url}/property-for-sale-in-{city.lower()}-ffid"
        elif property_type == 'rent':
            return f"{self.base_url}/property-for-rent-in-{city.lower()}-ffid"
        else:
            raise ValueError("property_type must be 'sale' or 'rent'")
    
    def random_delay(self, min_seconds=1, max_seconds=3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def extract_property_data(self, property_card):
        """Extract property data from a property card element with JSON-LD priority"""
        property_data = self.property_schema.copy()

        try:
            # Priority Method: JSON-LD Structured Data Extraction
            structured_data = self._extract_structured_data(property_card)
            if structured_data:
                property_data.update(structured_data)
                self.logger.debug("Used structured data extraction")

            # Fallback: Enhanced text-based extraction for all 50+ fields
            card_text = property_card.get_text()

            # Extract title (if not from structured data)
            if not property_data.get('title'):
                property_data['title'] = self._extract_title(property_card)

            # Extract all price-related fields
            price_data = self._extract_price_fields(card_text)
            for key, value in price_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract all area-related fields
            area_data = self._extract_area_fields(card_text)
            for key, value in area_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract property configuration
            config_data = self._extract_configuration_fields(card_text)
            for key, value in config_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract location information
            location_data = self._extract_location_fields(card_text, property_data.get('title', ''))
            for key, value in location_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract property specifications
            spec_data = self._extract_specification_fields(card_text)
            for key, value in spec_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract builder/agent information
            agent_data = self._extract_agent_fields(card_text)
            for key, value in agent_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract legal and compliance data
            legal_data = self._extract_legal_fields(card_text)
            for key, value in legal_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract amenities and features
            amenities_data = self._extract_amenities_fields(card_text)
            for key, value in amenities_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract media and contact information
            media_data = self._extract_media_fields(property_card)
            for key, value in media_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Extract price
            card_text = property_card.get_text()

            # Look for main price patterns (prioritize single property prices)
            price_patterns = [
                r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)',  # Single Cr price (not range)
                r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)',  # Single Lakh price (not range)
                r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*crore(?!\s*-)',  # Single crore price
                r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*lakh(?!\s*-)',  # Single lakh price
            ]

            for i, pattern in enumerate(price_patterns):
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    amount = matches[0]
                    unit = 'Cr' if i < 2 else ('Cr' if 'crore' in pattern else 'Lakh')
                    property_data['price'] = f"₹{amount} {unit}"
                    break

            # If no single price found, try price ranges for projects
            if not property_data['price']:
                range_patterns = [
                    r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr',
                    r'₹(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh'
                ]

                for pattern in range_patterns:
                    matches = re.findall(pattern, card_text, re.IGNORECASE)
                    if matches:
                        min_price, max_price = matches[0]
                        unit = 'Cr' if 'Cr' in pattern else 'Lakh'
                        property_data['price'] = f"₹{min_price} - {max_price} {unit}"
                        break

            # Look for price per sqft
            price_per_sqft_match = re.search(r'₹\s*([\d,]+)\s*/sqft', card_text, re.IGNORECASE)
            if price_per_sqft_match:
                property_data['price_per_sqft'] = f"₹{price_per_sqft_match.group(1)} /sqft"

            # Extract area - be more specific about area types
            area_patterns = [
                (r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Carpet\s*Area', 'carpet'),
                (r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Super\s*Built-up\s*Area', 'super_buildup'),
                (r'(\d+(?:,\d+)*)\s*sqft\s*\([^)]*\)\s*Built-up\s*Area', 'buildup'),
                (r'(\d+(?:,\d+)*)\s*sqft', 'general')
            ]

            for pattern, area_type in area_patterns:
                matches = re.findall(pattern, card_text, re.IGNORECASE)
                if matches:
                    # Get the first area of this type
                    area = matches[0].replace(',', '')
                    property_data['area'] = area
                    property_data['area_unit'] = f'sqft ({area_type})'
                    break

            # Extract BHK information
            bhk_matches = re.findall(r'(\d+)\s*bhk', card_text, re.IGNORECASE)
            if bhk_matches:
                property_data['bedrooms'] = bhk_matches[0]

            # Extract bathroom information
            bath_matches = re.findall(r'(\d+)\s*bath', card_text, re.IGNORECASE)
            if bath_matches:
                property_data['bathrooms'] = bath_matches[0]

            # Extract property URL
            links = property_card.find_all('a', href=True)
            for link in links:
                href = link.get('href', '')
                if 'spid-' in href or 'npxid-' in href:
                    if href.startswith('/'):
                        property_data['property_url'] = urljoin(self.base_url, href)
                    else:
                        property_data['property_url'] = href
                    break

            # Extract location information from title
            if property_data['title']:
                # Extract locality from title (after "in")
                location_patterns = [
                    r'in\s+([^,]+),\s*([^,]+)',  # "in Locality, City"
                    r'in\s+([^,]+)',  # "in Locality"
                ]

                for pattern in location_patterns:
                    location_match = re.search(pattern, property_data['title'])
                    if location_match:
                        if len(location_match.groups()) == 2:
                            property_data['locality'] = location_match.group(1).strip()
                            city_part = location_match.group(2).strip()
                            if 'mumbai' in city_part.lower():
                                property_data['city'] = 'Mumbai'
                        else:
                            property_data['locality'] = location_match.group(1).strip()
                        break

            # Extract society/project name from card text
            society_patterns = [
                r'^([A-Z][^₹\n]*?)(?:RESALE|NEW BOOKING)',  # Project name before RESALE/NEW BOOKING
                r'([A-Z][A-Za-z\s&]+?)(?:\s+RESALE|\s+NEW)',  # Project name before status
            ]

            for pattern in society_patterns:
                society_match = re.search(pattern, card_text, re.MULTILINE)
                if society_match:
                    society_name = society_match.group(1).strip()
                    if len(society_name) > 3 and society_name not in property_data['title']:
                        property_data['society'] = society_name
                        break

            # Set default city and state
            if not property_data['city']:
                property_data['city'] = 'Mumbai'
            property_data['state'] = 'Maharashtra'

            # Classify property type and transaction type
            classification_data = self._classify_property(property_data, card_text)
            for key, value in classification_data.items():
                if not property_data.get(key):
                    property_data[key] = value

            # Validate and clean property data using comprehensive validation system
            is_valid, validation_errors, cleaned_data = self.validator.validate_property_data(property_data)

            # Use cleaned data and add validation metadata
            property_data = cleaned_data
            property_data['validation_errors'] = '; '.join(validation_errors) if validation_errors else ''
            property_data['is_valid'] = is_valid

            # Set scraped timestamp and session info
            property_data['scraped_at'] = datetime.now().isoformat()
            property_data['scraping_session_id'] = f"session_{self.session_start_time.strftime('%Y%m%d_%H%M%S')}"

        except Exception as e:
            self.logger.error(f"Error extracting property data: {str(e)}")

        return property_data

    def _calculate_data_quality_score(self, property_data):
        """Calculate data quality score based on field completeness"""
        # High priority fields (weight: 3)
        high_priority = ['title', 'price', 'area', 'bedrooms', 'locality', 'property_url']
        # Medium priority fields (weight: 2)
        medium_priority = ['bathrooms', 'property_type', 'city', 'agent_type', 'construction_status']
        # Low priority fields (weight: 1)
        low_priority = ['amenities', 'floor', 'facing', 'age', 'rera_id', 'images']

        score = 0
        max_score = 0

        # Calculate score for high priority fields
        for field in high_priority:
            max_score += 3
            if property_data.get(field) and property_data[field].strip():
                score += 3

        # Calculate score for medium priority fields
        for field in medium_priority:
            max_score += 2
            if property_data.get(field) and property_data[field].strip():
                score += 2

        # Calculate score for low priority fields
        for field in low_priority:
            max_score += 1
            if property_data.get(field) and property_data[field].strip():
                score += 1

        # Return percentage score
        return int((score / max_score) * 100) if max_score > 0 else 0

    def _extract_structured_data(self, property_card):
        """Extract data from JSON-LD structured data (Priority Method)"""
        structured_data = {}

        try:
            # Look for JSON-LD scripts in the property card or nearby
            current = property_card
            for _ in range(5):  # Look up the DOM tree
                if current is None:
                    break

                json_scripts = current.find_all('script', type='application/ld+json')
                for script in json_scripts:
                    try:
                        data = json.loads(script.string)
                        if isinstance(data, dict):
                            extracted = self._parse_structured_data(data)
                            if extracted:
                                structured_data.update(extracted)
                        elif isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    extracted = self._parse_structured_data(item)
                                    if extracted:
                                        structured_data.update(extracted)
                    except (json.JSONDecodeError, AttributeError):
                        continue

                current = current.parent

            # Also check the entire page for structured data
            if not structured_data:
                page_soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                json_scripts = page_soup.find_all('script', type='application/ld+json')
                for script in json_scripts:
                    try:
                        data = json.loads(script.string)
                        if isinstance(data, dict):
                            extracted = self._parse_structured_data(data)
                            if extracted:
                                structured_data.update(extracted)
                                break  # Use first valid structured data
                    except (json.JSONDecodeError, AttributeError):
                        continue

        except Exception as e:
            self.logger.debug(f"Structured data extraction error: {str(e)}")

        return structured_data

    def _parse_structured_data(self, data):
        """Parse JSON-LD structured data into property fields"""
        parsed = {}

        try:
            # Check if this is property-related structured data
            data_type = data.get('@type', '').lower()
            if not any(prop_type in data_type for prop_type in ['apartment', 'house', 'residence', 'property']):
                return parsed

            # Extract basic information
            if 'name' in data:
                parsed['title'] = data['name']

            # Extract area information
            if 'floorSize' in data:
                floor_size = data['floorSize']
                if isinstance(floor_size, dict):
                    if 'value' in floor_size:
                        parsed['area'] = str(floor_size['value'])
                elif isinstance(floor_size, (int, float, str)):
                    parsed['area'] = str(floor_size)

            # Extract room information
            if 'numberOfRooms' in data:
                parsed['bedrooms'] = str(data['numberOfRooms'])

            if 'numberOfBathroomsTotal' in data:
                parsed['bathrooms'] = str(data['numberOfBathroomsTotal'])

            # Extract floor information
            if 'floorLevel' in data:
                parsed['floor'] = str(data['floorLevel'])

            # Extract address and location
            if 'address' in data:
                address = data['address']
                if isinstance(address, dict):
                    if 'streetAddress' in address:
                        parsed['locality'] = address['streetAddress']
                    if 'addressLocality' in address:
                        parsed['city'] = address['addressLocality']
                    if 'addressRegion' in address:
                        parsed['state'] = address['addressRegion']
                    if 'postalCode' in address:
                        parsed['pincode'] = address['postalCode']
                elif isinstance(address, str):
                    parsed['locality'] = address

            # Extract GPS coordinates
            if 'geo' in data:
                geo = data['geo']
                if isinstance(geo, dict):
                    if 'latitude' in geo and 'longitude' in geo:
                        parsed['latitude'] = str(geo['latitude'])
                        parsed['longitude'] = str(geo['longitude'])

            # Extract price information
            if 'offers' in data:
                offers = data['offers']
                if isinstance(offers, dict):
                    if 'price' in offers:
                        parsed['price'] = str(offers['price'])
                    if 'priceCurrency' in offers:
                        parsed['currency'] = offers['priceCurrency']
                elif isinstance(offers, list) and offers:
                    offer = offers[0]
                    if isinstance(offer, dict):
                        if 'price' in offer:
                            parsed['price'] = str(offer['price'])

            # Extract description
            if 'description' in data:
                parsed['description'] = data['description']

            # Extract images
            if 'image' in data:
                images = data['image']
                if isinstance(images, list):
                    parsed['images'] = images
                elif isinstance(images, str):
                    parsed['images'] = [images]

        except Exception as e:
            self.logger.debug(f"Structured data parsing error: {str(e)}")

        return parsed

    def _extract_title(self, property_card):
        """Extract property title"""
        # Try headings first
        headings = property_card.find_all(['h1', 'h2', 'h3', 'h4'])
        for heading in headings:
            text = heading.get_text(strip=True)
            if text and ('bhk' in text.lower() or 'apartment' in text.lower() or 'flat' in text.lower()):
                return text

        # Try property links
        links = property_card.find_all('a', href=True)
        for link in links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                text = link.get_text(strip=True)
                if text and len(text) > 10:
                    return text

        return ''

    def _extract_price_fields(self, card_text):
        """Extract all price-related fields"""
        price_data = {}

        # Main price patterns (prioritize single property prices)
        price_patterns = [
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Cr(?!\s*-)', 'Cr'),
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lakh(?!\s*-)', 'Lakh'),
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*Lac(?!\s*-)', 'Lakh'),  # Added "Lac" variant
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*crore(?!\s*-)', 'Cr'),
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*lakh(?!\s*-)', 'Lakh'),
            (r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*lac(?!\s*-)', 'Lakh'),   # Added "lac" variant
        ]

        for pattern, unit in price_patterns:
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                price_data['price'] = f"₹{matches[0]} {unit}"
                break

        # Enhanced price ranges for projects
        if not price_data.get('price'):
            price_data.update(self._extract_price_ranges(card_text))

        # Price per sqft
        price_per_sqft_match = re.search(r'₹\s*([\d,]+)\s*/sqft', card_text, re.IGNORECASE)
        if price_per_sqft_match:
            price_data['price_per_sqft'] = f"₹{price_per_sqft_match.group(1)} /sqft"

        # EMI
        emi_match = re.search(r'EMI\s*₹\s*([\d,]+)', card_text, re.IGNORECASE)
        if emi_match:
            price_data['emi'] = f"₹{emi_match.group(1)}"

        # Booking amount
        booking_match = re.search(r'Booking\s*Amount\s*₹\s*([\d,]+)', card_text, re.IGNORECASE)
        if booking_match:
            price_data['booking_amount'] = f"₹{booking_match.group(1)}"

        return price_data

    def _extract_price_ranges(self, card_text):
        """Enhanced price range extraction for project properties"""
        price_data = {}

        # Multiple price range patterns
        range_patterns = [
            # Standard range patterns
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*-\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*to\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*–\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',  # En dash
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*—\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',  # Em dash

            # Starting from patterns
            r'Starting\s*from\s*₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            r'Starts\s*at\s*₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',
            r'From\s*₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)',

            # Onwards patterns
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)\s*onwards',
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(Cr|Lakh|Lac)\s*onward',
        ]

        # Try range patterns first
        for pattern in range_patterns[:4]:  # First 4 are range patterns
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                min_price, max_price, unit = matches[0]
                # Normalize unit
                unit = self._normalize_price_unit(unit)

                # Validate range (min should be less than max)
                try:
                    min_val = float(min_price.replace(',', ''))
                    max_val = float(max_price.replace(',', ''))
                    if min_val < max_val:
                        price_data['price'] = f"₹{min_price} - {max_price} {unit}"
                        price_data['price_range'] = f"₹{min_price} - {max_price} {unit}"
                        price_data['min_price'] = f"₹{min_price} {unit}"
                        price_data['max_price'] = f"₹{max_price} {unit}"
                        price_data['listing_type'] = 'Project'
                        return price_data
                except ValueError:
                    continue

        # Try "starting from" patterns
        for pattern in range_patterns[4:7]:  # Starting from patterns
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                if len(matches[0]) == 2:  # Starting from pattern
                    start_price, unit = matches[0]
                    unit = self._normalize_price_unit(unit)
                    price_data['price'] = f"Starting from ₹{start_price} {unit}"
                    price_data['min_price'] = f"₹{start_price} {unit}"
                    price_data['listing_type'] = 'Project'
                    return price_data

        # Try "onwards" patterns
        for pattern in range_patterns[7:]:  # Onwards patterns
            matches = re.findall(pattern, card_text, re.IGNORECASE)
            if matches:
                start_price, unit = matches[0]
                unit = self._normalize_price_unit(unit)
                price_data['price'] = f"₹{start_price} {unit} onwards"
                price_data['min_price'] = f"₹{start_price} {unit}"
                price_data['listing_type'] = 'Project'
                return price_data

        return price_data

    def _normalize_price_unit(self, unit):
        """Normalize price unit variations"""
        unit_lower = unit.lower()
        if unit_lower in ['lakh', 'lac']:
            return 'Lakh'
        elif unit_lower in ['cr', 'crore']:
            return 'Cr'
        else:
            return unit.title()

    def _extract_area_fields(self, card_text):
        """Extract all area-related fields"""
        area_data = {}

        # Specific area types
        area_patterns = {
            'carpet_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Carpet\s*Area',
            'buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Built-up\s*Area',
            'super_buildup_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Super\s*Built-up\s*Area',
            'plot_area': r'(\d+(?:,\d+)*)\s*sqft\s*.*?Plot\s*Area'
        }

        for area_type, pattern in area_patterns.items():
            match = re.search(pattern, card_text, re.IGNORECASE)
            if match:
                area_data[area_type] = f"{match.group(1)} sqft"

        # General area (if no specific type found)
        if not any(area_data.values()):
            general_area_match = re.search(r'(\d+(?:,\d+)*)\s*sqft', card_text, re.IGNORECASE)
            if general_area_match:
                area_data['area'] = general_area_match.group(1).replace(',', '')

        return area_data

    def _extract_configuration_fields(self, card_text):
        """Extract property configuration fields"""
        config_data = {}

        # BHK
        bhk_match = re.search(r'(\d+)\s*BHK', card_text, re.IGNORECASE)
        if bhk_match:
            config_data['bedrooms'] = bhk_match.group(1)

        # Bathrooms
        bath_match = re.search(r'(\d+)\s*Bath', card_text, re.IGNORECASE)
        if bath_match:
            config_data['bathrooms'] = bath_match.group(1)

        # Balconies
        balcony_match = re.search(r'(\d+)\s*Balcon', card_text, re.IGNORECASE)
        if balcony_match:
            config_data['balconies'] = balcony_match.group(1)

        # Parking
        parking_match = re.search(r'(\d+)\s*Parking', card_text, re.IGNORECASE)
        if parking_match:
            config_data['parking'] = parking_match.group(1)

        return config_data

    def _extract_location_fields(self, card_text, title):
        """Extract location-related fields"""
        location_data = {}

        # Extract locality from title
        if title:
            location_patterns = [
                r'in\s+([^,]+),\s*([^,]+)',  # "in Locality, City"
                r'in\s+([^,]+)',  # "in Locality"
            ]

            for pattern in location_patterns:
                location_match = re.search(pattern, title)
                if location_match:
                    if len(location_match.groups()) == 2:
                        location_data['locality'] = location_match.group(1).strip()
                        city_part = location_match.group(2).strip()
                        if 'mumbai' in city_part.lower():
                            location_data['city'] = 'Mumbai'
                    else:
                        location_data['locality'] = location_match.group(1).strip()
                    break

        # Extract society/project name
        society_patterns = [
            r'^([A-Z][^₹\n]*?)(?:RESALE|NEW BOOKING)',
            r'([A-Z][A-Za-z\s&]+?)(?:\s+RESALE|\s+NEW)',
        ]

        for pattern in society_patterns:
            society_match = re.search(pattern, card_text, re.MULTILINE)
            if society_match:
                society_name = society_match.group(1).strip()
                if len(society_name) > 3:
                    location_data['society'] = society_name
                    break

        # Extract pincode
        pincode_match = re.search(r'(\d{6})', card_text)
        if pincode_match:
            location_data['pincode'] = pincode_match.group(1)

        # Set default city and state
        if not location_data.get('city'):
            location_data['city'] = 'Mumbai'
        location_data['state'] = 'Maharashtra'

        return location_data

    def _extract_specification_fields(self, card_text):
        """Extract property specification fields"""
        spec_data = {}

        # Floor details
        floor_match = re.search(r'(\d+)(?:st|nd|rd|th)\s*Floor', card_text, re.IGNORECASE)
        if floor_match:
            spec_data['floor'] = floor_match.group(1)

        total_floors_match = re.search(r'of\s*(\d+)\s*Floors', card_text, re.IGNORECASE)
        if total_floors_match:
            spec_data['total_floors'] = total_floors_match.group(1)

        # Facing direction
        facing_match = re.search(r'(North|South|East|West|North-East|North-West|South-East|South-West)\s*Facing', card_text, re.IGNORECASE)
        if facing_match:
            spec_data['facing'] = facing_match.group(1)

        # Age
        age_match = re.search(r'(\d+)\s*Years?\s*Old', card_text, re.IGNORECASE)
        if age_match:
            spec_data['age'] = f"{age_match.group(1)} years"

        # Furnishing
        furnishing_match = re.search(r'(Furnished|Semi-Furnished|Unfurnished)', card_text, re.IGNORECASE)
        if furnishing_match:
            spec_data['furnishing'] = furnishing_match.group(1)

        # Construction status
        status_match = re.search(r'(Ready To Move|Under Construction|New Launch|Partially Ready)', card_text, re.IGNORECASE)
        if status_match:
            spec_data['construction_status'] = status_match.group(1)

        # Possession
        possession_match = re.search(r'Possession\s*in\s*([^,\n]+)', card_text, re.IGNORECASE)
        if possession_match:
            spec_data['possession'] = possession_match.group(1).strip()

        # Property type (enhanced patterns)
        property_type_patterns = [
            r'(Apartment|Villa|Independent House|Builder Floor|Studio|Penthouse|Plot)',
            r'(Flat|House|Bungalow|Row House|Duplex)',
            r'(\d+\s*BHK)\s*(Apartment|Flat|House|Villa)'
        ]

        for pattern in property_type_patterns:
            property_type_match = re.search(pattern, card_text, re.IGNORECASE)
            if property_type_match:
                if len(property_type_match.groups()) == 2:
                    spec_data['property_type'] = property_type_match.group(2)  # Get the type part
                else:
                    spec_data['property_type'] = property_type_match.group(1)
                break

        # Transaction type
        if 'resale' in card_text.lower():
            spec_data['transaction_type'] = 'Resale'
        elif 'new booking' in card_text.lower() or 'new launch' in card_text.lower():
            spec_data['transaction_type'] = 'New Booking'

        return spec_data

    def _extract_agent_fields(self, card_text):
        """Extract builder/agent information"""
        agent_data = {}

        # Builder name
        builder_patterns = [
            r'Builder:\s*([^\n]+)',
            r'by\s+([A-Z][A-Za-z\s&]+)',
            r'Developed\s+by\s+([^\n]+)'
        ]

        for pattern in builder_patterns:
            builder_match = re.search(pattern, card_text, re.IGNORECASE)
            if builder_match:
                agent_data['builder'] = builder_match.group(1).strip()
                break

        # Agent type
        agent_type_match = re.search(r'(Owner|Dealer|Builder|Agent)', card_text, re.IGNORECASE)
        if agent_type_match:
            agent_data['agent_type'] = agent_type_match.group(1)

        # Posted time
        posted_match = re.search(r'(\d+[dwmy])\s*ago', card_text, re.IGNORECASE)
        if posted_match:
            agent_data['posted_time'] = posted_match.group(1) + ' ago'

        return agent_data

    def _extract_legal_fields(self, card_text):
        """Extract legal and compliance information"""
        legal_data = {}

        # RERA ID
        rera_patterns = [
            r'RERA\s*ID[:\s]*([A-Z0-9]+)',
            r'RERA\s*No[:\s]*([A-Z0-9]+)',
            r'Registration\s*No[:\s]*([A-Z0-9]+)'
        ]

        for pattern in rera_patterns:
            rera_match = re.search(pattern, card_text, re.IGNORECASE)
            if rera_match:
                legal_data['rera_id'] = rera_match.group(1)
                break

        # Verification status
        if 'verified' in card_text.lower():
            legal_data['verified'] = 'Yes'
            legal_data['verification_status'] = 'Verified'

        if 'rera approved' in card_text.lower():
            legal_data['rera_approved'] = 'Yes'

        return legal_data

    def _extract_amenities_fields(self, card_text):
        """Extract amenities and features"""
        amenities_data = {}

        # Enhanced amenities detection with variations
        amenity_patterns = {
            'Swimming Pool': ['swimming pool', 'pool', 'swimming'],
            'Gym': ['gym', 'fitness', 'gymnasium'],
            'Club House': ['club house', 'clubhouse', 'club'],
            'Garden': ['garden', 'landscaped', 'green area'],
            'Security': ['security', '24x7 security', '24/7 security'],
            'Lift': ['lift', 'elevator'],
            'Power Backup': ['power backup', 'generator', 'backup power'],
            'Car Parking': ['parking', 'car parking', 'covered parking'],
            'Playground': ['playground', 'play area', 'kids play'],
            'Jogging Track': ['jogging track', 'jogging', 'walking track'],
            'CCTV': ['cctv', 'surveillance', 'camera'],
            'Intercom': ['intercom', 'video door phone'],
            'Fire Safety': ['fire safety', 'fire fighting', 'fire alarm'],
            'Maintenance': ['maintenance', 'housekeeping'],
            'Water Supply': ['water supply', '24x7 water', 'borewell']
        }

        found_amenities = []
        text_lower = card_text.lower()

        for amenity_name, patterns in amenity_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    found_amenities.append(amenity_name)
                    break  # Found this amenity, move to next

        if found_amenities:
            amenities_data['amenities'] = ', '.join(found_amenities)

        return amenities_data

    def _extract_media_fields(self, property_card):
        """Extract media and contact information"""
        media_data = {}

        # Extract property URL
        links = property_card.find_all('a', href=True)
        for link in links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                if href.startswith('/'):
                    media_data['property_url'] = urljoin(self.base_url, href)
                else:
                    media_data['property_url'] = href

                # Extract property ID
                if 'spid-' in href:
                    property_id_match = re.search(r'spid-([A-Z0-9]+)', href)
                    if property_id_match:
                        media_data['property_id'] = property_id_match.group(1)
                elif 'npxid-' in href:
                    property_id_match = re.search(r'npxid-([A-Z0-9]+)', href)
                    if property_id_match:
                        media_data['property_id'] = property_id_match.group(1)
                break

        # Count images
        images = property_card.find_all('img')
        if images:
            image_urls = []
            for img in images:
                src = img.get('src') or img.get('data-src')
                if src:
                    image_urls.append(src)
            if image_urls:
                media_data['image_urls'] = ', '.join(image_urls[:5])  # First 5 images
                media_data['images'] = str(len(image_urls))

        # Check for video
        videos = property_card.find_all(['video', 'iframe'])
        if videos:
            media_data['has_video'] = 'Yes'

        # Contact buttons
        contact_buttons = property_card.find_all(['button', 'a'], string=re.compile(r'contact|call|phone', re.IGNORECASE))
        if contact_buttons:
            media_data['contact_buttons'] = str(len(contact_buttons))

        return media_data

    def _classify_property(self, property_data, card_text):
        """Comprehensive property classification system"""
        classification = {}
        text_lower = card_text.lower()
        title_lower = property_data.get('title', '').lower()

        # Property Type Classification
        property_type = self._classify_property_type(text_lower, title_lower)
        if property_type:
            classification['property_type'] = property_type

        # Transaction Type Classification
        transaction_type = self._classify_transaction_type(text_lower, title_lower)
        if transaction_type:
            classification['transaction_type'] = transaction_type

        # Property Status Classification
        property_status = self._classify_property_status(text_lower)
        if property_status:
            classification['property_status'] = property_status

        # Listing Type Classification
        listing_type = self._classify_listing_type(property_data, text_lower)
        if listing_type:
            classification['listing_type'] = listing_type

        return classification

    def _classify_property_type(self, text_lower, title_lower):
        """Classify the type of property"""
        # Property type patterns with priority
        property_patterns = [
            # High specificity patterns
            ('Penthouse', ['penthouse']),
            ('Studio', ['studio', '1rk']),
            ('Villa', ['villa', 'bungalow', 'row house']),
            ('Independent House', ['independent house', 'independent home']),
            ('Builder Floor', ['builder floor', 'builder flr']),
            ('Plot', ['plot', 'land', 'na plot']),
            ('Farm House', ['farm house', 'farmhouse']),

            # Medium specificity patterns
            ('Duplex', ['duplex']),
            ('Triplex', ['triplex']),
            ('Apartment', ['apartment', 'apt']),
            ('Flat', ['flat', 'bhk flat', 'bhk apartment']),

            # Generic patterns (lowest priority)
            ('Residential', ['residential', 'home', 'house']),
        ]

        # Check title first (more reliable)
        for prop_type, patterns in property_patterns:
            for pattern in patterns:
                if pattern in title_lower:
                    return prop_type

        # Check full text if not found in title
        for prop_type, patterns in property_patterns:
            for pattern in patterns:
                if pattern in text_lower:
                    return prop_type

        # Default classification based on BHK
        if 'bhk' in text_lower or 'bedroom' in text_lower:
            return 'Apartment'

        return None

    def _classify_transaction_type(self, text_lower, title_lower):
        """Classify the transaction type"""
        # Transaction type patterns
        transaction_patterns = [
            ('Resale', ['resale', 'resell', 'second hand']),
            ('New Booking', ['new booking', 'new launch', 'pre launch']),
            ('Ready to Move', ['ready to move', 'ready possession']),
            ('Under Construction', ['under construction', 'upcoming']),
        ]

        # Check title first
        for trans_type, patterns in transaction_patterns:
            for pattern in patterns:
                if pattern in title_lower:
                    return trans_type

        # Check full text
        for trans_type, patterns in transaction_patterns:
            for pattern in patterns:
                if pattern in text_lower:
                    return trans_type

        return None

    def _classify_property_status(self, text_lower):
        """Classify the property status"""
        status_patterns = [
            ('Ready to Move', ['ready to move', 'ready possession', 'immediate possession']),
            ('Under Construction', ['under construction', 'upcoming', 'launching soon']),
            ('New Launch', ['new launch', 'pre launch', 'new project']),
            ('Partially Ready', ['partially ready', 'nearing completion']),
        ]

        for status, patterns in status_patterns:
            for pattern in patterns:
                if pattern in text_lower:
                    return status

        return None

    def _classify_listing_type(self, property_data, text_lower):
        """Classify whether it's individual property or project"""
        # Check if it's a project (multiple configurations, price ranges)
        if property_data.get('price_range'):
            return 'Project'

        # Check for project indicators
        project_indicators = [
            'starting from', 'price range', 'multiple options',
            'various configurations', 'different sizes'
        ]

        for indicator in project_indicators:
            if indicator in text_lower:
                return 'Project'

        # Check for individual property indicators
        individual_indicators = [
            'specific unit', 'particular flat', 'this property',
            'owner selling', 'direct owner'
        ]

        for indicator in individual_indicators:
            if indicator in text_lower:
                return 'Individual'

        # Default classification based on price format
        price = property_data.get('price', '')
        if '-' in price:  # Price range indicates project
            return 'Project'
        else:
            return 'Individual'

    def scrape_page(self, url):
        """Scrape properties from a single page"""
        self.logger.info(f"Scraping page: {url}")

        try:
            # Initialize driver if not already done
            if self.driver is None:
                self.setup_driver()

            self.driver.get(url)
            self.random_delay(3, 5)

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Enhanced dynamic content loading based on research
            self.logger.info("Loading dynamic content with progressive scrolling...")
            initial_properties = self._count_properties_on_page()
            total_properties = self._load_all_dynamic_content()

            self.logger.info(f"Dynamic loading: {initial_properties} -> {total_properties} properties")

            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # Find property cards
            property_cards = self._find_property_cards(soup)
            self.logger.info(f"Found {len(property_cards)} property cards")

            properties = []
            for i, card in enumerate(property_cards):
                property_data = self.extract_property_data(card)
                if property_data['title']:  # Only add if we extracted meaningful data
                    properties.append(property_data)
                    self.logger.debug(f"Extracted property {i+1}: {property_data['title'][:50]}...")

            self.logger.info(f"Extracted {len(properties)} properties from page")
            return properties

        except Exception as e:
            self.logger.error(f"Error scraping page {url}: {str(e)}")
            return []
    
    def _find_property_cards(self, soup):
        """Enhanced multi-method property card detection based on research findings"""
        property_cards = []
        method_stats = {'method1': 0, 'method2': 0, 'method3': 0}

        # Method 1: Property Link Traversal (Primary - Most Reliable)
        # Based on research: spid-/npxid- links are 100% accurate property identifiers
        property_links = soup.find_all('a', href=True)
        for link in property_links:
            href = link.get('href', '')
            if 'spid-' in href or 'npxid-' in href:
                # Find the container that holds complete property information
                current = link
                for level in range(25):  # Increased range based on research
                    if current is None:
                        break

                    # Enhanced property card validation
                    if self._is_property_card_enhanced(current):
                        if current not in property_cards:
                            property_cards.append(current)
                            method_stats['method1'] += 1
                        break
                    current = current.parent

        # Method 2: BHK Heading Detection (Secondary - High Accuracy)
        # Based on research: BHK headings are reliable property indicators
        bhk_headings = soup.find_all(['h1', 'h2', 'h3', 'h4'])
        for heading in bhk_headings:
            text = heading.get_text().lower()
            # Enhanced BHK detection patterns from research
            if (('bhk' in text or 'bedroom' in text) and
                ('apartment' in text or 'flat' in text or 'house' in text or 'villa' in text)):

                current = heading
                for level in range(20):
                    if current is None:
                        break
                    if self._is_property_card_enhanced(current):
                        if current not in property_cards:
                            property_cards.append(current)
                            method_stats['method2'] += 1
                        break
                    current = current.parent

        # Method 3: Price Element Container Detection (Tertiary - Fallback)
        # Based on research: Price elements are good fallback when other methods fail
        if len(property_cards) < 10:  # Only use if we haven't found enough cards
            # Look for price patterns from research
            price_patterns = [
                r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:Cr|Lakh|crore|lakh)',
                r'₹\s*\d+(?:,\d+)*\s*/sqft'
            ]

            for pattern in price_patterns:
                price_elements = soup.find_all(string=re.compile(pattern, re.IGNORECASE))
                for price_elem in price_elements:
                    current = price_elem.parent
                    for level in range(18):
                        if current is None:
                            break
                        if self._is_property_card_enhanced(current):
                            if current not in property_cards:
                                property_cards.append(current)
                                method_stats['method3'] += 1
                            break
                        current = current.parent

        # Remove duplicates while preserving order
        unique_cards = []
        seen_cards = set()
        for card in property_cards:
            card_id = id(card)
            if card_id not in seen_cards:
                unique_cards.append(card)
                seen_cards.add(card_id)

        # Log method effectiveness
        total_found = len(unique_cards)
        self.logger.info(f"Property card detection: Method1={method_stats['method1']}, "
                        f"Method2={method_stats['method2']}, Method3={method_stats['method3']}, "
                        f"Total={total_found}")

        return unique_cards

    def _count_properties_on_page(self):
        """Count current number of properties on page"""
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            property_links = soup.find_all('a', href=True)
            count = 0
            for link in property_links:
                href = link.get('href', '')
                if 'spid-' in href or 'npxid-' in href:
                    count += 1
            return count
        except:
            return 0

    def _load_all_dynamic_content(self):
        """Progressive scrolling to load all dynamic content"""
        max_scrolls = 10  # Prevent infinite scrolling
        scroll_pause = 2
        last_height = 0
        stable_count = 0

        for scroll_attempt in range(max_scrolls):
            # Get current page height
            current_height = self.driver.execute_script("return document.body.scrollHeight")

            # Count properties before scroll
            properties_before = self._count_properties_on_page()

            # Scroll down
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.random_delay(scroll_pause, scroll_pause + 1)

            # Count properties after scroll
            properties_after = self._count_properties_on_page()

            # Check if new content loaded
            new_height = self.driver.execute_script("return document.body.scrollHeight")

            # If no new properties and height didn't change, content is fully loaded
            if properties_after == properties_before and new_height == current_height:
                stable_count += 1
                if stable_count >= 2:  # Stable for 2 attempts
                    break
            else:
                stable_count = 0

            # Additional scroll patterns for better content loading
            if scroll_attempt % 3 == 0:  # Every 3rd scroll, scroll back up
                self.driver.execute_script("window.scrollTo(0, 0);")
                self.random_delay(1, 2)
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                self.random_delay(1, 2)

        # Final property count
        return self._count_properties_on_page()

    def _is_property_card_enhanced(self, element):
        """Enhanced property card validation based on research findings"""
        if element is None:
            return False

        text = element.get_text().lower()

        # Research-based validation criteria
        indicators = 0
        required_indicators = 0

        # Essential indicators (must have at least 2)
        # Price indicator (high priority from research)
        price_patterns = [
            r'₹\s*\d+(?:,\d+)*(?:\.\d+)?\s*(?:cr|lakh|crore)',
            r'₹\s*\d+(?:,\d+)*\s*/sqft'
        ]
        has_price = any(re.search(pattern, text, re.IGNORECASE) for pattern in price_patterns)
        if has_price:
            indicators += 2  # Price is highly indicative
            required_indicators += 1

        # Area information (high priority from research)
        area_patterns = [
            r'\d+(?:,\d+)*\s*sqft',
            r'\d+(?:,\d+)*\s*sq\.?\s*ft',
            r'carpet\s*area|built-?up\s*area|super\s*built-?up'
        ]
        has_area = any(re.search(pattern, text, re.IGNORECASE) for pattern in area_patterns)
        if has_area:
            indicators += 2  # Area is highly indicative
            required_indicators += 1

        # BHK information (high priority from research)
        bhk_patterns = [
            r'\d+\s*bhk',
            r'\d+\s*bedroom',
            r'\d+\s*bed'
        ]
        has_bhk = any(re.search(pattern, text, re.IGNORECASE) for pattern in bhk_patterns)
        if has_bhk:
            indicators += 2  # BHK is highly indicative
            required_indicators += 1

        # Property type keywords (medium priority)
        property_keywords = [
            'apartment', 'flat', 'villa', 'house', 'property', 'penthouse',
            'studio', 'duplex', 'bungalow', 'farmhouse'
        ]
        if any(keyword in text for keyword in property_keywords):
            indicators += 1

        # Location indicators (medium priority)
        location_keywords = ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'chennai']
        if any(keyword in text for keyword in location_keywords):
            indicators += 1

        # Transaction type indicators (from research)
        transaction_keywords = ['resale', 'new booking', 'ready to move', 'under construction']
        if any(keyword in text for keyword in transaction_keywords):
            indicators += 1

        # Contact/action indicators
        contact_keywords = ['contact', 'call', 'view number', 'enquire']
        if any(keyword in text for keyword in contact_keywords):
            indicators += 1

        # Size validation - property cards should have substantial content
        text_length = len(text.strip())
        if text_length < 50:  # Too small to be a property card
            return False
        elif text_length > 2000:  # Too large, might be entire page
            return False

        # Enhanced validation: Must have at least 2 required indicators and total score >= 4
        return required_indicators >= 2 and indicators >= 4

    def _is_property_card(self, element):
        """Legacy method - redirects to enhanced version"""
        return self._is_property_card_enhanced(element)

        # Check for location indicators
        location_keywords = ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad', 'in ']
        if any(keyword in text for keyword in location_keywords):
            indicators += 1

        # Check for contact/action buttons
        if 'contact' in text or 'view number' in text:
            indicators += 1

        # Must have at least 3 indicators to be considered a property card
        return indicators >= 3
    
    def save_to_database(self, properties):
        """Save properties to database"""
        if not properties:
            return 0, 0
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        new_count = 0
        updated_count = 0
        
        for prop in properties:
            try:
                # Check if property already exists
                cursor.execute("SELECT id FROM properties WHERE property_url = ?", (prop['property_url'],))
                existing = cursor.fetchone()
                
                if existing:
                    # Update existing property
                    update_query = """
                        UPDATE properties SET 
                        title=?, price=?, price_per_sqft=?, area=?, bedrooms=?, bathrooms=?,
                        locality=?, scraped_at=?
                        WHERE property_url=?
                    """
                    cursor.execute(update_query, (
                        prop['title'], prop['price'], prop['price_per_sqft'], prop['area'],
                        prop['bedrooms'], prop['bathrooms'], prop['locality'],
                        prop['scraped_at'], prop['property_url']
                    ))
                    updated_count += 1
                else:
                    # Insert new property with core fields (safe insertion)
                    insert_query = """
                        INSERT INTO properties (
                            title, price, price_per_sqft, area, bedrooms, bathrooms,
                            locality, city, property_url, property_type, construction_status,
                            rera_id, amenities, data_quality_score, scraped_at, source,
                            price_range, min_price, max_price, transaction_type, listing_type,
                            extraction_method, container_class
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    cursor.execute(insert_query, (
                        prop.get('title', ''), prop.get('price', ''), prop.get('price_per_sqft', ''),
                        prop.get('area', ''), prop.get('bedrooms', ''), prop.get('bathrooms', ''),
                        prop.get('locality', ''), prop.get('city', ''), prop.get('property_url', ''),
                        prop.get('property_type', ''), prop.get('construction_status', ''),
                        prop.get('rera_id', ''), prop.get('amenities', ''), prop.get('data_quality_score', 0),
                        prop.get('scraped_at', ''), prop.get('source', '99acres'),
                        prop.get('price_range', ''), prop.get('min_price', ''), prop.get('max_price', ''),
                        prop.get('transaction_type', ''), prop.get('listing_type', ''),
                        prop.get('extraction_method', 'enhanced'), prop.get('container_class', '')
                    ))
                    new_count += 1
                    
            except Exception as e:
                self.logger.error(f"Error saving property to database: {str(e)}")
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"Saved to database: {new_count} new, {updated_count} updated")
        return new_count, updated_count
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            self.logger.info("WebDriver closed")


if __name__ == "__main__":
    # Test the scraper
    scraper = Integrated99acresScraper(headless=False)
    
    try:
        scraper.setup_driver()
        
        # Test scraping Mumbai properties
        mumbai_url = scraper.generate_city_url('mumbai', 'sale')
        properties = scraper.scrape_page(mumbai_url)
        
        print(f"Scraped {len(properties)} properties")
        for prop in properties[:3]:  # Show first 3
            print(f"- {prop['title']} | {prop['price']} | {prop['area']} sqft")
        
        # Save to database
        new_count, updated_count = scraper.save_to_database(properties)
        print(f"Database: {new_count} new, {updated_count} updated")
        
    finally:
        scraper.close()
