#!/usr/bin/env python3
"""
Fix URLs and create browsable links for manual analysis
"""

import json
import re

def fix_urls():
    """Fix malformed URLs in the sample"""
    
    # Load the sample
    with open('manual_analysis_sample.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("🔧 Fixing URLs for Manual Analysis")
    print("=" * 50)
    
    fixed_urls = []
    
    for i, prop in enumerate(data['sample_urls'], 1):
        original_url = prop['url']
        
        # Fix URL format
        if original_url.startswith('httpswww.99acres.com'):
            fixed_url = original_url.replace('httpswww.99acres.com', 'https://www.99acres.com/')
        elif original_url.startswith('www.99acres.com'):
            fixed_url = f"https://{original_url}"
        else:
            fixed_url = f"https://www.99acres.com/{original_url}"
        
        # Update the property data
        prop['url'] = fixed_url
        fixed_urls.append(prop)
        
        print(f"{i:2d}. {prop['title'][:40]}...")
        print(f"    Category: {prop['category']}")
        print(f"    Fixed URL: {fixed_url}")
        print()
    
    # Save fixed URLs
    data['sample_urls'] = fixed_urls
    
    with open('manual_analysis_sample_fixed.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Fixed {len(fixed_urls)} URLs")
    print(f"💾 Saved to: manual_analysis_sample_fixed.json")
    
    return fixed_urls

def create_browsing_list():
    """Create a prioritized list for manual browsing"""
    
    with open('manual_analysis_sample_fixed.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Prioritize diverse properties for analysis
    priority_properties = []
    
    # Group by categories for diverse analysis
    categories = {}
    for prop in data['sample_urls']:
        category = prop['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(prop)
    
    print(f"\n📊 Property Categories Found:")
    for category, props in categories.items():
        print(f"   {category}: {len(props)} properties")
    
    # Select 1-2 properties from each category for focused analysis
    browsing_list = []
    for category, props in categories.items():
        # Take first 2 from each category
        for prop in props[:2]:
            browsing_list.append(prop)
    
    # Create browsing instructions
    instructions = {
        "manual_browsing_instructions": {
            "total_properties_to_analyze": len(browsing_list),
            "analysis_focus": [
                "Page structure and layout",
                "CSS selectors for key data fields",
                "JavaScript-loaded content",
                "Price and specification extraction",
                "Image and gallery structure",
                "Contact information layout",
                "Amenities and features display",
                "Location and map integration",
                "Differences between Sale vs Rent",
                "Mobile vs Desktop layout differences"
            ],
            "properties_to_browse": browsing_list
        }
    }
    
    with open('browsing_instructions.json', 'w', encoding='utf-8') as f:
        json.dump(instructions, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎯 Created browsing list of {len(browsing_list)} priority properties")
    print(f"💾 Saved to: browsing_instructions.json")
    
    # Print the URLs to browse
    print(f"\n📋 PRIORITY URLS FOR MANUAL ANALYSIS:")
    print("=" * 60)
    
    for i, prop in enumerate(browsing_list, 1):
        print(f"\n{i:2d}. {prop['title']}")
        print(f"    Category: {prop['category']}")
        print(f"    Price: {prop['price']}")
        print(f"    URL: {prop['url']}")
    
    return browsing_list

if __name__ == "__main__":
    print("🔧 99acres URL Fixer and Browser Setup")
    print("=" * 50)
    
    # Fix URLs
    fixed_urls = fix_urls()
    
    # Create browsing list
    browsing_list = create_browsing_list()
    
    print(f"\n✅ Setup complete!")
    print(f"📄 Files created:")
    print(f"   - manual_analysis_sample_fixed.json (all fixed URLs)")
    print(f"   - browsing_instructions.json (priority URLs)")
    print(f"\n🎯 Next: Manually browse the priority URLs and document findings")
