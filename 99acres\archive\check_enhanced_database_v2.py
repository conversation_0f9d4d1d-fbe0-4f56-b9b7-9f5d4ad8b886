#!/usr/bin/env python3
"""
Check the enhanced database with all new fields from deep research
"""

import sqlite3
import pandas as pd

def check_enhanced_database():
    """Check the enhanced database with all new fields"""
    conn = sqlite3.connect('data/99acres_properties.db')
    
    # Get total count
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM properties")
    total_count = cursor.fetchone()[0]
    print(f"📊 Total properties in enhanced database: {total_count}")
    
    # Check database schema
    cursor.execute("PRAGMA table_info(properties)")
    columns = cursor.fetchall()
    print(f"🗄️ Database schema: {len(columns)} columns")
    
    # Show enhanced fields sample
    print(f"\n📋 Enhanced Fields Sample:")
    df = pd.read_sql_query("""
        SELECT title, price, price_range, min_price, max_price, area, 
               property_type, transaction_type, listing_type, 
               data_quality_score, extraction_method, container_class,
               city, locality
        FROM properties 
        WHERE extraction_method = 'enhanced'
        ORDER BY id DESC 
        LIMIT 5
    """, conn)
    
    for i, row in df.iterrows():
        print(f"\n{i+1}. {row['title']}")
        print(f"   Price: {row['price']}")
        if row['price_range']:
            print(f"   Price Range: {row['price_range']}")
        if row['min_price']:
            print(f"   Min Price: {row['min_price']}")
        if row['max_price']:
            print(f"   Max Price: {row['max_price']}")
        print(f"   Area: {row['area']}")
        print(f"   Type: {row['property_type'] or 'N/A'}")
        print(f"   Transaction: {row['transaction_type'] or 'N/A'}")
        print(f"   Listing: {row['listing_type'] or 'N/A'}")
        print(f"   Quality Score: {row['data_quality_score']}%")
        print(f"   Extraction: {row['extraction_method']}")
        print(f"   Container: {row['container_class'] or 'N/A'}")
        print(f"   Location: {row['locality']}, {row['city']}")
    
    # Enhanced field analysis
    print(f"\n📈 Enhanced Fields Analysis:")
    
    # Core fields performance
    core_fields = ['title', 'price', 'area', 'bedrooms', 'locality', 'property_url']
    print(f"\n🔥 Core Fields Performance:")
    for field in core_fields:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Enhanced price fields
    price_fields = ['price_range', 'min_price', 'max_price', 'price_unit']
    print(f"\n💰 Enhanced Price Fields:")
    for field in price_fields:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Classification fields
    classification_fields = ['property_type', 'transaction_type', 'listing_type']
    print(f"\n🏢 Classification Fields:")
    for field in classification_fields:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != ''")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Quality and validation fields
    quality_fields = ['data_quality_score', 'extraction_method', 'container_class']
    print(f"\n🎯 Quality & Validation Fields:")
    for field in quality_fields:
        cursor.execute(f"SELECT COUNT(*) FROM properties WHERE {field} IS NOT NULL AND {field} != '' AND {field} != '0'")
        filled_count = cursor.fetchone()[0]
        percentage = (filled_count / total_count * 100) if total_count > 0 else 0
        print(f"   {field}: {filled_count}/{total_count} ({percentage:.1f}%)")
    
    # Price analysis
    print(f"\n💰 Price Analysis:")
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price IS NOT NULL AND price != '' AND price NOT LIKE '%-%'")
    single_price_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM properties WHERE price_range IS NOT NULL AND price_range != ''")
    range_price_count = cursor.fetchone()[0]
    
    print(f"   Single prices: {single_price_count} ({single_price_count/total_count*100:.1f}%)")
    print(f"   Price ranges: {range_price_count} ({range_price_count/total_count*100:.1f}%)")
    
    # Extraction method analysis
    print(f"\n🔧 Extraction Method Analysis:")
    cursor.execute("""
        SELECT extraction_method, COUNT(*) as count
        FROM properties 
        WHERE extraction_method IS NOT NULL AND extraction_method != ''
        GROUP BY extraction_method
        ORDER BY count DESC
    """)
    
    extraction_methods = cursor.fetchall()
    for method, count in extraction_methods:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {method}: {count} properties ({percentage:.1f}%)")
    
    # Container class analysis
    print(f"\n📦 Container Class Analysis:")
    cursor.execute("""
        SELECT container_class, COUNT(*) as count
        FROM properties 
        WHERE container_class IS NOT NULL AND container_class != ''
        GROUP BY container_class
        ORDER BY count DESC
    """)
    
    container_classes = cursor.fetchall()
    for container_class, count in container_classes:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {container_class}: {count} properties ({percentage:.1f}%)")
    
    # Data quality distribution
    print(f"\n📊 Data Quality Distribution:")
    cursor.execute("""
        SELECT 
            CASE 
                WHEN data_quality_score >= 80 THEN 'Excellent (80-100%)'
                WHEN data_quality_score >= 60 THEN 'Good (60-79%)'
                WHEN data_quality_score >= 40 THEN 'Fair (40-59%)'
                WHEN data_quality_score > 0 THEN 'Poor (1-39%)'
                ELSE 'No Score (0%)'
            END as quality_range,
            COUNT(*) as count
        FROM properties 
        GROUP BY quality_range
        ORDER BY MIN(data_quality_score) DESC
    """)
    
    quality_dist = cursor.fetchall()
    for quality_range, count in quality_dist:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"   {quality_range}: {count} properties ({percentage:.1f}%)")
    
    # Show database capabilities
    print(f"\n🚀 Enhanced Database Capabilities:")
    print(f"   ✅ 100 columns supporting all research findings")
    print(f"   ✅ Enhanced price handling (ranges, min/max)")
    print(f"   ✅ Property classification (type, transaction, listing)")
    print(f"   ✅ Quality scoring and validation tracking")
    print(f"   ✅ Extraction method tracking")
    print(f"   ✅ Container class identification")
    print(f"   ✅ Performance indexes for fast queries")
    print(f"   ✅ Comprehensive field coverage from deep research")
    
    conn.close()

if __name__ == "__main__":
    check_enhanced_database()
