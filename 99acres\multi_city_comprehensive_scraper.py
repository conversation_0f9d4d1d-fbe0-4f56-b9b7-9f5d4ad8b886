#!/usr/bin/env python3
"""
Multi-City Comprehensive 99acres Scraper
Scrapes properties from multiple cities to create a diverse database for field analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integrated_99acres_scraper import Integrated99acresScraper
import time
from datetime import datetime

class MultiCityComprehensiveScraper:
    def __init__(self):
        self.scraper = Integrated99acresScraper(headless=False)
        self.total_scraped = 0
        self.cities_completed = 0
        
    def scrape_multiple_cities(self):
        """Scrape properties from multiple cities for comprehensive analysis"""
        
        # Define cities and their configurations
        cities_config = [
            {
                'city': 'mumbai',
                'pages_to_scrape': 15,  # ~300 properties
                'property_types': ['sale', 'rent']
            },
            {
                'city': 'delhi',
                'pages_to_scrape': 12,  # ~240 properties
                'property_types': ['sale', 'rent']
            },
            {
                'city': 'bangalore',
                'pages_to_scrape': 10,  # ~200 properties
                'property_types': ['sale', 'rent']
            },
            {
                'city': 'pune',
                'pages_to_scrape': 8,   # ~160 properties
                'property_types': ['sale', 'rent']
            },
            {
                'city': 'hyderabad',
                'pages_to_scrape': 6,   # ~120 properties
                'property_types': ['sale']
            },
            {
                'city': 'chennai',
                'pages_to_scrape': 6,   # ~120 properties
                'property_types': ['sale']
            },
            {
                'city': 'kolkata',
                'pages_to_scrape': 5,   # ~100 properties
                'property_types': ['sale']
            },
            {
                'city': 'ahmedabad',
                'pages_to_scrape': 4,   # ~80 properties
                'property_types': ['sale']
            }
        ]
        
        print("🚀 Starting Multi-City Comprehensive Scraping")
        print("=" * 60)
        print(f"🎯 Target: {len(cities_config)} cities")
        print(f"📊 Estimated total properties: ~1,400+")
        print("=" * 60)
        
        try:
            # Setup scraper
            self.scraper.setup_driver()
            
            # Scrape each city
            for city_config in cities_config:
                city = city_config['city']
                pages_to_scrape = city_config['pages_to_scrape']
                property_types = city_config['property_types']
                
                print(f"\n🏙️ SCRAPING {city.upper()}")
                print(f"   📋 Pages per type: {pages_to_scrape}")
                print(f"   🏠 Property types: {', '.join(property_types)}")
                print("-" * 40)
                
                city_total = 0
                
                for prop_type in property_types:
                    print(f"\n   📍 {city} - {prop_type} properties")
                    
                    try:
                        # Generate URL for this city and property type
                        city_url = self.scraper.generate_city_url(city, prop_type)
                        print(f"   🔗 URL: {city_url}")
                        
                        # Scrape multiple pages
                        for page in range(1, pages_to_scrape + 1):
                            print(f"      📄 Page {page}/{pages_to_scrape}")
                            
                            try:
                                # Modify URL for pagination
                                page_url = f"{city_url}&page={page}" if page > 1 else city_url
                                
                                # Scrape this page
                                properties = self.scraper.scrape_page(page_url)
                                
                                if properties:
                                    # Save to database
                                    new_count, updated_count = self.scraper.save_to_database(properties)
                                    
                                    page_total = len(properties)
                                    city_total += page_total
                                    self.total_scraped += page_total
                                    
                                    print(f"         ✅ {page_total} properties | DB: +{new_count} new, ~{updated_count} updated")
                                    
                                    # Respectful delay between pages
                                    time.sleep(3)
                                else:
                                    print(f"         ⚠️ No properties found on page {page}")
                                    break  # No more properties, stop pagination
                                    
                            except Exception as e:
                                print(f"         ❌ Error on page {page}: {str(e)}")
                                continue
                        
                        # Delay between property types
                        time.sleep(5)
                        
                    except Exception as e:
                        print(f"   ❌ Error scraping {city} {prop_type}: {str(e)}")
                        continue
                
                self.cities_completed += 1
                print(f"\n   ✅ {city.upper()} COMPLETE: {city_total} properties")
                print(f"   📊 Total scraped so far: {self.total_scraped} properties")
                print(f"   🏙️ Cities completed: {self.cities_completed}/{len(cities_config)}")
                
                # Longer delay between cities
                time.sleep(10)
            
            # Final summary
            print("\n" + "=" * 60)
            print("🎉 MULTI-CITY SCRAPING COMPLETE!")
            print("=" * 60)
            print(f"🏙️ Cities scraped: {self.cities_completed}")
            print(f"📊 Total properties: {self.total_scraped}")
            print(f"⏱️ Duration: {datetime.now() - self.scraper.session_start_time}")
            print("💾 All data saved to database")
            
            # Generate summary report
            self.generate_scraping_summary()
            
        except Exception as e:
            print(f"❌ Multi-city scraping failed: {str(e)}")
        finally:
            if self.scraper.driver:
                self.scraper.driver.quit()
    
    def generate_scraping_summary(self):
        """Generate a summary of the scraping session"""
        try:
            # Connect to database and get summary
            import sqlite3
            conn = sqlite3.connect(self.scraper.database_path)
            cursor = conn.cursor()
            
            # Get city distribution
            cursor.execute("SELECT city, COUNT(*) FROM properties GROUP BY city ORDER BY COUNT(*) DESC")
            city_distribution = cursor.fetchall()
            
            # Get property type distribution
            cursor.execute("SELECT property_type, COUNT(*) FROM properties GROUP BY property_type ORDER BY COUNT(*) DESC")
            type_distribution = cursor.fetchall()
            
            # Get bedroom distribution
            cursor.execute("SELECT bedrooms, COUNT(*) FROM properties GROUP BY bedrooms ORDER BY COUNT(*) DESC")
            bedroom_distribution = cursor.fetchall()
            
            # Total count
            cursor.execute("SELECT COUNT(*) FROM properties")
            total_count = cursor.fetchone()[0]
            
            print(f"\n📊 DATABASE SUMMARY:")
            print(f"   Total properties: {total_count}")
            
            print(f"\n🏙️ City Distribution:")
            for city, count in city_distribution:
                print(f"   {city}: {count} properties")
            
            print(f"\n🏠 Property Type Distribution:")
            for prop_type, count in type_distribution:
                print(f"   {prop_type}: {count} properties")
            
            print(f"\n🛏️ Bedroom Distribution:")
            for bedrooms, count in bedroom_distribution:
                print(f"   {bedrooms} BHK: {count} properties")
            
            conn.close()
            
            # Save summary to file
            summary_data = {
                'scraping_timestamp': datetime.now().isoformat(),
                'total_properties': total_count,
                'cities_scraped': self.cities_completed,
                'total_scraped_this_session': self.total_scraped,
                'city_distribution': dict(city_distribution),
                'property_type_distribution': dict(type_distribution),
                'bedroom_distribution': dict(bedroom_distribution)
            }
            
            import json
            with open('multi_city_scraping_summary.json', 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Summary saved to: multi_city_scraping_summary.json")
            
        except Exception as e:
            print(f"❌ Error generating summary: {str(e)}")

def main():
    """Main function to run multi-city scraping"""
    print("🔍 99acres Multi-City Comprehensive Scraper")
    print("=" * 50)
    
    scraper = MultiCityComprehensiveScraper()
    scraper.scrape_multiple_cities()
    
    print(f"\n✅ Multi-city scraping session complete!")
    print("📄 Ready for comprehensive field analysis with diverse data!")

if __name__ == "__main__":
    main()
